# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration

# Application environment. Available options [dev, test, behat, prod]
APP_ENV=dev

# Debug mode. Available options [0, 1]
#APP_DEBUG=1

# Hostname
APP_HOSTNAME=u2.localhost

# Update this setting for better security (Must be 32 chars or longer see https://universalunits.atlassian.net/browse/UU-6324)
APP_SECRET=ChangeThisTokenToImproveSecurity

# Used as a seed to generate the cache namespace and for prefixing messages in redis
#APP_NAME=

# The location to store binary data when using local data storage. See DATA_STORE setting
#APP_DATA_DIR=/app/api/var/data

# AWS configuration for S3 data storage. See DATA_STORE setting
#AWS_REGION=
#AWS_S3_BUCKET=
#AWS_S3_BUCKET_PREFIX=
# AWS environment credentials that allow access to data storage S3 bucket.
# See https://docs.aws.amazon.com/aws-sdk-php/v2/guide/credentials.html for information about providing AWS credentials
#AWS_ACCESS_KEY_ID=
#AWS_SECRET_ACCESS_KEY=

# Authorize composer with a github token to avoid rate limits
#COMPOSER_AUTH={"github-oauth":{"github.com":"your-oauth-token"}}

# Database connection
DATABASE_URL=mysql://u2:u2@db/u2
DATABASE_VERSION=8.0.36

# Binary data storage method. Available options [local, s3]
DATA_STORE=local

# Paths to SSL certificates for HTTPS
DOMAIN_CERT=./certs/u2.localhost.crt
DOMAIN_CERT_KEY=./certs/u2.localhost.key

# This setting enables IDE support in symfony.
# See https://symfony.com/doc/current/reference/configuration/framework.html#ide for information
# E.g. for phpstorm you can use "phpstorm://open?file=%f&line=%l&/app/>/path/to/project"
#IDE_URL=

# A base64 encoded private key
#JWT_SECRET_KEY=
# A base64 encoded public key
#JWT_PUBLIC_KEY=
JWT_PASSPHRASE=ChangeThisTokenToImproveSecurity

# Email configuration
MAILER_URL=smtp://mailer:1025
MAILER_FROM=<EMAIL>

# Static tenant configuration with all available tenants.
TENANTS_CONFIG='[{"name":"dev","type":"demo","modules":{"apm":true,"contract_management":true,"igt":true,"tam":true,"tcm":true,"tpm":true,"dtm":true}},{"name":"test","modules":{"apm":true,"contract_management":true,"igt":true,"tam":true,"tcm":true,"tpm":true,"dtm":true}}]'

# Redis connection
REDIS_URL=redis://redis

# Run the storybook container
RUN_STORYBOOK=false

# Proxy configuration. Currently unused
TRUSTED_PROXIES=127.0.0.1,*********
TRUSTED_HOSTS=localhost,u2.localhost

# Enables XDEBUG extension in PHP. Available options https://xdebug.org/docs/all_settings#mode
XDEBUG_MODE=off
XDEBUG_CONFIG="xdebug.start_with_request=yes"
