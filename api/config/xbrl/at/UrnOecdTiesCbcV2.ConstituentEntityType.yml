U2\Xbrl\At\Schema\UrnOecdTiesCbcV2\ConstituentEntityType:
    properties:
        constEntity:
            expose: true
            access_type: public_method
            serialized_name: ConstEntity
            xml_element:
                namespace: 'urn:oecd:ties:cbc:v2'
                cdata: false
            accessor:
                getter: getConstEntity
                setter: setConstEntity
            type: U2\Xbrl\At\Schema\UrnOecdTiesCbcV2\OrganisationPartyType
        role:
            expose: true
            access_type: public_method
            serialized_name: Role
            xml_element:
                namespace: 'urn:oecd:ties:cbc:v2'
                cdata: false
            accessor:
                getter: getRole
                setter: setRole
            type: U2\Serializer\Jms\Types\XbrlString
        incorpCountryCode:
            expose: true
            access_type: public_method
            serialized_name: IncorpCountryCode
            xml_element:
                namespace: 'urn:oecd:ties:cbc:v2'
                cdata: false
            accessor:
                getter: getIncorpCountryCode
                setter: setIncorpCountryCode
            type: U2\Serializer\Jms\Types\XbrlString
        bizActivities:
            expose: true
            access_type: public_method
            serialized_name: BizActivities
            xml_element:
                namespace: 'urn:oecd:ties:cbc:v2'
                cdata: false
            accessor:
                getter: getBizActivities
                setter: setBizActivities
            xml_list:
                inline: true
                entry_name: BizActivities
                namespace: 'urn:oecd:ties:cbc:v2'
            type: array<string>
        otherEntityInfo:
            expose: true
            access_type: public_method
            serialized_name: OtherEntityInfo
            xml_element:
                namespace: 'urn:oecd:ties:cbc:v2'
                cdata: false
            accessor:
                getter: getOtherEntityInfo
                setter: setOtherEntityInfo
            type: U2\Serializer\Jms\Types\XbrlString
