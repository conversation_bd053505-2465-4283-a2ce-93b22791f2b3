<?php

declare(strict_types=1);
namespace U2\Api\Processor;

use ApiPlatform\Doctrine\Common\State\PersistProcessor;
use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use U2\Entity\Comment;
use U2\User\CurrentUserProvider;

/**
 * @implements  ProcessorInterface<Comment,Comment>
 */
readonly class CommentProcessor implements ProcessorInterface
{
    public function __construct(
        private CurrentUserProvider $currentUserProvider,
        private PersistProcessor $persistProcessor,
    ) {
    }

    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): Comment
    {
        $data->getTask()?->setUpdatedAt(new \DateTime());
        $data->getTask()?->setUpdatedBy($this->currentUserProvider->get());

        /** @var Comment $comment */
        $comment = $this->persistProcessor->process($data, $operation, $uriVariables, $context);

        return $comment;
    }
}
