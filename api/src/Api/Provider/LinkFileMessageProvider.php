<?php

declare(strict_types=1);
namespace U2\Api\Provider;

use ApiPlatform\Metadata\IriConverterInterface;
use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use U2\Entity\CountryByCountryReportSection;
use U2\Entity\LocalFileSection;
use U2\Entity\MasterFileSection;
use U2\Entity\Task\Task;
use U2\Entity\Unit;
use U2\Messenger\File\LinkFileMessage;

/**
 * @implements ProviderInterface<LinkFileMessage>
 */
final readonly class LinkFileMessageProvider implements ProviderInterface
{
    public function __construct(
        private IriConverterInterface $iriConverter,
    ) {
    }

    /**
     * @param array<string, mixed> $uriVariables
     * @param array<string, mixed> $context
     */
    public function provide(Operation $operation, array $uriVariables = [], array $context = []): LinkFileMessage
    {
        \assert(\array_key_exists('request_uri', $context));
        /** @var string $requestUri */
        $requestUri = $context['request_uri'];

        $fileAttachableIri = substr($requestUri, 0, -12);

        /** @var Task|MasterFileSection|LocalFileSection|CountryByCountryReportSection|Unit $fileAttachableResource */
        $fileAttachableResource = $this->iriConverter->getResourceFromIri($fileAttachableIri);

        return new LinkFileMessage(
            $fileAttachableResource,
        );
    }
}
