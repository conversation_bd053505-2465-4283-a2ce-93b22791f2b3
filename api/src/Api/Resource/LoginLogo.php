<?php

declare(strict_types=1);
namespace U2\Api\Resource;

use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\Post;
use ApiPlatform\OpenApi\Model\Operation;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Api\Processor\DynamicAssetProcessor;
use U2\Api\Provider\DynamicAssetAsBinaryFileProvider;
use U2\Dto\DynamicAsset;
use U2\Security\UserRoles;
use U2\SystemSettings\SystemImagesHandler;

#[ApiResource(
    operations: [
        new Get(
            uriTemplate: '/dynamic-assets/login-logo',
            formats: ['binary' => 'application/octet-stream'],
            openapi: new Operation(
                responses: [
                    Response::HTTP_OK => [
                        'description' => 'Downloads the login logo',
                        'content' => [
                            'application/octet-stream' => ['schema' => ['type' => 'string', 'format' => 'binary']],
                        ],
                    ],
                ],
                description: 'Download the login logo',
            ),
            provider: DynamicAssetAsBinaryFileProvider::class
        ),
        new Post(
            uriTemplate: '/dynamic-assets/login-logo',
            inputFormats: ['multipart' => ['multipart/form-data']],
            status: Response::HTTP_NO_CONTENT,
            security: 'is_granted("' . UserRoles::Admin->value . '")',
            output: false,
            processor: DynamicAssetProcessor::class,
        ),
    ],
)]
class LoginLogo extends DynamicAsset
{
    public function __construct(
        #[Assert\Image(
            maxSize: 500000,
            mimeTypes: [
                'image/svg',
                'image/svg+xml',
                'image/png',
                'image/apng',
                'image/vnd.mozilla.apng',
                'image/jpeg',
                'image/pjpeg',
            ]
        )]
        public readonly ?UploadedFile $uploadedFile = null,
    ) {
        parent::__construct(
            SystemImagesHandler::APPLICATION_LOGIN_LOGO,
            '/public/img/u2-login-logo.svg',
        );
    }
}
