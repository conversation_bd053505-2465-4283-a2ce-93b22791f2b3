<?php

declare(strict_types=1);
namespace U2\Behat\Context\Setup;

use Behat\MinkExtension\Context\RawMinkContext;
use Doctrine\ORM\EntityManagerInterface;
use U2\Behat\Service\Record\Collection\Bag;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\User;

class UserContext extends RawMinkContext
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly Bag $recordBag,
    ) {
    }

    /**
     * @Given there is a user named :username
     */
    public function thereIsAUserWithUsername(string $username): User
    {
        $data = ['username' => $username];
        $newUser = UserFactory::getObject($data);
        $this->entityManager->persist($newUser);
        $this->entityManager->flush();

        $this->recordBag->getCollection(User::class)->attach($newUser, $data);

        return $newUser;
    }
}
