<?php

declare(strict_types=1);
namespace U2\Behat\Service\Table;

class NodeCollection
{
    /**
     * @var Node[]
     */
    private array $nodes = [];

    public function getNodes(): array
    {
        return array_reduce(
            $this->nodes,
            static fn ($previous, $line): array => array_merge($previous, $line),
            []
        );
    }

    public function addNode(Node $node, $line, $column): self
    {
        $this->nodes[$line][$column] = $node;

        return $this;
    }

    /**
     * @return array<Node>|Node|null
     */
    public function atPosition($line = null, $column = null): array|Node|null
    {
        switch (true) {
            case null === $line && null === $column:
                return $this->getNodes();
            case null !== $line && null === $column:
                return \array_key_exists($line, $this->nodes)
                    ? $this->nodes[$line]
                    : [];
            case null === $line && null !== $column:
                $nodes = [];
                foreach ($this->nodes as $i => $nodes) {
                    if (property_exists($nodes, $column)) {
                        $nodes[$i] = $nodes[$column];
                    }
                }

                return $nodes;
            case null !== $line && null !== $column:
                $nodes = $this->atPosition($line);

                return $nodes[$column] ?? null;
        }

        return null;
    }

    public function search($content): array
    {
        return array_filter(
            $this->getNodes(),
            static fn (Node $node): bool => $node->getContent() === $content
        );
    }
}
