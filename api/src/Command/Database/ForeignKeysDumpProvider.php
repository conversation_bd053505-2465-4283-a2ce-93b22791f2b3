<?php

declare(strict_types=1);
namespace U2\Command\Database;

class ForeignKeysDumpProvider
{
    /**
     * @return non-empty-array<literal-string&non-falsy-string, array{TABLE_NAME: literal-string&non-falsy-string, REFERENCED_TABLE_NAME: literal-string&non-falsy-string}>
     */
    public static function get(): array
    {
        return [
            'FK_168D84FBF396750' => [
                'TABLE_NAME' => 'dtm_diff_item_value',
                'REFERENCED_TABLE_NAME' => 'dtm_item_value',
            ],
            'FK_18A1EEDA896DBBDE' => [
                'TABLE_NAME' => 'document_template',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_18A1EEDAB03A8386' => [
                'TABLE_NAME' => 'document_template',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_18A1EEDAC05070BB' => [
                'TABLE_NAME' => 'document_template',
                'REFERENCED_TABLE_NAME' => 'collective_permission',
            ],
            'FK_192A817277D80F90' => [
                'TABLE_NAME' => 'unit_audit',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_192A8172B64C06E5' => [
                'TABLE_NAME' => 'unit_audit',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_1BACBA4A84042C99' => [
                'TABLE_NAME' => 'tcm_tax_authority_audit_objection',
                'REFERENCED_TABLE_NAME' => 'tax_type',
            ],
            'FK_1BACBA4A896DBBDE' => [
                'TABLE_NAME' => 'tcm_tax_authority_audit_objection',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_1BACBA4A8DB60186' => [
                'TABLE_NAME' => 'tcm_tax_authority_audit_objection',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_1BACBA4AB03A8386' => [
                'TABLE_NAME' => 'tcm_tax_authority_audit_objection',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_1BACBA4AF8BD700D' => [
                'TABLE_NAME' => 'tcm_tax_authority_audit_objection',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_1BC224067E3C61F9' => [
                'TABLE_NAME' => 'saved_filter',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_1BC22406896DBBDE' => [
                'TABLE_NAME' => 'saved_filter',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_1BC22406B03A8386' => [
                'TABLE_NAME' => 'saved_filter',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_1C9E6C423F571310' => [
                'TABLE_NAME' => 'tam_transfer_pricing',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_1C9E6C42896DBBDE' => [
                'TABLE_NAME' => 'tam_transfer_pricing',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_1C9E6C428DB60186' => [
                'TABLE_NAME' => 'tam_transfer_pricing',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_1C9E6C42B03A8386' => [
                'TABLE_NAME' => 'tam_transfer_pricing',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_1C9E6C42B3E6B071' => [
                'TABLE_NAME' => 'tam_transfer_pricing',
                'REFERENCED_TABLE_NAME' => 'transaction_type',
            ],
            'FK_1C9E6C42B7194DDD' => [
                'TABLE_NAME' => 'tam_transfer_pricing',
                'REFERENCED_TABLE_NAME' => 'pricing_method',
            ],
            'FK_1C9E6C42EC8B7ADE' => [
                'TABLE_NAME' => 'tam_transfer_pricing',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_1C9E6C42F8BD700D' => [
                'TABLE_NAME' => 'tam_transfer_pricing',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_1E883F9184042C99' => [
                'TABLE_NAME' => 'tam_tax_assessment_status',
                'REFERENCED_TABLE_NAME' => 'tax_type',
            ],
            'FK_1E883F91896DBBDE' => [
                'TABLE_NAME' => 'tam_tax_assessment_status',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_1E883F918DB60186' => [
                'TABLE_NAME' => 'tam_tax_assessment_status',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_1E883F91B03A8386' => [
                'TABLE_NAME' => 'tam_tax_assessment_status',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_1E883F91EC8B7ADE' => [
                'TABLE_NAME' => 'tam_tax_assessment_status',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_1E883F91F8BD700D' => [
                'TABLE_NAME' => 'tam_tax_assessment_status',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_1F376E6584042C99' => [
                'TABLE_NAME' => 'tam_tax_credit',
                'REFERENCED_TABLE_NAME' => 'tax_type',
            ],
            'FK_1F376E65896DBBDE' => [
                'TABLE_NAME' => 'tam_tax_credit',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_1F376E658DB60186' => [
                'TABLE_NAME' => 'tam_tax_credit',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_1F376E65B03A8386' => [
                'TABLE_NAME' => 'tam_tax_credit',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_1F376E65B649AB19' => [
                'TABLE_NAME' => 'tam_tax_credit',
                'REFERENCED_TABLE_NAME' => 'tam_loss_restriction',
            ],
            'FK_1F376E65EC8B7ADE' => [
                'TABLE_NAME' => 'tam_tax_credit',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_1F376E65F8BD700D' => [
                'TABLE_NAME' => 'tam_tax_credit',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_1F376E65FFC8EBBC' => [
                'TABLE_NAME' => 'tam_tax_credit',
                'REFERENCED_TABLE_NAME' => 'tam_tax_credit_type',
            ],
            'FK_1FA93204877338D2' => [
                'TABLE_NAME' => 'document_template_section',
                'REFERENCED_TABLE_NAME' => 'document_template',
            ],
            'FK_1FA93204896DBBDE' => [
                'TABLE_NAME' => 'document_template_section',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_1FA93204B03A8386' => [
                'TABLE_NAME' => 'document_template_section',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_200490F68DB60186' => [
                'TABLE_NAME' => 'task_to_watcher',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_200490F6A76ED395' => [
                'TABLE_NAME' => 'task_to_watcher',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_20A81F84896DBBDE' => [
                'TABLE_NAME' => 'dtm_layout_collection',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_20A81F849C3E4F87' => [
                'TABLE_NAME' => 'dtm_layout_collection',
                'REFERENCED_TABLE_NAME' => 'collective_permission',
            ],
            'FK_20A81F84B03A8386' => [
                'TABLE_NAME' => 'dtm_layout_collection',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_231799CD2F8B0EB2' => [
                'TABLE_NAME' => 'authorization_profile_authorization',
                'REFERENCED_TABLE_NAME' => 'authorization',
            ],
            'FK_231799CD9525ACFC' => [
                'TABLE_NAME' => 'authorization_profile_authorization',
                'REFERENCED_TABLE_NAME' => 'authorization_profile',
            ],
            'FK_272FB2DD16CFE2E6' => [
                'TABLE_NAME' => 'tam_tax_consulting_fee',
                'REFERENCED_TABLE_NAME' => 'tam_advice_type',
            ],
            'FK_272FB2DD896DBBDE' => [
                'TABLE_NAME' => 'tam_tax_consulting_fee',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_272FB2DD8DB60186' => [
                'TABLE_NAME' => 'tam_tax_consulting_fee',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_272FB2DD8F3A631B' => [
                'TABLE_NAME' => 'tam_tax_consulting_fee',
                'REFERENCED_TABLE_NAME' => 'auditor',
            ],
            'FK_272FB2DDB03A8386' => [
                'TABLE_NAME' => 'tam_tax_consulting_fee',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_272FB2DDBD445DAB' => [
                'TABLE_NAME' => 'tam_tax_consulting_fee',
                'REFERENCED_TABLE_NAME' => 'auditor',
            ],
            'FK_272FB2DDEC8B7ADE' => [
                'TABLE_NAME' => 'tam_tax_consulting_fee',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_272FB2DDF8BD700D' => [
                'TABLE_NAME' => 'tam_tax_consulting_fee',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_286579711ED93D47' => [
                'TABLE_NAME' => 'user_user_group',
                'REFERENCED_TABLE_NAME' => 'user_group',
            ],
            'FK_28657971A76ED395' => [
                'TABLE_NAME' => 'user_user_group',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_2A5B52771ED93D47' => [
                'TABLE_NAME' => 'saved_filter_subscription_user_group',
                'REFERENCED_TABLE_NAME' => 'user_group',
            ],
            'FK_2A5B5277C68197EE' => [
                'TABLE_NAME' => 'saved_filter_subscription_user_group',
                'REFERENCED_TABLE_NAME' => 'saved_filter_subscription',
            ],
            'FK_2A7210271D9C9EB7' => [
                'TABLE_NAME' => 'base_local_group_money',
                'REFERENCED_TABLE_NAME' => 'currency',
            ],
            'FK_2A7210273101778E' => [
                'TABLE_NAME' => 'base_local_group_money',
                'REFERENCED_TABLE_NAME' => 'currency',
            ],
            'FK_2A721027532DF3D8' => [
                'TABLE_NAME' => 'base_local_group_money',
                'REFERENCED_TABLE_NAME' => 'currency',
            ],
            'FK_2FC152C1BF396750' => [
                'TABLE_NAME' => 'dtm_number_item_value',
                'REFERENCED_TABLE_NAME' => 'dtm_item_value',
            ],
            'FK_320121A6126F525E' => [
                'TABLE_NAME' => 'dtm_item_country_report_to_item_entry',
                'REFERENCED_TABLE_NAME' => 'dtm_item',
            ],
            'FK_320121A67301CE56' => [
                'TABLE_NAME' => 'dtm_item_country_report_to_item_entry',
                'REFERENCED_TABLE_NAME' => 'dtm_item_country_report',
            ],
            'FK_3445C1833F571310' => [
                'TABLE_NAME' => 'igt_igt5_transaction',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_3445C183473DBD53' => [
                'TABLE_NAME' => 'igt_igt5_transaction',
                'REFERENCED_TABLE_NAME' => 'pricing_method',
            ],
            'FK_3445C1835F8C47AD' => [
                'TABLE_NAME' => 'igt_igt5_transaction',
                'REFERENCED_TABLE_NAME' => 'trace_id',
            ],
            'FK_3445C183896DBBDE' => [
                'TABLE_NAME' => 'igt_igt5_transaction',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_3445C1838DB60186' => [
                'TABLE_NAME' => 'igt_igt5_transaction',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_3445C183AC8DE0F' => [
                'TABLE_NAME' => 'igt_igt5_transaction',
                'REFERENCED_TABLE_NAME' => 'igt_service_type',
            ],
            'FK_3445C183B03A8386' => [
                'TABLE_NAME' => 'igt_igt5_transaction',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_3445C183B75292CA' => [
                'TABLE_NAME' => 'igt_igt5_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_3445C183EC8B7ADE' => [
                'TABLE_NAME' => 'igt_igt5_transaction',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_3445C183F8BD700D' => [
                'TABLE_NAME' => 'igt_igt5_transaction',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_3445C183FFAE5AB7' => [
                'TABLE_NAME' => 'igt_igt5_transaction',
                'REFERENCED_TABLE_NAME' => 'country',
            ],
            'FK_363E2041A76ED395' => [
                'TABLE_NAME' => 'current_user_is_user_condition_user',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_363E2041B265B5F5' => [
                'TABLE_NAME' => 'current_user_is_user_condition_user',
                'REFERENCED_TABLE_NAME' => 'workflow_transition_conditions',
            ],
            'FK_3784F3181ED93D47' => [
                'TABLE_NAME' => 'group_permission',
                'REFERENCED_TABLE_NAME' => 'user_group',
            ],
            'FK_3784F318C05070BB' => [
                'TABLE_NAME' => 'group_permission',
                'REFERENCED_TABLE_NAME' => 'collective_permission',
            ],
            'FK_3F15B2EB84042C99' => [
                'TABLE_NAME' => 'tcm_tax_filing_monitor',
                'REFERENCED_TABLE_NAME' => 'tax_type',
            ],
            'FK_3F15B2EB896DBBDE' => [
                'TABLE_NAME' => 'tcm_tax_filing_monitor',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_3F15B2EB8DB60186' => [
                'TABLE_NAME' => 'tcm_tax_filing_monitor',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_3F15B2EBAADEEA81' => [
                'TABLE_NAME' => 'tcm_tax_filing_monitor',
                'REFERENCED_TABLE_NAME' => 'tcm_declaration_type',
            ],
            'FK_3F15B2EBB03A8386' => [
                'TABLE_NAME' => 'tcm_tax_filing_monitor',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_3F15B2EBF8BD700D' => [
                'TABLE_NAME' => 'tcm_tax_filing_monitor',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_40DF1DE3B6A263D9' => [
                'TABLE_NAME' => 'import_data',
                'REFERENCED_TABLE_NAME' => 'import',
            ],
            'FK_439F736A1ED93D47' => [
                'TABLE_NAME' => 'authorization_profile_user_group',
                'REFERENCED_TABLE_NAME' => 'user_group',
            ],
            'FK_439F736A9525ACFC' => [
                'TABLE_NAME' => 'authorization_profile_user_group',
                'REFERENCED_TABLE_NAME' => 'authorization_profile',
            ],
            'FK_45F27E2E126F525E' => [
                'TABLE_NAME' => 'dtm_field',
                'REFERENCED_TABLE_NAME' => 'dtm_item',
            ],
            'FK_45F27E2E8C22AA1A' => [
                'TABLE_NAME' => 'dtm_field',
                'REFERENCED_TABLE_NAME' => 'dtm_layout',
            ],
            'FK_472E5446A76ED395' => [
                'TABLE_NAME' => 'user_permission',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_472E5446C05070BB' => [
                'TABLE_NAME' => 'user_permission',
                'REFERENCED_TABLE_NAME' => 'collective_permission',
            ],
            'FK_475FD4E684042C99' => [
                'TABLE_NAME' => 'tam_loss_carry_forward',
                'REFERENCED_TABLE_NAME' => 'tax_type',
            ],
            'FK_475FD4E6896DBBDE' => [
                'TABLE_NAME' => 'tam_loss_carry_forward',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_475FD4E68DB60186' => [
                'TABLE_NAME' => 'tam_loss_carry_forward',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_475FD4E695D7F614' => [
                'TABLE_NAME' => 'tam_loss_carry_forward',
                'REFERENCED_TABLE_NAME' => 'tam_loss_type',
            ],
            'FK_475FD4E6B03A8386' => [
                'TABLE_NAME' => 'tam_loss_carry_forward',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_475FD4E6B649AB19' => [
                'TABLE_NAME' => 'tam_loss_carry_forward',
                'REFERENCED_TABLE_NAME' => 'tam_loss_restriction',
            ],
            'FK_475FD4E6EC8B7ADE' => [
                'TABLE_NAME' => 'tam_loss_carry_forward',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_475FD4E6F8BD700D' => [
                'TABLE_NAME' => 'tam_loss_carry_forward',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_4890895884042C99' => [
                'TABLE_NAME' => 'tam_income_tax_planning',
                'REFERENCED_TABLE_NAME' => 'tax_type',
            ],
            'FK_48908958896DBBDE' => [
                'TABLE_NAME' => 'tam_income_tax_planning',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_489089588DB60186' => [
                'TABLE_NAME' => 'tam_income_tax_planning',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_48908958B03A8386' => [
                'TABLE_NAME' => 'tam_income_tax_planning',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_48908958EC8B7ADE' => [
                'TABLE_NAME' => 'tam_income_tax_planning',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_48908958F8BD700D' => [
                'TABLE_NAME' => 'tam_income_tax_planning',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_4936C7DB287DA498' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_4936C7DB3F571310' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_4936C7DB473DBD53' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'pricing_method',
            ],
            'FK_4936C7DB5CCD6FF6' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'currency',
            ],
            'FK_4936C7DB5F8C47AD' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'trace_id',
            ],
            'FK_4936C7DB675BD393' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_4936C7DB896DBBDE' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_4936C7DB8DB60186' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_4936C7DBB03A8386' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_4936C7DBD41C16E3' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'igt_instrument_id_type',
            ],
            'FK_4936C7DBD51AA2C3' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'currency',
            ],
            'FK_4936C7DBE143AA07' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_4936C7DBEC8B7ADE' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_4936C7DBF20D281A' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_4936C7DBF8BD700D' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_4936C7DBFFABFB8F' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'igt_instrument_id_type',
            ],
            'FK_4936C7DBFFAE5AB7' => [
                'TABLE_NAME' => 'igt_igt2_transaction',
                'REFERENCED_TABLE_NAME' => 'country',
            ],
            'FK_4A68DEDBA76ED395' => [
                'TABLE_NAME' => 'saved_filter_subscription_user',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_4A68DEDBC68197EE' => [
                'TABLE_NAME' => 'saved_filter_subscription_user',
                'REFERENCED_TABLE_NAME' => 'saved_filter_subscription',
            ],
            'FK_4BBBE22FA76ED395' => [
                'TABLE_NAME' => 'saved_filter_user',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_4BBBE22FE64EFBA3' => [
                'TABLE_NAME' => 'saved_filter_user',
                'REFERENCED_TABLE_NAME' => 'saved_filter',
            ],
            'FK_4C62E638F5B7AF75' => [
                'TABLE_NAME' => 'contact',
                'REFERENCED_TABLE_NAME' => 'address',
            ],
            'FK_4C62E638F92F3E70' => [
                'TABLE_NAME' => 'contact',
                'REFERENCED_TABLE_NAME' => 'country',
            ],
            'FK_4D551520896DBBDE' => [
                'TABLE_NAME' => 'tcm_other_deadline',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_4D5515208DB60186' => [
                'TABLE_NAME' => 'tcm_other_deadline',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_4D551520A4D76431' => [
                'TABLE_NAME' => 'tcm_other_deadline',
                'REFERENCED_TABLE_NAME' => 'deadline_type',
            ],
            'FK_4D551520B03A8386' => [
                'TABLE_NAME' => 'tcm_other_deadline',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_4D551520F8BD700D' => [
                'TABLE_NAME' => 'tcm_other_deadline',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_511AA1952C7C2CBA' => [
                'TABLE_NAME' => 'workflow_binding',
                'REFERENCED_TABLE_NAME' => 'workflow',
            ],
            'FK_527EDB2559EC7D60' => [
                'TABLE_NAME' => 'task',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_527EDB256BF700BD' => [
                'TABLE_NAME' => 'task',
                'REFERENCED_TABLE_NAME' => 'workflow_status',
            ],
            'FK_527EDB25896DBBDE' => [
                'TABLE_NAME' => 'task',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_527EDB25B03A8386' => [
                'TABLE_NAME' => 'task',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_527EDB25E1CFE6F5' => [
                'TABLE_NAME' => 'task',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_528691CF1ED93D47' => [
                'TABLE_NAME' => 'user_group_unit',
                'REFERENCED_TABLE_NAME' => 'user_group',
            ],
            'FK_528691CFF8BD700D' => [
                'TABLE_NAME' => 'user_group_unit',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_52E1F538896DBBDE' => [
                'TABLE_NAME' => 'unit_period',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_52E1F5388DB60186' => [
                'TABLE_NAME' => 'unit_period',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_52E1F538B03A8386' => [
                'TABLE_NAME' => 'unit_period',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_52E1F538EC8B7ADE' => [
                'TABLE_NAME' => 'unit_period',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_52E1F538F8BD700D' => [
                'TABLE_NAME' => 'unit_period',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_58551C7F90A8E95D' => [
                'TABLE_NAME' => 'tpm_local_file_section_file',
                'REFERENCED_TABLE_NAME' => 'tpm_local_file_section',
            ],
            'FK_58551C7F93CB796C' => [
                'TABLE_NAME' => 'tpm_local_file_section_file',
                'REFERENCED_TABLE_NAME' => 'file',
            ],
            'FK_5970990324DC16B' => [
                'TABLE_NAME' => 'tpm_country_by_country_report_section',
                'REFERENCED_TABLE_NAME' => 'tpm_country_by_country_report',
            ],
            'FK_5970990896DBBDE' => [
                'TABLE_NAME' => 'tpm_country_by_country_report_section',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_5970990B03A8386' => [
                'TABLE_NAME' => 'tpm_country_by_country_report_section',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_59CC14384042C99' => [
                'TABLE_NAME' => 'tam_tax_litigation',
                'REFERENCED_TABLE_NAME' => 'tax_type',
            ],
            'FK_59CC143896DBBDE' => [
                'TABLE_NAME' => 'tam_tax_litigation',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_59CC1438DB60186' => [
                'TABLE_NAME' => 'tam_tax_litigation',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_59CC1438F53E2DC' => [
                'TABLE_NAME' => 'tam_tax_litigation',
                'REFERENCED_TABLE_NAME' => 'tam_risk_type',
            ],
            'FK_59CC143908E2FFE' => [
                'TABLE_NAME' => 'tam_tax_litigation',
                'REFERENCED_TABLE_NAME' => 'tam_specification',
            ],
            'FK_59CC143B03A8386' => [
                'TABLE_NAME' => 'tam_tax_litigation',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_59CC143EC8B7ADE' => [
                'TABLE_NAME' => 'tam_tax_litigation',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_59CC143F8BD700D' => [
                'TABLE_NAME' => 'tam_tax_litigation',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_59D74A93CB796C' => [
                'TABLE_NAME' => 'unit_file',
                'REFERENCED_TABLE_NAME' => 'file',
            ],
            'FK_59D74AF8BD700D' => [
                'TABLE_NAME' => 'unit_file',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_64C4D68E896DBBDE' => [
                'TABLE_NAME' => 'saved_filter_subscription',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_64C4D68EB03A8386' => [
                'TABLE_NAME' => 'saved_filter_subscription',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_64C4D68EE64EFBA3' => [
                'TABLE_NAME' => 'saved_filter_subscription',
                'REFERENCED_TABLE_NAME' => 'saved_filter',
            ],
            'FK_65C59816A04E0A68' => [
                'TABLE_NAME' => 'workflow',
                'REFERENCED_TABLE_NAME' => 'workflow_status',
            ],
            'FK_65D16FE324DC16B' => [
                'TABLE_NAME' => 'tpm_country_by_country_report_unit',
                'REFERENCED_TABLE_NAME' => 'tpm_country_by_country_report',
            ],
            'FK_65D16FEF8BD700D' => [
                'TABLE_NAME' => 'tpm_country_by_country_report_unit',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_65EBB890233222CE' => [
                'TABLE_NAME' => 'document_template_section_file',
                'REFERENCED_TABLE_NAME' => 'document_template_section',
            ],
            'FK_65EBB89093CB796C' => [
                'TABLE_NAME' => 'document_template_section_file',
                'REFERENCED_TABLE_NAME' => 'file',
            ],
            'FK_6A3A796F2C7C2CBA' => [
                'TABLE_NAME' => 'workflow_transition',
                'REFERENCED_TABLE_NAME' => 'workflow',
            ],
            'FK_6A3A796F3A77B047' => [
                'TABLE_NAME' => 'workflow_transition',
                'REFERENCED_TABLE_NAME' => 'workflow_status',
            ],
            'FK_6A3A796FD67944CB' => [
                'TABLE_NAME' => 'workflow_transition',
                'REFERENCED_TABLE_NAME' => 'workflow_status',
            ],
            'FK_6CBC77996BF700BD' => [
                'TABLE_NAME' => 'workflow_field_configuration_statuses_to_status',
                'REFERENCED_TABLE_NAME' => 'workflow_status',
            ],
            'FK_6CBC7799722AB997' => [
                'TABLE_NAME' => 'workflow_field_configuration_statuses_to_status',
                'REFERENCED_TABLE_NAME' => 'workflow_to_field_configuration_statuses',
            ],
            'FK_70DA042177D80F90' => [
                'TABLE_NAME' => 'user_audit',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_70DA0421B64C06E5' => [
                'TABLE_NAME' => 'user_audit',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_72206399A76ED395' => [
                'TABLE_NAME' => 'user_password_history',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_74900D358DB60186' => [
                'TABLE_NAME' => 'task_to_file',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_74900D3593CB796C' => [
                'TABLE_NAME' => 'task_to_file',
                'REFERENCED_TABLE_NAME' => 'file',
            ],
            'FK_761A507B8C22AA1A' => [
                'TABLE_NAME' => 'dtm_layout_collection_entry',
                'REFERENCED_TABLE_NAME' => 'dtm_layout',
            ],
            'FK_761A507BCD381AB5' => [
                'TABLE_NAME' => 'dtm_layout_collection_entry',
                'REFERENCED_TABLE_NAME' => 'dtm_layout_collection',
            ],
            'FK_76AC9FB28ABC61BF' => [
                'TABLE_NAME' => 'unit_hierarchy_definition',
                'REFERENCED_TABLE_NAME' => 'unit_hierarchy',
            ],
            'FK_76AC9FB28AF5044B' => [
                'TABLE_NAME' => 'unit_hierarchy_definition',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_76AC9FB2F8BD700D' => [
                'TABLE_NAME' => 'unit_hierarchy_definition',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_77587C5A8BF1A064' => [
                'TABLE_NAME' => 'workflow_transition_actions',
                'REFERENCED_TABLE_NAME' => 'workflow_transition',
            ],
            'FK_794381C68DB60186' => [
                'TABLE_NAME' => 'review',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_794381C6A76ED395' => [
                'TABLE_NAME' => 'review',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_7A01DAA5896DBBDE' => [
                'TABLE_NAME' => 'tpm_master_file',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_7A01DAA58ABC61BF' => [
                'TABLE_NAME' => 'tpm_master_file',
                'REFERENCED_TABLE_NAME' => 'unit_hierarchy',
            ],
            'FK_7A01DAA58DB60186' => [
                'TABLE_NAME' => 'tpm_master_file',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_7A01DAA5B03A8386' => [
                'TABLE_NAME' => 'tpm_master_file',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_7A01DAA5C05070BB' => [
                'TABLE_NAME' => 'tpm_master_file',
                'REFERENCED_TABLE_NAME' => 'collective_permission',
            ],
            'FK_7A01DAA5EC8B7ADE' => [
                'TABLE_NAME' => 'tpm_master_file',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_7A01DAA5F8BD700D' => [
                'TABLE_NAME' => 'tpm_master_file',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_7B3132D92C7C2CBA' => [
                'TABLE_NAME' => 'workflow_to_field_configuration_statuses',
                'REFERENCED_TABLE_NAME' => 'workflow',
            ],
            'FK_7B3132D9317DA47B' => [
                'TABLE_NAME' => 'workflow_to_field_configuration_statuses',
                'REFERENCED_TABLE_NAME' => 'workflow_field_configuration',
            ],
            'FK_7B3132D9896DBBDE' => [
                'TABLE_NAME' => 'workflow_to_field_configuration_statuses',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_7B3132D9B03A8386' => [
                'TABLE_NAME' => 'workflow_to_field_configuration_statuses',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_7CB0A9D27301CE56' => [
                'TABLE_NAME' => 'dtm_layout_collection_item_country_report_entry',
                'REFERENCED_TABLE_NAME' => 'dtm_item_country_report',
            ],
            'FK_7CB0A9D2CD381AB5' => [
                'TABLE_NAME' => 'dtm_layout_collection_item_country_report_entry',
                'REFERENCED_TABLE_NAME' => 'dtm_layout_collection',
            ],
            'FK_7FBC4C0B709385E7' => [
                'TABLE_NAME' => 'task_check_state',
                'REFERENCED_TABLE_NAME' => 'workflow_check',
            ],
            'FK_7FBC4C0B8DB60186' => [
                'TABLE_NAME' => 'task_check_state',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_7FBC4C0BB03A8386' => [
                'TABLE_NAME' => 'task_check_state',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_8342B8D0896DBBDE' => [
                'TABLE_NAME' => 'dtm_layout',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_8342B8D09C3E4F87' => [
                'TABLE_NAME' => 'dtm_layout',
                'REFERENCED_TABLE_NAME' => 'collective_permission',
            ],
            'FK_8342B8D0B03A8386' => [
                'TABLE_NAME' => 'dtm_layout',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_839714889525ACFC' => [
                'TABLE_NAME' => 'authorization_profile_user',
                'REFERENCED_TABLE_NAME' => 'authorization_profile',
            ],
            'FK_83971488A76ED395' => [
                'TABLE_NAME' => 'authorization_profile_user',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_875286E11A4B72F2' => [
                'TABLE_NAME' => 'tpm_financial_data',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_875286E11FDA2F42' => [
                'TABLE_NAME' => 'tpm_financial_data',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_875286E1387521E7' => [
                'TABLE_NAME' => 'tpm_financial_data',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_875286E139D243EE' => [
                'TABLE_NAME' => 'tpm_financial_data',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_875286E14ED5E0EC' => [
                'TABLE_NAME' => 'tpm_financial_data',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_875286E15E479E07' => [
                'TABLE_NAME' => 'tpm_financial_data',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_875286E1896DBBDE' => [
                'TABLE_NAME' => 'tpm_financial_data',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_875286E18DB60186' => [
                'TABLE_NAME' => 'tpm_financial_data',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_875286E19D991D04' => [
                'TABLE_NAME' => 'tpm_financial_data',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_875286E1B03A8386' => [
                'TABLE_NAME' => 'tpm_financial_data',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_875286E1CB880735' => [
                'TABLE_NAME' => 'tpm_financial_data',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_875286E1D9E9181C' => [
                'TABLE_NAME' => 'tpm_financial_data',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_875286E1EC8B7ADE' => [
                'TABLE_NAME' => 'tpm_financial_data',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_875286E1F8BD700D' => [
                'TABLE_NAME' => 'tpm_financial_data',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_881FE919A76ED395' => [
                'TABLE_NAME' => 'user_to_favourite_saved_filters',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_881FE919E64EFBA3' => [
                'TABLE_NAME' => 'user_to_favourite_saved_filters',
                'REFERENCED_TABLE_NAME' => 'saved_filter',
            ],
            'FK_8B2D803193CB796C' => [
                'TABLE_NAME' => 'file_file_type',
                'REFERENCED_TABLE_NAME' => 'file',
            ],
            'FK_8B2D80319E2A35A8' => [
                'TABLE_NAME' => 'file_file_type',
                'REFERENCED_TABLE_NAME' => 'file_type',
            ],
            'FK_8B957886727ACA70' => [
                'TABLE_NAME' => 'task_comment',
                'REFERENCED_TABLE_NAME' => 'task_comment',
            ],
            'FK_8B9578868DB60186' => [
                'TABLE_NAME' => 'task_comment',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_8B957886F675F31B' => [
                'TABLE_NAME' => 'task_comment',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_8B957886FE54D947' => [
                'TABLE_NAME' => 'task_comment',
                'REFERENCED_TABLE_NAME' => 'user_group',
            ],
            'FK_8C9F3610896DBBDE' => [
                'TABLE_NAME' => 'file',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_8C9F3610B03A8386' => [
                'TABLE_NAME' => 'file',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_8C9F3610C05070BB' => [
                'TABLE_NAME' => 'file',
                'REFERENCED_TABLE_NAME' => 'collective_permission',
            ],
            'FK_8D182FE86BF700BD' => [
                'TABLE_NAME' => 'workflow_check_to_workflow_status',
                'REFERENCED_TABLE_NAME' => 'workflow_status',
            ],
            'FK_8D182FE8709385E7' => [
                'TABLE_NAME' => 'workflow_check_to_workflow_status',
                'REFERENCED_TABLE_NAME' => 'workflow_check',
            ],
            'FK_8D93D649D526A7D3' => [
                'TABLE_NAME' => 'user',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_8D93D649E7A1254A' => [
                'TABLE_NAME' => 'user',
                'REFERENCED_TABLE_NAME' => 'contact',
            ],
            'FK_8DC0DA272C7C2CBA' => [
                'TABLE_NAME' => 'workflow_check',
                'REFERENCED_TABLE_NAME' => 'workflow',
            ],
            'FK_8DC0DA27896DBBDE' => [
                'TABLE_NAME' => 'workflow_check',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_8DC0DA27B03A8386' => [
                'TABLE_NAME' => 'workflow_check',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_8F8F9016126F525E' => [
                'TABLE_NAME' => 'dtm_item_formula',
                'REFERENCED_TABLE_NAME' => 'dtm_item',
            ],
            'FK_8FB9290F8DB60186' => [
                'TABLE_NAME' => 'task_layout_collection',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_8FB9290FCD381AB5' => [
                'TABLE_NAME' => 'task_layout_collection',
                'REFERENCED_TABLE_NAME' => 'dtm_layout_collection',
            ],
            'FK_93B7A7ABBF396750' => [
                'TABLE_NAME' => 'dtm_money_item_value',
                'REFERENCED_TABLE_NAME' => 'dtm_item_value',
            ],
            'FK_93E89F38896DBBDE' => [
                'TABLE_NAME' => 'tpm_local_file_section',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_93E89F38B03A8386' => [
                'TABLE_NAME' => 'tpm_local_file_section',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_93E89F38E1E7CE1' => [
                'TABLE_NAME' => 'tpm_local_file_section',
                'REFERENCED_TABLE_NAME' => 'tpm_local_file',
            ],
            'FK_94A01E5E3F571310' => [
                'TABLE_NAME' => 'igt_igt3_transaction',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_94A01E5E473DBD53' => [
                'TABLE_NAME' => 'igt_igt3_transaction',
                'REFERENCED_TABLE_NAME' => 'pricing_method',
            ],
            'FK_94A01E5E4FD78D0E' => [
                'TABLE_NAME' => 'igt_igt3_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_94A01E5E5F8C47AD' => [
                'TABLE_NAME' => 'igt_igt3_transaction',
                'REFERENCED_TABLE_NAME' => 'trace_id',
            ],
            'FK_94A01E5E896DBBDE' => [
                'TABLE_NAME' => 'igt_igt3_transaction',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_94A01E5E8DB60186' => [
                'TABLE_NAME' => 'igt_igt3_transaction',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_94A01E5EA264169D' => [
                'TABLE_NAME' => 'igt_igt3_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_94A01E5EAC70E9C3' => [
                'TABLE_NAME' => 'igt_igt3_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_94A01E5EB03A8386' => [
                'TABLE_NAME' => 'igt_igt3_transaction',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_94A01E5EC56149D9' => [
                'TABLE_NAME' => 'igt_igt3_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_94A01E5ED56F8C8B' => [
                'TABLE_NAME' => 'igt_igt3_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_94A01E5EEC8B7ADE' => [
                'TABLE_NAME' => 'igt_igt3_transaction',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_94A01E5EF8BD700D' => [
                'TABLE_NAME' => 'igt_igt3_transaction',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_94A01E5EFFAE5AB7' => [
                'TABLE_NAME' => 'igt_igt3_transaction',
                'REFERENCED_TABLE_NAME' => 'country',
            ],
            'FK_963D49AC896DBBDE' => [
                'TABLE_NAME' => 'tpm_local_file',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_963D49AC8ABC61BF' => [
                'TABLE_NAME' => 'tpm_local_file',
                'REFERENCED_TABLE_NAME' => 'unit_hierarchy',
            ],
            'FK_963D49AC8DB60186' => [
                'TABLE_NAME' => 'tpm_local_file',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_963D49ACB03A8386' => [
                'TABLE_NAME' => 'tpm_local_file',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_963D49ACC05070BB' => [
                'TABLE_NAME' => 'tpm_local_file',
                'REFERENCED_TABLE_NAME' => 'collective_permission',
            ],
            'FK_963D49ACEC8B7ADE' => [
                'TABLE_NAME' => 'tpm_local_file',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_963D49ACF8BD700D' => [
                'TABLE_NAME' => 'tpm_local_file',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_963D49ACF92F3E70' => [
                'TABLE_NAME' => 'tpm_local_file',
                'REFERENCED_TABLE_NAME' => 'country',
            ],
            'FK_9AE3B94BA76ED395' => [
                'TABLE_NAME' => 'dashboard_user',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_9AE3B94BB9D04D2B' => [
                'TABLE_NAME' => 'dashboard_user',
                'REFERENCED_TABLE_NAME' => 'dashboard',
            ],
            'FK_9D4ECE1DA76ED395' => [
                'TABLE_NAME' => 'import',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_A3B0771C77D80F90' => [
                'TABLE_NAME' => 'period_audit',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_A3B0771CB64C06E5' => [
                'TABLE_NAME' => 'period_audit',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_A469C342126F525E' => [
                'TABLE_NAME' => 'workflow_transition_conditions',
                'REFERENCED_TABLE_NAME' => 'dtm_item',
            ],
            'FK_A469C3428BF1A064' => [
                'TABLE_NAME' => 'workflow_transition_conditions',
                'REFERENCED_TABLE_NAME' => 'workflow_transition',
            ],
            'FK_A4A2CE184FEC9404' => [
                'TABLE_NAME' => 'tpm_master_file_unit',
                'REFERENCED_TABLE_NAME' => 'tpm_master_file',
            ],
            'FK_A4A2CE18F8BD700D' => [
                'TABLE_NAME' => 'tpm_master_file_unit',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_A63A409AA76ED395' => [
                'TABLE_NAME' => 'user_unit',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_A63A409AF8BD700D' => [
                'TABLE_NAME' => 'user_unit',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_A7EEB7823F571310' => [
                'TABLE_NAME' => 'cm_contract',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_A7EEB782896DBBDE' => [
                'TABLE_NAME' => 'cm_contract',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_A7EEB7828DB60186' => [
                'TABLE_NAME' => 'cm_contract',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_A7EEB782B03A8386' => [
                'TABLE_NAME' => 'cm_contract',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_A7EEB782CD1DF15B' => [
                'TABLE_NAME' => 'cm_contract',
                'REFERENCED_TABLE_NAME' => 'contract_type',
            ],
            'FK_A7EEB782F8BD700D' => [
                'TABLE_NAME' => 'cm_contract',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_A7EEB782FFAE5AB7' => [
                'TABLE_NAME' => 'cm_contract',
                'REFERENCED_TABLE_NAME' => 'country',
            ],
            'FK_A97043BDBF396750' => [
                'TABLE_NAME' => 'dtm_percent_item_value',
                'REFERENCED_TABLE_NAME' => 'dtm_item_value',
            ],
            'FK_ABFEEB631ED93D47' => [
                'TABLE_NAME' => 'dashboard_user_group',
                'REFERENCED_TABLE_NAME' => 'user_group',
            ],
            'FK_ABFEEB63B9D04D2B' => [
                'TABLE_NAME' => 'dashboard_user_group',
                'REFERENCED_TABLE_NAME' => 'dashboard',
            ],
            'FK_AEC509E359BB1592' => [
                'TABLE_NAME' => 'tam_tax_relevant_restriction',
                'REFERENCED_TABLE_NAME' => 'tam_restriction_reason',
            ],
            'FK_AEC509E384042C99' => [
                'TABLE_NAME' => 'tam_tax_relevant_restriction',
                'REFERENCED_TABLE_NAME' => 'tax_type',
            ],
            'FK_AEC509E3896DBBDE' => [
                'TABLE_NAME' => 'tam_tax_relevant_restriction',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_AEC509E38DB60186' => [
                'TABLE_NAME' => 'tam_tax_relevant_restriction',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_AEC509E3B03A8386' => [
                'TABLE_NAME' => 'tam_tax_relevant_restriction',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_AEC509E3EC8B7ADE' => [
                'TABLE_NAME' => 'tam_tax_relevant_restriction',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_AEC509E3F8BD700D' => [
                'TABLE_NAME' => 'tam_tax_relevant_restriction',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_AF1CC40D20C45065' => [
                'TABLE_NAME' => 'tpm_transaction',
                'REFERENCED_TABLE_NAME' => 'currency',
            ],
            'FK_AF1CC40D3F571310' => [
                'TABLE_NAME' => 'tpm_transaction',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_AF1CC40D473DBD53' => [
                'TABLE_NAME' => 'tpm_transaction',
                'REFERENCED_TABLE_NAME' => 'pricing_method',
            ],
            'FK_AF1CC40D53FED9F2' => [
                'TABLE_NAME' => 'tpm_transaction',
                'REFERENCED_TABLE_NAME' => 'currency',
            ],
            'FK_AF1CC40D73899EFA' => [
                'TABLE_NAME' => 'tpm_transaction',
                'REFERENCED_TABLE_NAME' => 'currency',
            ],
            'FK_AF1CC40D84AD945B' => [
                'TABLE_NAME' => 'tpm_transaction',
                'REFERENCED_TABLE_NAME' => 'currency',
            ],
            'FK_AF1CC40D896DBBDE' => [
                'TABLE_NAME' => 'tpm_transaction',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_AF1CC40D8DB60186' => [
                'TABLE_NAME' => 'tpm_transaction',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_AF1CC40DAE620744' => [
                'TABLE_NAME' => 'tpm_transaction',
                'REFERENCED_TABLE_NAME' => 'billing_type',
            ],
            'FK_AF1CC40DB03A8386' => [
                'TABLE_NAME' => 'tpm_transaction',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_AF1CC40DC54C8C93' => [
                'TABLE_NAME' => 'tpm_transaction',
                'REFERENCED_TABLE_NAME' => 'transaction_type',
            ],
            'FK_AF1CC40DC5BEC7BC' => [
                'TABLE_NAME' => 'tpm_transaction',
                'REFERENCED_TABLE_NAME' => 'currency',
            ],
            'FK_AF1CC40DEC8B7ADE' => [
                'TABLE_NAME' => 'tpm_transaction',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_AF1CC40DF8BD700D' => [
                'TABLE_NAME' => 'tpm_transaction',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_B7660071126F525E' => [
                'TABLE_NAME' => 'dtm_item_value',
                'REFERENCED_TABLE_NAME' => 'dtm_item',
            ],
            'FK_B7660071EC8B7ADE' => [
                'TABLE_NAME' => 'dtm_item_value',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_B7660071F8BD700D' => [
                'TABLE_NAME' => 'dtm_item_value',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_C13D0F24BF396750' => [
                'TABLE_NAME' => 'dtm_text_item_value',
                'REFERENCED_TABLE_NAME' => 'dtm_item_value',
            ],
            'FK_C5B81ECE896DBBDE' => [
                'TABLE_NAME' => 'period',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_C5B81ECEB03A8386' => [
                'TABLE_NAME' => 'period',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_C5B81ECEEBF955DF' => [
                'TABLE_NAME' => 'period',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_C779A692A76ED395' => [
                'TABLE_NAME' => 'user_setting',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_C912ED9DB03A8386' => [
                'TABLE_NAME' => 'api_key',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_CA2FCFE4BF396750' => [
                'TABLE_NAME' => 'dtm_checkbox_item_value',
                'REFERENCED_TABLE_NAME' => 'dtm_item_value',
            ],
            'FK_CA662A5930C35A0' => [
                'TABLE_NAME' => 'tpm_country_by_country_report',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_CA662A59896DBBDE' => [
                'TABLE_NAME' => 'tpm_country_by_country_report',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_CA662A598ABC61BF' => [
                'TABLE_NAME' => 'tpm_country_by_country_report',
                'REFERENCED_TABLE_NAME' => 'unit_hierarchy',
            ],
            'FK_CA662A598DB60186' => [
                'TABLE_NAME' => 'tpm_country_by_country_report',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_CA662A59B03A8386' => [
                'TABLE_NAME' => 'tpm_country_by_country_report',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_CA662A59C05070BB' => [
                'TABLE_NAME' => 'tpm_country_by_country_report',
                'REFERENCED_TABLE_NAME' => 'collective_permission',
            ],
            'FK_CA662A59E23500DB' => [
                'TABLE_NAME' => 'tpm_country_by_country_report',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_CA662A59EC8B7ADE' => [
                'TABLE_NAME' => 'tpm_country_by_country_report',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_CA662A59F8BD700D' => [
                'TABLE_NAME' => 'tpm_country_by_country_report',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_CA662A59F92F3E70' => [
                'TABLE_NAME' => 'tpm_country_by_country_report',
                'REFERENCED_TABLE_NAME' => 'country',
            ],
            'FK_CB2F725F561EE3C3' => [
                'TABLE_NAME' => 'tpm_master_file_section_file',
                'REFERENCED_TABLE_NAME' => 'tpm_master_file_section',
            ],
            'FK_CB2F725F93CB796C' => [
                'TABLE_NAME' => 'tpm_master_file_section_file',
                'REFERENCED_TABLE_NAME' => 'file',
            ],
            'FK_CC1B05BA4FEC9404' => [
                'TABLE_NAME' => 'tpm_master_file_section',
                'REFERENCED_TABLE_NAME' => 'tpm_master_file',
            ],
            'FK_CC1B05BA896DBBDE' => [
                'TABLE_NAME' => 'tpm_master_file_section',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_CC1B05BAB03A8386' => [
                'TABLE_NAME' => 'tpm_master_file_section',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_CCF6B5E1896DBBDE' => [
                'TABLE_NAME' => 'tpm_main_business_activity',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_CCF6B5E18DB60186' => [
                'TABLE_NAME' => 'tpm_main_business_activity',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_CCF6B5E1B03A8386' => [
                'TABLE_NAME' => 'tpm_main_business_activity',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_CCF6B5E1C004B9FE' => [
                'TABLE_NAME' => 'tpm_main_business_activity',
                'REFERENCED_TABLE_NAME' => 'tpm_business_activity',
            ],
            'FK_CCF6B5E1EC8B7ADE' => [
                'TABLE_NAME' => 'tpm_main_business_activity',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_CCF6B5E1F8BD700D' => [
                'TABLE_NAME' => 'tpm_main_business_activity',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_CE48CAADF5B7AF75' => [
                'TABLE_NAME' => 'auditor',
                'REFERENCED_TABLE_NAME' => 'address',
            ],
            'FK_D4E6F81F92F3E70' => [
                'TABLE_NAME' => 'address',
                'REFERENCED_TABLE_NAME' => 'country',
            ],
            'FK_D5B50E692F8B0EB2' => [
                'TABLE_NAME' => 'authorization_user',
                'REFERENCED_TABLE_NAME' => 'authorization',
            ],
            'FK_D5B50E69A76ED395' => [
                'TABLE_NAME' => 'authorization_user',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_D6CF7CD5896DBBDE' => [
                'TABLE_NAME' => 'workflow_field_configuration',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_D6CF7CD5B03A8386' => [
                'TABLE_NAME' => 'workflow_field_configuration',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_D6DD01D91ED93D47' => [
                'TABLE_NAME' => 'authorization_user_group',
                'REFERENCED_TABLE_NAME' => 'user_group',
            ],
            'FK_D6DD01D92F8B0EB2' => [
                'TABLE_NAME' => 'authorization_user_group',
                'REFERENCED_TABLE_NAME' => 'authorization',
            ],
            'FK_D8F9E5C93F571310' => [
                'TABLE_NAME' => 'apm_transaction',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_D8F9E5C94FD78D0E' => [
                'TABLE_NAME' => 'apm_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_D8F9E5C9896DBBDE' => [
                'TABLE_NAME' => 'apm_transaction',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_D8F9E5C98DB60186' => [
                'TABLE_NAME' => 'apm_transaction',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_D8F9E5C992236FB8' => [
                'TABLE_NAME' => 'apm_transaction',
                'REFERENCED_TABLE_NAME' => 'apm_business_case',
            ],
            'FK_D8F9E5C9B03A8386' => [
                'TABLE_NAME' => 'apm_transaction',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_D8F9E5C9B75292CA' => [
                'TABLE_NAME' => 'apm_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_D8F9E5C9DF1882CC' => [
                'TABLE_NAME' => 'apm_transaction',
                'REFERENCED_TABLE_NAME' => 'collateralisation_type',
            ],
            'FK_D8F9E5C9EC8B7ADE' => [
                'TABLE_NAME' => 'apm_transaction',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_D8F9E5C9F8BD700D' => [
                'TABLE_NAME' => 'apm_transaction',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_D8F9E5C9FFAE5AB7' => [
                'TABLE_NAME' => 'apm_transaction',
                'REFERENCED_TABLE_NAME' => 'country',
            ],
            'FK_D9E2E33FF8BD700D' => [
                'TABLE_NAME' => 'unit_tax_number',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_D9E2E33FF92F3E70' => [
                'TABLE_NAME' => 'unit_tax_number',
                'REFERENCED_TABLE_NAME' => 'country',
            ],
            'FK_D9FD4237E1E7CE1' => [
                'TABLE_NAME' => 'tpm_local_file_unit',
                'REFERENCED_TABLE_NAME' => 'tpm_local_file',
            ],
            'FK_D9FD4237F8BD700D' => [
                'TABLE_NAME' => 'tpm_local_file_unit',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_DCBB0C532164B122' => [
                'TABLE_NAME' => 'unit',
                'REFERENCED_TABLE_NAME' => 'country',
            ],
            'FK_DCBB0C5338248176' => [
                'TABLE_NAME' => 'unit',
                'REFERENCED_TABLE_NAME' => 'currency',
            ],
            'FK_DCBB0C533D41F214' => [
                'TABLE_NAME' => 'unit',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_DCBB0C5379D0C0E4' => [
                'TABLE_NAME' => 'unit',
                'REFERENCED_TABLE_NAME' => 'address',
            ],
            'FK_DCBB0C53884D8118' => [
                'TABLE_NAME' => 'unit',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_DCBB0C53896DBBDE' => [
                'TABLE_NAME' => 'unit',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_DCBB0C5398CD0513' => [
                'TABLE_NAME' => 'unit',
                'REFERENCED_TABLE_NAME' => 'legal_form',
            ],
            'FK_DCBB0C53B03A8386' => [
                'TABLE_NAME' => 'unit',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_DCBB0C53BD445DAB' => [
                'TABLE_NAME' => 'unit',
                'REFERENCED_TABLE_NAME' => 'auditor',
            ],
            'FK_DCBB0C53DCD6CC49' => [
                'TABLE_NAME' => 'unit',
                'REFERENCED_TABLE_NAME' => 'branch',
            ],
            'FK_DCBB0C53DD533ADF' => [
                'TABLE_NAME' => 'unit',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_DCBB0C53E3008DF1' => [
                'TABLE_NAME' => 'unit',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_DCBB0C53F92F3E70' => [
                'TABLE_NAME' => 'unit',
                'REFERENCED_TABLE_NAME' => 'country',
            ],
            'FK_DCBB0C53FD54954B' => [
                'TABLE_NAME' => 'unit',
                'REFERENCED_TABLE_NAME' => 'address',
            ],
            'FK_DCBB0C53FEF3FDAB' => [
                'TABLE_NAME' => 'unit',
                'REFERENCED_TABLE_NAME' => 'auditor',
            ],
            'FK_DE1AAFFD317DA47B' => [
                'TABLE_NAME' => 'workflow_field_state',
                'REFERENCED_TABLE_NAME' => 'workflow_field_configuration',
            ],
            'FK_DE1AAFFD896DBBDE' => [
                'TABLE_NAME' => 'workflow_field_state',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_DE1AAFFDB03A8386' => [
                'TABLE_NAME' => 'workflow_field_state',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_DFDF4FDB8DB60186' => [
                'TABLE_NAME' => 'workflow_status_transition_log',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_E21FC81F6FB21D5D' => [
                'TABLE_NAME' => 'tcm_tax_assessment_monitor',
                'REFERENCED_TABLE_NAME' => 'tcm_assessment_type',
            ],
            'FK_E21FC81F84042C99' => [
                'TABLE_NAME' => 'tcm_tax_assessment_monitor',
                'REFERENCED_TABLE_NAME' => 'tax_type',
            ],
            'FK_E21FC81F896DBBDE' => [
                'TABLE_NAME' => 'tcm_tax_assessment_monitor',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_E21FC81F8DB60186' => [
                'TABLE_NAME' => 'tcm_tax_assessment_monitor',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_E21FC81FB03A8386' => [
                'TABLE_NAME' => 'tcm_tax_assessment_monitor',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_E21FC81FF8BD700D' => [
                'TABLE_NAME' => 'tcm_tax_assessment_monitor',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_E270DBD384042C99' => [
                'TABLE_NAME' => 'tam_tax_rate',
                'REFERENCED_TABLE_NAME' => 'tax_type',
            ],
            'FK_E270DBD3896DBBDE' => [
                'TABLE_NAME' => 'tam_tax_rate',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_E270DBD38DB60186' => [
                'TABLE_NAME' => 'tam_tax_rate',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_E270DBD3B03A8386' => [
                'TABLE_NAME' => 'tam_tax_rate',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_E270DBD3EC8B7ADE' => [
                'TABLE_NAME' => 'tam_tax_rate',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_E270DBD3F8BD700D' => [
                'TABLE_NAME' => 'tam_tax_rate',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_E9277F371ED93D47' => [
                'TABLE_NAME' => 'current_user_is_in_group_condition_user_group',
                'REFERENCED_TABLE_NAME' => 'user_group',
            ],
            'FK_E9277F37C68AED23' => [
                'TABLE_NAME' => 'current_user_is_in_group_condition_user_group',
                'REFERENCED_TABLE_NAME' => 'workflow_transition_conditions',
            ],
            'FK_E9521FAB896DBBDE' => [
                'TABLE_NAME' => 'exchange_rate',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_E9521FABB03A8386' => [
                'TABLE_NAME' => 'exchange_rate',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_E9521FABCDEEBD33' => [
                'TABLE_NAME' => 'exchange_rate',
                'REFERENCED_TABLE_NAME' => 'currency',
            ],
            'FK_E9521FABE2739A65' => [
                'TABLE_NAME' => 'exchange_rate',
                'REFERENCED_TABLE_NAME' => 'currency',
            ],
            'FK_E9521FABEC8B7ADE' => [
                'TABLE_NAME' => 'exchange_rate',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_E9D3180616EAB059' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_E9D3180618831704' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_E9D318061CE7B5B5' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'igt_line_of_business',
            ],
            'FK_E9D3180628F68919' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_E9D318063F571310' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_E9D31806473DBD53' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'pricing_method',
            ],
            'FK_E9D318065389369' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_E9D318065F8C47AD' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'trace_id',
            ],
            'FK_E9D31806896DBBDE' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_E9D318068DB60186' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_E9D31806B03A8386' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_E9D31806B75292CA' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_E9D31806C92FA040' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_E9D31806CC5890D7' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_E9D31806EC8B7ADE' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_E9D31806F8BD700D' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_E9D31806FFAE5AB7' => [
                'TABLE_NAME' => 'igt_igt4_transaction',
                'REFERENCED_TABLE_NAME' => 'country',
            ],
            'FK_EE084A6184042C99' => [
                'TABLE_NAME' => 'tam_tax_audit_risk',
                'REFERENCED_TABLE_NAME' => 'tax_type',
            ],
            'FK_EE084A61896DBBDE' => [
                'TABLE_NAME' => 'tam_tax_audit_risk',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_EE084A618DB60186' => [
                'TABLE_NAME' => 'tam_tax_audit_risk',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_EE084A618F53E2DC' => [
                'TABLE_NAME' => 'tam_tax_audit_risk',
                'REFERENCED_TABLE_NAME' => 'tam_risk_type',
            ],
            'FK_EE084A61B03A8386' => [
                'TABLE_NAME' => 'tam_tax_audit_risk',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_EE084A61EC8B7ADE' => [
                'TABLE_NAME' => 'tam_tax_audit_risk',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_EE084A61F8BD700D' => [
                'TABLE_NAME' => 'tam_tax_audit_risk',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_EFBF973E896DBBDE' => [
                'TABLE_NAME' => 'dtm_item_country_report',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_EFBF973EB03A8386' => [
                'TABLE_NAME' => 'dtm_item_country_report',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_F4FCAB1516BC0D3' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_F4FCAB1534BA8930' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_F4FCAB153B08FB94' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_F4FCAB153F571310' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_F4FCAB15473DBD53' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'pricing_method',
            ],
            'FK_F4FCAB155F8C47AD' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'trace_id',
            ],
            'FK_F4FCAB15896DBBDE' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_F4FCAB158DB60186' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'task',
            ],
            'FK_F4FCAB159AECFCEF' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_F4FCAB15B03A8386' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'user',
            ],
            'FK_F4FCAB15BF161B31' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_F4FCAB15CBFD6503' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_F4FCAB15D41C16E3' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'igt_instrument_id_type',
            ],
            'FK_F4FCAB15EC8B7ADE' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'period',
            ],
            'FK_F4FCAB15F20D281A' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'base_local_group_money',
            ],
            'FK_F4FCAB15F8BD700D' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'unit',
            ],
            'FK_F4FCAB15FFAE5AB7' => [
                'TABLE_NAME' => 'igt_igt1_transaction',
                'REFERENCED_TABLE_NAME' => 'country',
            ],
            'FK_F5AA20BF1ED93D47' => [
                'TABLE_NAME' => 'saved_filter_user_group',
                'REFERENCED_TABLE_NAME' => 'user_group',
            ],
            'FK_F5AA20BFE64EFBA3' => [
                'TABLE_NAME' => 'saved_filter_user_group',
                'REFERENCED_TABLE_NAME' => 'saved_filter',
            ],
            'FK_F89F74A193CB796C' => [
                'TABLE_NAME' => 'tpm_country_by_country_report_section_file',
                'REFERENCED_TABLE_NAME' => 'file',
            ],
            'FK_F89F74A1FD3A71F1' => [
                'TABLE_NAME' => 'tpm_country_by_country_report_section_file',
                'REFERENCED_TABLE_NAME' => 'tpm_country_by_country_report_section',
            ],
        ];
    }
}
