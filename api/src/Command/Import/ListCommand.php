<?php

declare(strict_types=1);
namespace U2\Command\Import;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use U2\Entity\Import;
use U2\Repository\ImportRepository;

#[AsCommand(name: 'u2:import:list', description: 'Lists imports')]
class ListCommand extends Command
{
    public function __construct(private readonly ImportRepository $importRepository)
    {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $imports = $this->importRepository->findBy([], ['id' => 'DESC']);

        $io = new SymfonyStyle($input, $output);
        $this->printImportsList($imports, $io);

        return 0;
    }

    /**
     * @param Import[] $imports
     */
    private function printImportsList(array $imports, SymfonyStyle $io): void
    {
        $headers = [
            'ID',
            'Status',
            'Result',
            'Uploaded File',
            'Type',
            'Simulated',
            'Created by',
            'Created at',
            'Started at',
            'Completed at',
        ];

        $rows = array_map(
            static function (Import $import): array {
                $createdAt = $import->getCreatedAt();
                $startedAt = $import->getStartedAt();
                $completedAt = $import->getCompletedAt();

                return [
                    $import->getId(),
                    $import->getStatus(),
                    $import->getResult(),
                    $import->getReference(),
                    $import->getConfigurationKey(),
                    $import->isDryRun() ? 'Yes' : 'No',
                    $import->getUser(),
                    $createdAt->format('Y.m.d H:i'),
                    null !== $startedAt ? $startedAt->format('Y.m.d H:i') : '',
                    null !== $completedAt ? $completedAt->format('Y.m.d H:i') : '',
                ];
            },
            $imports
        );

        $io->table($headers, $rows);
    }
}
