<?php

declare(strict_types=1);
namespace U2\Command\Import;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use U2\Import\Configuration\Factory\Task\ConfigurationCollection;

#[AsCommand(name: 'u2:import:list-configurations', description: 'Lists available import configurations')]
class ListConfigurationsCommand extends Command
{
    public function __construct(
        private readonly ConfigurationCollection $configurationCollection,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->showConfigurationsList($output);

        return 0;
    }

    private function showConfigurationsList(OutputInterface $output): void
    {
        $output->writeln('Available configurations:');
        $configurations = $this->configurationCollection->getConfigurationIds();
        foreach ($configurations as $configuration) {
            $output->writeln("\t$configuration");
        }
    }
}
