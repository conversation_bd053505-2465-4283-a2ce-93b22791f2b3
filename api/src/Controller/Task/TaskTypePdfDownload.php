<?php

declare(strict_types=1);
namespace U2\Controller\Task;

use ApiPlatform\Metadata\HttpOperation;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Attribute\Route;
use U2\Controller\Helper;
use U2\Entity\StructuredDocumentInterface;
use U2\Entity\Task\TaskType\AbstractDocument;
use U2\Http\DownloadFilenameSanitizer;
use U2\Security\Voter\DocumentVoterAttributes;
use U2\Task\TaskTypeKnowledge;
use U2\TransferPricing\DocumentPdfRenderer;

#[Route(path: '/tasktype/{shortName}/{id}/pdf-download', name: 'u2_tasktype_pdfdownload', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
class TaskTypePdfDownload extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly Helper $controllerHelper,
        private readonly DocumentPdfRenderer $pdfRenderer,
        private readonly DownloadFilenameSanitizer $downloadFilenameSanitizer,
    ) {
    }

    public function __invoke(string $shortName, int $id): Response
    {
        $entityClass = TaskTypeKnowledge::resolveEntityClassByShortName($shortName);
        $entity = $this->entityManager->find($entityClass, $id);
        if (false === ($entity instanceof StructuredDocumentInterface)) {
            throw new NotFoundHttpException();
        }

        $this->controllerHelper->denyAccessUnlessGranted(DocumentVoterAttributes::viewContent, $entity, 'You do not have rights to download pdf of this entity');

        \assert($entity instanceof AbstractDocument);

        return new Response(
            $this->pdfRenderer->generatePdf($entity),
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => HeaderUtils::makeDisposition(
                    ResponseHeaderBag::DISPOSITION_INLINE,
                    $this->downloadFilenameSanitizer->sanitize("{$entity->getName()}.pdf"),
                    'document.pdf'
                ),
            ]
        );
    }
}
