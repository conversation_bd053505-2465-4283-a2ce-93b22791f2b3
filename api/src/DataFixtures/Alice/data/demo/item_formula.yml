U2\Entity\ItemFormula:
  item_formula_TR0110020P:
    __construct:
      item: '@item_TR0110020P'
      formulaString: '{PTR0110020}'

  item_formula_TR0120020P:
    __construct:
      item: '@item_TR0120020P'
      formulaString: '{PTR0120020}'

  item_formula_TR0130010:
    __construct:
      item: '@item_TR0130010'
      formulaString: '{TR0110010}+({TR0110010}*{TR0120010})'

  item_formula_TR0130020:
    __construct:
      item: '@item_TR0130020'
      formulaString: '{TR0110020}+({TR0110020}*{TR0120020})'

  item_formula_TR0130020P:
    __construct:
      item: '@item_TR0130020P'
      formulaString: '{PTR0130020}'

  item_formula_TR0140020P:
    __construct:
      item: '@item_TR0140020P'
      formulaString: '{PTR0140020}'

  item_formula_TR0150010:
    __construct:
      item: '@item_TR0150010'
      formulaString: '{TR0180010}+{TR0140010}'

  item_formula_TR0150020:
    __construct:
      item: '@item_TR0150020'
      formulaString: '{TR0180020}+{TR0140020}'

  item_formula_TR0150020P:
    __construct:
      item: '@item_TR0150020P'
      formulaString: '{PTR0150020}'

  item_formula_TR0180010:
    __construct:
      item: '@item_TR0180010'
      formulaString: '({TX0160010}==1?-{TR0130010}*{TR0140010}:0)'

  item_formula_TR0180020:
    __construct:
      item: '@item_TR0180020'
      formulaString: '({TX0160020}==1?-{TR0130020}*{TR0140020}:0)'

  item_formula_TR0180020P:
    __construct:
      item: '@item_TR0180020P'
      formulaString: '{PTR0180020}'

  item_formula_TR0190010:
    __construct:
      item: '@item_TR0190010'
      formulaString: '{TR0130010}+{TR0150010}'

  item_formula_TR0190020:
    __construct:
      item: '@item_TR0190020'
      formulaString: '{TR0130020}+{TR0150020}'

  item_formula_TR0190020P:
    __construct:
      item: '@item_TR0190020P'
      formulaString: '{PTR0190020}'

  item_formula_TR0200110:
    __construct:
      item: '@item_TR0200110'
      formulaString: '0.270800'

  item_formula_TR3819010:
    __construct:
      item: '@item_TR3819010'
      formulaString: '({TX3818010}!=0&&{TX3110020}!=0?{TX3818010}/{TX3110020}:0)'

  item_formula_TR3910010:
    __construct:
      item: '@item_TR3910010'
      formulaString: '(({TX3110020}\<\>0?{TX3923020}/{TX3110020}:0))'

  item_formula_TX02801010:
    __construct:
      item: '@item_TX02801010'
      formulaString: '{TX71450010}'

  item_formula_TX02801020:
    __construct:
      item: '@item_TX02801020'
      formulaString: '{TX71450020}'

  item_formula_TX02802010:
    __construct:
      item: '@item_TX02802010'
      formulaString: '{TX71450030}'

  item_formula_TX02802020:
    __construct:
      item: '@item_TX02802020'
      formulaString: '{TX71450040}'

  item_formula_TX02803010:
    __construct:
      item: '@item_TX02803010'
      formulaString: '{TX02801010}+{TX02802010}'

  item_formula_TX02803020:
    __construct:
      item: '@item_TX02803020'
      formulaString: '{TX02801020}+{TX02802020}'

  item_formula_TX02805010:
    __construct:
      item: '@item_TX02805010'
      formulaString: '{TX02803010}+{PTX02840010}'

  item_formula_TX02805020:
    __construct:
      item: '@item_TX02805020'
      formulaString: '{TX02803020}+{PTX02840020}'

  item_formula_TX02830010:
    __construct:
      item: '@item_TX02830010'
      formulaString: '{TX71450101}+{TX71450112}'

  item_formula_TX02830020:
    __construct:
      item: '@item_TX02830020'
      formulaString: '{TX71450111}+{TX71450113}'

  item_formula_TX02840010:
    __construct:
      item: '@item_TX02840010'
      formulaString: '{PTX02840010}+{TX02831010}'

  item_formula_TX02840010P:
    __construct:
      item: '@item_TX02840010P'
      formulaString: '{PTX02840010}'

  item_formula_TX02840011P:
    __construct:
      item: '@item_TX02840011P'
      formulaString: '{PTX02840010}'

  item_formula_TX02840020:
    __construct:
      item: '@item_TX02840020'
      formulaString: '{PTX02840020}+{TX02831020}'

  item_formula_TX02840020P:
    __construct:
      item: '@item_TX02840020P'
      formulaString: '{PTX02840020}'

  item_formula_TX02840021P:
    __construct:
      item: '@item_TX02840021P'
      formulaString: '{PTX02840020}'

  item_formula_TX02841010:
    __construct:
      item: '@item_TX02841010'
      formulaString: '{TX02840011P}-{TX02840010P}'

  item_formula_TX02841020:
    __construct:
      item: '@item_TX02841020'
      formulaString: '{TX02840021P}-{TX02840020P}'

  item_formula_TX02850010:
    __construct:
      item: '@item_TX02850010'
      formulaString: '{TX02830010}+{TX02840010}'

  item_formula_TX02850020:
    __construct:
      item: '@item_TX02850020'
      formulaString: '{TX02830020}+{TX02840020}'

  item_formula_TX02870010:
    __construct:
      item: '@item_TX02870010'
      formulaString: '{TX02881210}+{TX02881410}+{TX02881610}+{TX02881810}'

  item_formula_TX02871010:
    __construct:
      item: '@item_TX02871010'
      formulaString: '{TX02870010}-{TX02881610}'

  item_formula_TX02880010:
    __construct:
      item: '@item_TX02880010'
      formulaString: '{TX02870010}+{PTX02880010}'

  item_formula_TX02880010P:
    __construct:
      item: '@item_TX02880010P'
      formulaString: '{PTX02880010}'

  item_formula_TX02880011P:
    __construct:
      item: '@item_TX02880011P'
      formulaString: '{PTX02880010}'

  item_formula_TX02881010:
    __construct:
      item: '@item_TX02881010'
      formulaString: '{TX02880011P}-{TX02880010P}'

  item_formula_TX3114030:
    __construct:
      item: '@item_TX3114030'
      formulaString: '{TX3110020}+{TX91450101}+{TX91450111}+{TX3113020}'

  item_formula_TX3117020:
    __construct:
      item: '@item_TX3117020'
      formulaString: '({TX3115020}+{TX3115021}+{TX3115022}+{TX3115023}+{TX3115024}+{TX3115025}+{TX3115026}+{TX3115027}+{TX3115028}+{TX3115029}+{TX3115121}+{TX3115122}+{TX3116020}+{TX3116021})'

  item_formula_TX3118030:
    __construct:
      item: '@item_TX3118030'
      formulaString: '-{TX3118020}'

  item_formula_TX3118031:
    __construct:
      item: '@item_TX3118031'
      formulaString: '-{TX3118021}'

  item_formula_TX3118032:
    __construct:
      item: '@item_TX3118032'
      formulaString: '-{TX3118022}'

  item_formula_TX3118033:
    __construct:
      item: '@item_TX3118033'
      formulaString: '-{TX3118023}'

  item_formula_TX3118034:
    __construct:
      item: '@item_TX3118034'
      formulaString: '-{TX3118024}'

  item_formula_TX3118035:
    __construct:
      item: '@item_TX3118035'
      formulaString: '-{TX3118025}'

  item_formula_TX3118036:
    __construct:
      item: '@item_TX3118036'
      formulaString: '-{TX3118026}'

  item_formula_TX3118037:
    __construct:
      item: '@item_TX3118037'
      formulaString: '-{TX3118027}'

  item_formula_TX3118038:
    __construct:
      item: '@item_TX3118038'
      formulaString: '-{TX3118028}'

  item_formula_TX3119030:
    __construct:
      item: '@item_TX3119030'
      formulaString: '-{TX3119020}'

  item_formula_TX3119031:
    __construct:
      item: '@item_TX3119031'
      formulaString: '-{TX3119021}'

  item_formula_TX3120020:
    __construct:
      item: '@item_TX3120020'
      formulaString: '({TX3118020}+{TX3118021}+{TX3118022}+{TX3118023}+{TX3118024}+{TX3118025}+{TX3118026}+{TX3118027}+{TX3118028}+{TX3119020}+{TX3119021})'

  item_formula_TX3120023:
    __construct:
      item: '@item_TX3120023'
      formulaString: '{TX3120021}+{TX3120022}'

  item_formula_TX3120026:
    __construct:
      item: '@item_TX3120026'
      formulaString: '{TX3120024}+{TX3120025}'

  item_formula_TX3120030:
    __construct:
      item: '@item_TX3120030'
      formulaString: '-{TX3120020}'

  item_formula_TX3120031:
    __construct:
      item: '@item_TX3120031'
      formulaString: '-{TX3120021}'

  item_formula_TX3120032:
    __construct:
      item: '@item_TX3120032'
      formulaString: '-{TX3120022}'

  item_formula_TX3120033:
    __construct:
      item: '@item_TX3120033'
      formulaString: '-{TX3120023}'

  item_formula_TX3120036:
    __construct:
      item: '@item_TX3120036'
      formulaString: '-{TX3120026}'

  item_formula_TX3121030:
    __construct:
      item: '@item_TX3121030'
      formulaString: '{TX3114030}+{TX3117020}+{TX3120030}+{TX3120033}+{TX3120036}'

  item_formula_TX3122020:
    __construct:
      item: '@item_TX3122020'
      formulaString: '({TX0160010}==1?{TX3219030}:0)'

  item_formula_TX3126020:
    __construct:
      item: '@item_TX3126020'
      formulaString: '-{TX3129030}'

  item_formula_TX3126030:
    __construct:
      item: '@item_TX3126030'
      formulaString: '{TX3121030}+{TX3121020}+{TX3122020}+{TX3123020}+{TX3124020}'

  item_formula_TX3128020:
    __construct:
      item: '@item_TX3128020'
      formulaString: '{TX3126020}+{TX3127020}'

  item_formula_TX3128021:
    __construct:
      item: '@item_TX3128021'
      formulaString: '-(({TX3120021}*{TR0191010})+({TX3120022}*{TR0192010}))'

  item_formula_TX3128030:
    __construct:
      item: '@item_TX3128030'
      formulaString: '{TX3126030}+{TX3125020}+{TX3125021}'

  item_formula_TX3129030:
    __construct:
      item: '@item_TX3129030'
      formulaString: '({TX3128030}\>0?-({TX3128030}*{TR0110010}):0)'

  item_formula_TX3130021:
    __construct:
      item: '@item_TX3130021'
      formulaString: '({TX3129020}+{TX3130020})'

  item_formula_TX3130025:
    __construct:
      item: '@item_TX3130025'
      formulaString: '({TX3130022}+{TX3130023}+{TX3130024})'

  item_formula_TX3130030:
    __construct:
      item: '@item_TX3130030'
      formulaString: '({TX3126020}+{TX3127020}\>0?({TX3126020}+{TX3127020})*-{TR0120010}:0)'

  item_formula_TX3132032:
    __construct:
      item: '@item_TX3132032'
      formulaString: '-{TX3130022}'

  item_formula_TX3132033:
    __construct:
      item: '@item_TX3132033'
      formulaString: '-{TX3130023}'

  item_formula_TX3132034:
    __construct:
      item: '@item_TX3132034'
      formulaString: '-{TX3130024}'

  item_formula_TX3132035:
    __construct:
      item: '@item_TX3132035'
      formulaString: '-{TX3130025}'

  item_formula_TX3133030:
    __construct:
      item: '@item_TX3133030'
      formulaString: '{TX3129030}+{TX3130030}+{TX3128021}+{TX3130021}+{TX3132035}'

  item_formula_TX3210010:
    __construct:
      item: '@item_TX3210010'
      formulaString: '({TR0150010}\>0?{TX3121030}:0)'

  item_formula_TX3212030:
    __construct:
      item: '@item_TX3212030'
      formulaString: '-{TX3250020}'

  item_formula_TX3214030:
    __construct:
      item: '@item_TX3214030'
      formulaString: '{TX3210020}+{TX3230020}+{TX3212030}+{TX3260020}'

  item_formula_TX3217030:
    __construct:
      item: '@item_TX3217030'
      formulaString: '-{TX3290020}'

  item_formula_TX3218030:
    __construct:
      item: '@item_TX3218030'
      formulaString: '{TX3214030}+{TX3270020}+{TX3280020}+{TX3217030}'

  item_formula_TX3219030:
    __construct:
      item: '@item_TX3219030'
      formulaString: '({TX3218030}\>0?-{TX3218030}*{TR0140010}:0)'

  item_formula_TX3230020:
    __construct:
      item: '@item_TX3230020'
      formulaString: '({TX3220020}+{TX3220021}+{TX3220022})'

  item_formula_TX3250020:
    __construct:
      item: '@item_TX3250020'
      formulaString: '({TX3240020}+{TX3240021}+{TX3240022})'

  item_formula_TX3311110:
    __construct:
      item: '@item_TX3311110'
      formulaString: '{TX3317011P}-{TX3317010P}'

  item_formula_TX3311120:
    __construct:
      item: '@item_TX3311120'
      formulaString: '{TX3317021P}-{TX3317020P}'

  item_formula_TX3311130:
    __construct:
      item: '@item_TX3311130'
      formulaString: '{TX3317031P}-{TX3317030P}'

  item_formula_TX3311140:
    __construct:
      item: '@item_TX3311140'
      formulaString: '{TX3317041P}-{TX3317040P}'

  item_formula_TX3313010:
    __construct:
      item: '@item_TX3313010'
      formulaString: '({PTX3317010}+{TX3311010}+{TX3312010})'

  item_formula_TX3313011:
    __construct:
      item: '@item_TX3313011'
      formulaString: '{TX3313010}'

  item_formula_TX3313020:
    __construct:
      item: '@item_TX3313020'
      formulaString: '({PTX3317020}+{TX3311020}+{TX3312020})'

  item_formula_TX3313030:
    __construct:
      item: '@item_TX3313030'
      formulaString: '({PTX3317030}+{TX3311030}+{TX3312030})'

  item_formula_TX3313040:
    __construct:
      item: '@item_TX3313040'
      formulaString: '({PTX3317040}+{TX3311040}+{TX3312040})'

  item_formula_TX3315010:
    __construct:
      item: '@item_TX3315010'
      formulaString: '({TX3128030}\<0?-{TX3128030}:0)'

  item_formula_TX3315020:
    __construct:
      item: '@item_TX3315020'
      formulaString: '({TX3214030}\<0?-{TX3214030}:0)'

  item_formula_TX3317010:
    __construct:
      item: '@item_TX3317010'
      formulaString: '{TX3125020}+{TX3315010}+{TX3316010}+{TX3313010}'

  item_formula_TX3317010P:
    __construct:
      item: '@item_TX3317010P'
      formulaString: '{PTX3317010}'

  item_formula_TX3317011P:
    __construct:
      item: '@item_TX3317011P'
      formulaString: '{TX3317010P}'

  item_formula_TX3317020:
    __construct:
      item: '@item_TX3317020'
      formulaString: '{TX3313020}+{TX3270020}+{TX3315020}+{TX3316020}'

  item_formula_TX3317020P:
    __construct:
      item: '@item_TX3317020P'
      formulaString: '{PTX3317020}'

  item_formula_TX3317021P:
    __construct:
      item: '@item_TX3317021P'
      formulaString: '{PTX3317020}'

  item_formula_TX3317030:
    __construct:
      item: '@item_TX3317030'
      formulaString: '{TX3313030}+{TX3118038}+{TX3115029}+{TX3316030}'

  item_formula_TX3317030P:
    __construct:
      item: '@item_TX3317030P'
      formulaString: '{PTX3317030}'

  item_formula_TX3317031P:
    __construct:
      item: '@item_TX3317031P'
      formulaString: '{PTX3317030}'

  item_formula_TX3317040:
    __construct:
      item: '@item_TX3317040'
      formulaString: '{TX3313040}+{TX3130023}+{TX3130022}+{TX3316040}'

  item_formula_TX3317040P:
    __construct:
      item: '@item_TX3317040P'
      formulaString: '{PTX3317040}'

  item_formula_TX3317041P:
    __construct:
      item: '@item_TX3317041P'
      formulaString: '{PTX3317040}'

  item_formula_TX3320010:
    __construct:
      item: '@item_TX3320010'
      formulaString: '{PTX3320010}+{TX3319010}'

  item_formula_TX3320010P:
    __construct:
      item: '@item_TX3320010P'
      formulaString: '{PTX3320010}'

  item_formula_TX3320011P:
    __construct:
      item: '@item_TX3320011P'
      formulaString: '{PTX3320010}'

  item_formula_TX3320020:
    __construct:
      item: '@item_TX3320020'
      formulaString: '{PTX3320020}+{TX3319020}'

  item_formula_TX3320020P:
    __construct:
      item: '@item_TX3320020P'
      formulaString: '{PTX3320020}'

  item_formula_TX3320021P:
    __construct:
      item: '@item_TX3320021P'
      formulaString: '{PTX3320020}'

  item_formula_TX3320030:
    __construct:
      item: '@item_TX3320030'
      formulaString: '{PTX3320030}+{TX3319030}'

  item_formula_TX3320030P:
    __construct:
      item: '@item_TX3320030P'
      formulaString: '{PTX3320030}'

  item_formula_TX3320031P:
    __construct:
      item: '@item_TX3320031P'
      formulaString: '{PTX3320030}'

  item_formula_TX3320040:
    __construct:
      item: '@item_TX3320040'
      formulaString: '{PTX3320040}+{TX3319040}'

  item_formula_TX3320040P:
    __construct:
      item: '@item_TX3320040P'
      formulaString: '{PTX3320040}'

  item_formula_TX3320041P:
    __construct:
      item: '@item_TX3320041P'
      formulaString: '{PTX3320040}'

  item_formula_TX3320110:
    __construct:
      item: '@item_TX3320110'
      formulaString: '{TX3320011P}-{TX3320010P}'

  item_formula_TX3320120:
    __construct:
      item: '@item_TX3320120'
      formulaString: '{TX3320021P}-{TX3320020P}'

  item_formula_TX3320130:
    __construct:
      item: '@item_TX3320130'
      formulaString: '{TX3320031P}-{TX3320030P}'

  item_formula_TX3320140:
    __construct:
      item: '@item_TX3320140'
      formulaString: '{TX3320041P}-{TX3320040P}'

  item_formula_TX3321010:
    __construct:
      item: '@item_TX3321010'
      formulaString: '{TX3317010}+{TX3320010}'

  item_formula_TX3321020:
    __construct:
      item: '@item_TX3321020'
      formulaString: '{TX3317020}+{TX3320020}'

  item_formula_TX3321030:
    __construct:
      item: '@item_TX3321030'
      formulaString: '{TX3317030}+{TX3320030}'

  item_formula_TX3321040:
    __construct:
      item: '@item_TX3321040'
      formulaString: '{TX3317040}+{TX3320040}'

  item_formula_TX3334010:
    __construct:
      item: '@item_TX3334010'
      formulaString: '({TX3323010}+{TX3324010}+{TX3325010}+{TX3326010}+{TX3327010}+{TX3328010}+{TX3329010}+{TX3330010}+{TX3331010}+{TX3332010}+{TX3333010})'

  item_formula_TX3334020:
    __construct:
      item: '@item_TX3334020'
      formulaString: '({TX3323020}+{TX3324020}+{TX3325020}+{TX3326020}+{TX3327020}+{TX3328020}+{TX3329020}+{TX3330020}+{TX3331020}+{TX3332020}+{TX3333020})'

  item_formula_TX3334030:
    __construct:
      item: '@item_TX3334030'
      formulaString: '({TX3323030}+{TX3324030}+{TX3325030}+{TX3326030}+{TX3327030}+{TX3328030}+{TX3329030}+{TX3330030}+{TX3331030}+{TX3332030}+{TX3333030})'

  item_formula_TX3334040:
    __construct:
      item: '@item_TX3334040'
      formulaString: '({TX3323040}+{TX3324040}+{TX3325040}+{TX3326040}+{TX3327040}+{TX3328040}+{TX3329040}+{TX3330040}+{TX3331040}+{TX3332040}+{TX3333040})'

  item_formula_TX3410110:
    __construct:
      item: '@item_TX3410110'
      formulaString: '{TX3416021P}-{TX3416020P}'

  item_formula_TX3410120:
    __construct:
      item: '@item_TX3410120'
      formulaString: '{TX3416021P}-{TX3416020P}'

  item_formula_TX3410130:
    __construct:
      item: '@item_TX3410130'
      formulaString: '{TX3417041P}-{TX3417040P}'

  item_formula_TX3410140:
    __construct:
      item: '@item_TX3410140'
      formulaString: '{TX3417041P}-{TX3417040P}'

  item_formula_TX3413020:
    __construct:
      item: '@item_TX3413020'
      formulaString: '-{TX3414020}+(({TX3414020}+{TX3415040})\>-({TX3133030})?{TX3414020}+{TX3415040}+({TX3133030}):0)'

  item_formula_TX3414040:
    __construct:
      item: '@item_TX3414040'
      formulaString: '-{TX3415040}+(({TX3414020}+{TX3415040})\<-({TX3133030})?{TX3414020}+{TX3415040}+({TX3133030}):0)'

  item_formula_TX3416020:
    __construct:
      item: '@item_TX3416020'
      formulaString: '({PTX3416020}+{TX3411020}+{TX3412020}+{TX3412220}+{TX3412420}+{TX3412620}+{TX3413020}+{TX3414020}+{TX3415020})'

  item_formula_TX3416020P:
    __construct:
      item: '@item_TX3416020P'
      formulaString: '{PTX3416020}'

  item_formula_TX3416021P:
    __construct:
      item: '@item_TX3416021P'
      formulaString: '{PTX3416020}'

  item_formula_TX3417040:
    __construct:
      item: '@item_TX3417040'
      formulaString: '({PTX3417040}+{TX3411040}+{TX3412040}+{TX3412240}+{TX3412440}+{TX3412640}+{TX3414040}+{TX3415040}+{TX3416040})'

  item_formula_TX3417040P:
    __construct:
      item: '@item_TX3417040P'
      formulaString: '{PTX3417040}'

  item_formula_TX3417041P:
    __construct:
      item: '@item_TX3417041P'
      formulaString: '{PTX3417040}'

  item_formula_TX3417110:
    __construct:
      item: '@item_TX3417110'
      formulaString: '{TX3423021P}-{TX3423020P}'

  item_formula_TX3417120:
    __construct:
      item: '@item_TX3417120'
      formulaString: '{TX3423021P}-{TX3423020P}'

  item_formula_TX3417130:
    __construct:
      item: '@item_TX3417130'
      formulaString: '{TX3425041P}-{TX3425040P}'

  item_formula_TX3417140:
    __construct:
      item: '@item_TX3417140'
      formulaString: '{TX3425041P}-{TX3425040P}'

  item_formula_TX3420020:
    __construct:
      item: '@item_TX3420020'
      formulaString: '-{TX3421020}+({TX3421020}+{TX3423040}\>-{TX3219030}?({TX3421020}+{TX3423040}+{TX3219030}):0)'

  item_formula_TX3422040:
    __construct:
      item: '@item_TX3422040'
      formulaString: '-{TX3423040}+({TX3421020}+{TX3423040}\<-{TX3219030}?({TX3421020}+{TX3423040}+{TX3219030}):0)'

  item_formula_TX3423020:
    __construct:
      item: '@item_TX3423020'
      formulaString: '({PTX3423020}+{TX3418020}+{TX3419020}+{TX3419220}+{TX3419420}+{TX3419620}+{TX3420020}+{TX3421020}+{TX3422020})'

  item_formula_TX3423020P:
    __construct:
      item: '@item_TX3423020P'
      formulaString: '{PTX3423020}'

  item_formula_TX3423021P:
    __construct:
      item: '@item_TX3423021P'
      formulaString: '{PTX3423020}'

  item_formula_TX3425040:
    __construct:
      item: '@item_TX3425040'
      formulaString: '({PTX3425040}+{TX3419040}+{TX3420040}+{TX3420240}+{TX3420440}+{TX3420640}+{TX3422040}+{TX3423040}+{TX3424040})'

  item_formula_TX3425040P:
    __construct:
      item: '@item_TX3425040P'
      formulaString: '{PTX3425040}'

  item_formula_TX3425041P:
    __construct:
      item: '@item_TX3425041P'
      formulaString: '{PTX3425040}'

  item_formula_TX3521010:
    __construct:
      item: '@item_TX3521010'
      formulaString: '{TX3416020}+{TX3423020}'

  item_formula_TX3521020:
    __construct:
      item: '@item_TX3521020'
      formulaString: '{TX3417040}+{TX3425040}'

  item_formula_TX3530010:
    __construct:
      item: '@item_TX3530010'
      formulaString: '({TX42770090}+{TX42770100}+{TX42770110}+{TX42770120}\>=0?{TX42770090}+{TX42770100}+{TX42770110}+{TX42770120}:0)+{TX3531116}'

  item_formula_TX3530020:
    __construct:
      item: '@item_TX3530020'
      formulaString: '({TX42770090}+{TX42770100}+{TX42770110}+{TX42770120}\<0?{TX42770090}+{TX42770100}+{TX42770110}+{TX42770120}:0)+{TX3531126}'

  item_formula_TX3540020:
    __construct:
      item: '@item_TX3540020'
      formulaString: '{TX3550020}+{TX3560020}'

  item_formula_TX3550010:
    __construct:
      item: '@item_TX3550010'
      formulaString: '{TX3560010}+{TX3570010}+{TX3580010}+{TX02871010}'

  item_formula_TX3550020:
    __construct:
      item: '@item_TX3550020'
      formulaString: '{TX42770100}+{TX42770120}'

  item_formula_TX3560010:
    __construct:
      item: '@item_TX3560010'
      formulaString: '-(({TX3817010}-{TX3815010}-{TX3816210}-{TX3816610}))'

  item_formula_TX3560020:
    __construct:
      item: '@item_TX3560020'
      formulaString: '({TX91450090}*{TR0190020})-({TX91450095}*{PTR0190020})'

  item_formula_TX3570010:
    __construct:
      item: '@item_TX3570010'
      formulaString: '-(({TX3815010}+{TX3816210}+{TX3816610}))'

  item_formula_TX3571010:
    __construct:
      item: '@item_TX3571010'
      formulaString: '{TX3560010}+{TX3570010}'

  item_formula_TX3580010:
    __construct:
      item: '@item_TX3580010'
      formulaString: '{TX42770160}+{TX42770170}+{TX42770189}'

  item_formula_TX3810010:
    __construct:
      item: '@item_TX3810010'
      formulaString: '-{TX3129030}-{TX3130030}-{TX3129020}-{TX3130020}'

  item_formula_TX3810110:
    __construct:
      item: '@item_TX3810110'
      formulaString: '-{TX3128021}'

  item_formula_TX3812020:
    __construct:
      item: '@item_TX3812020'
      formulaString: '-{TX42770170}'

  item_formula_TX3813010:
    __construct:
      item: '@item_TX3813010'
      formulaString: '-({TX3123020})'

  item_formula_TX3813011:
    __construct:
      item: '@item_TX3813011'
      formulaString: '{TX3130022}+{TX3130023}'

  item_formula_TX3813020:
    __construct:
      item: '@item_TX3813020'
      formulaString: '({TX3813021}+{TX3816022}+{TX3922016}+{TX3812020})'

  item_formula_TX3813021:
    __construct:
      item: '@item_TX3813021'
      formulaString: '-{TX42711189}'

  item_formula_TX3815010:
    __construct:
      item: '@item_TX3815010'
      formulaString: '-{TX3219030}'

  item_formula_TX3816010:
    __construct:
      item: '@item_TX3816010'
      formulaString: '-(({TX3412020}+{TX3412040}))'

  item_formula_TX3816022:
    __construct:
      item: '@item_TX3816022'
      formulaString: '-({TX42770189}+{TX42770160}-{TX42711189})'

  item_formula_TX3816210:
    __construct:
      item: '@item_TX3816210'
      formulaString: '-(({TX3419020}+{TX3420040}))'

  item_formula_TX3816410:
    __construct:
      item: '@item_TX3816410'
      formulaString: '-(({TX3412420}+{TX3412620}+{TX3412440}+{TX3412640}))'

  item_formula_TX3816610:
    __construct:
      item: '@item_TX3816610'
      formulaString: '-(({TX3419420}+{TX3419620}+{TX3420440}+{TX3420640}))'

  item_formula_TX3817010:
    __construct:
      item: '@item_TX3817010'
      formulaString: '({TX3810010}+{TX3810110}+{TX3129020}+{TX3812010}+{TX3813010}+{TX3813011}+{TX3130024}+{TX3815010}+{TX3816010}+{TX3816210}+{TX3816410}+{TX3816610})'

  item_formula_TX3818010:
    __construct:
      item: '@item_TX3818010'
      formulaString: '{TX3817010}+{TX3813020}'

  item_formula_TX3912020:
    __construct:
      item: '@item_TX3912020'
      formulaString: '({TX3110020}*{TR0200110})'

  item_formula_TX3913011:
    __construct:
      item: '@item_TX3913011'
      formulaString: '{TX3110020}*{TR0190010}'

  item_formula_TX3913012:
    __construct:
      item: '@item_TX3913012'
      formulaString: '-{TX3110020}*{TR0200110}'

  item_formula_TX3913013:
    __construct:
      item: '@item_TX3913013'
      formulaString: '-(({TX3120021}*{TR0190010})-({TX3120021}*{TR0191010})+({TX3120022}*{TR0190010})-({TX3120022}*{TR0192010}))'

  item_formula_TX3913020:
    __construct:
      item: '@item_TX3913020'
      formulaString: '({TX3913011}+{TX3913012}+{TX3913013})'

  item_formula_TX3914011:
    __construct:
      item: '@item_TX3914011'
      formulaString: '{TX3122020}*{TR0130010}'

  item_formula_TX3914012:
    __construct:
      item: '@item_TX3914012'
      formulaString: '-{TX71450070}'

  item_formula_TX3914014:
    __construct:
      item: '@item_TX3914014'
      formulaString: '-{TX42680170}'

  item_formula_TX3914015:
    __construct:
      item: '@item_TX3914015'
      formulaString: '-{TX42690170}'

  item_formula_TX3914016:
    __construct:
      item: '@item_TX3914016'
      formulaString: '-{TX42700170}'

  item_formula_TX3914017:
    __construct:
      item: '@item_TX3914017'
      formulaString: '-({TX3412040}+{TX3420040})-({TX3412020}+{TX3419020})'

  item_formula_TX3914018:
    __construct:
      item: '@item_TX3914018'
      formulaString: '-{TX3312040}'

  item_formula_TX3914020:
    __construct:
      item: '@item_TX3914020'
      formulaString: '({TX3914011}+{TX3914012}+{TX3914014}+{TX3914015}+{TX3914016}+{TX3914018}+{TX3816010}+{TX3816210}+{TX3816410}+{TX3816610})'

  item_formula_TX3915011:
    __construct:
      item: '@item_TX3915011'
      formulaString: '{TX91450111}*{TR0190010}'

  item_formula_TX3915012:
    __construct:
      item: '@item_TX3915012'
      formulaString: '{TX3113020}*{TR0190010}'

  item_formula_TX3915020:
    __construct:
      item: '@item_TX3915020'
      formulaString: '({TX3915011}+{TX3915012})'

  item_formula_TX3916011:
    __construct:
      item: '@item_TX3916011'
      formulaString: '-{TX71450060}'

  item_formula_TX3916012:
    __construct:
      item: '@item_TX3916012'
      formulaString: '{TX91450101}*({TR0190010}-{TR0190020})'

  item_formula_TX3916013:
    __construct:
      item: '@item_TX3916013'
      formulaString: '-({TX42710160}+{TX42780160})+(({TX3315010}+{TX3314010})*({TR0130010}-{TR0130020}))+(({TX3315020}+{TX3314020})*({TR0150010}-{TR0150020}))+(({TX3315030}+{TX3314030})*({TR0190010}-{TR0190020}))'

  item_formula_TX3916020:
    __construct:
      item: '@item_TX3916020'
      formulaString: '({TX3916011}+{TX3916012}+{TX3916013})'

  item_formula_TX3917011:
    __construct:
      item: '@item_TX3917011'
      formulaString: '{TX3130024}'

  item_formula_TX3917012:
    __construct:
      item: '@item_TX3917012'
      formulaString: '-{TX3123020}*(1-{TR0130010})'

  item_formula_TX3917020:
    __construct:
      item: '@item_TX3917020'
      formulaString: '({TX3917011}+{TX3917012})'

  item_formula_TX3918011:
    __construct:
      item: '@item_TX3918011'
      formulaString: '({TX3117020}-{TX3115029})*{TR0190010}'

  item_formula_TX3918012:
    __construct:
      item: '@item_TX3918012'
      formulaString: '0'

  item_formula_TX3918020:
    __construct:
      item: '@item_TX3918020'
      formulaString: '({TX3918011}+{TX3918012})'

  item_formula_TX3919011:
    __construct:
      item: '@item_TX3919011'
      formulaString: '({TX3120030}-{TX3118038})*{TR0190010}'

  item_formula_TX3919020:
    __construct:
      item: '@item_TX3919020'
      formulaString: '({TX3919011}+{TX3919012})'

  item_formula_TX3920011:
    __construct:
      item: '@item_TX3920011'
      formulaString: '{TX3230020}*{TR0150010}'

  item_formula_TX3920012:
    __construct:
      item: '@item_TX3920012'
      formulaString: '{TX3212030}*{TR0150010}'

  item_formula_TX3920013:
    __construct:
      item: '@item_TX3920013'
      formulaString: '{TX3280020}*{TR0150010}'

  item_formula_TX3920014:
    __construct:
      item: '@item_TX3920014'
      formulaString: '{TX3217030}*{TR0150010}'

  item_formula_TX3920020:
    __construct:
      item: '@item_TX3920020'
      formulaString: '({TX3920011}+{TX3920012}+{TX3920013}+{TX3920014}+{TX3922014})'

  item_formula_TX3921011:
    __construct:
      item: '@item_TX3921011'
      formulaString: '-{TX3316010}*{TR0130020}'

  item_formula_TX3921012:
    __construct:
      item: '@item_TX3921012'
      formulaString: '-{TX3319010}*{TR0130020}'

  item_formula_TX3921013:
    __construct:
      item: '@item_TX3921013'
      formulaString: '-{TX3316020}*{TR0150020}'

  item_formula_TX3921014:
    __construct:
      item: '@item_TX3921014'
      formulaString: '-{TX3319020}*{TR0150020}'

  item_formula_TX3921015:
    __construct:
      item: '@item_TX3921015'
      formulaString: '-{TX3316030}*({TR0130020}+0.75*{TR0150020})'

  item_formula_TX3921016:
    __construct:
      item: '@item_TX3921016'
      formulaString: '-{TX3319030}*({TR0130020}+0.75*{TR0150020})'

  item_formula_TX3921017:
    __construct:
      item: '@item_TX3921017'
      formulaString: '-{TX02831010}'

  item_formula_TX3921018:
    __construct:
      item: '@item_TX3921018'
      formulaString: '-{TX3316040}'

  item_formula_TX3921019:
    __construct:
      item: '@item_TX3921019'
      formulaString: '-{TX3319040}'

  item_formula_TX3921020:
    __construct:
      item: '@item_TX3921020'
      formulaString: '({TX3921011}+{TX3921012}+{TX3921013}+{TX3921014}+{TX3921015}+{TX3921016}+{TX3921018}+{TX3921019}+{TX3921017})'

  item_formula_TX3922011:
    __construct:
      item: '@item_TX3922011'
      formulaString: '{TX3124020}*{TR0130010}'

  item_formula_TX3922012:
    __construct:
      item: '@item_TX3922012'
      formulaString: '{TX3260020}*{TR0150010}'

  item_formula_TX3922013:
    __construct:
      item: '@item_TX3922013'
      formulaString: '{TX3127020}*{TR0120010}'

  item_formula_TX3922014:
    __construct:
      item: '@item_TX3922014'
      formulaString: '({TX3210020}-{TX3121030})*{TR0150010}'

  item_formula_TX3922015:
    __construct:
      item: '@item_TX3922015'
      formulaString: '({TX3115029}+{TX3118038})*25/100*({TR0150010}-({TR0150010}-{TR0150020}))'

  item_formula_TX3922016:
    __construct:
      item: '@item_TX3922016'
      formulaString: '-{TX02870010}'

  item_formula_TX3922017:
    __construct:
      item: '@item_TX3922017'
      formulaString: '{TX3125021}*{TR0130010}'

  item_formula_TX3922018:
    __construct:
      item: '@item_TX3922018'
      formulaString: '{TX3120036}*{TR0190010}'

  item_formula_TX3922020:
    __construct:
      item: '@item_TX3922020'
      formulaString: '({TX3922011}+{TX3922012}+{TX3922013}+{TX3922015}+{TX3922017}+{TX3922018})'

  item_formula_TX3923020:
    __construct:
      item: '@item_TX3923020'
      formulaString: '({TX3912020}+{TX3913020}+{TX3914020}+{TX3915020}+{TX3916020}+{TX3917020}+{TX3918020}+{TX3919020}+{TX3920020}+{TX3921020}+{TX3922020}+{TX3922016})'

  item_formula_TX3924020:
    __construct:
      item: '@item_TX3924020'
      formulaString: '{TX3818010}'

  item_formula_TX3925020:
    __construct:
      item: '@item_TX3925020'
      formulaString: '{TX3924020}-{TX3923020}'

  item_formula_TX42680010:
    __construct:
      item: '@item_TX42680010'
      formulaString: '{PTX3317010}*{PTR0130020}'

  item_formula_TX42680011:
    __construct:
      item: '@item_TX42680011'
      formulaString: '{PTX3317010}*{PTR0130020}'

  item_formula_TX42680090:
    __construct:
      item: '@item_TX42680090'
      formulaString: '{TX3317010}*{TR0130020}'

  item_formula_TX42680150:
    __construct:
      item: '@item_TX42680150'
      formulaString: '{TX42680011}-{TX42680010}'

  item_formula_TX42680160:
    __construct:
      item: '@item_TX42680160'
      formulaString: '({PTX3317010}+{TX3312010})*({TR0130020}-{PTR0130020})'

  item_formula_TX42680170:
    __construct:
      item: '@item_TX42680170'
      formulaString: '{TX3312010}*{PTR0130020}'

  item_formula_TX42680180:
    __construct:
      item: '@item_TX42680180'
      formulaString: '{TX3311010}*{TR0130020}'

  item_formula_TX42680189:
    __construct:
      item: '@item_TX42680189'
      formulaString: '({TX3125020}+{TX3315010}+{TX3316010})*{TR0130020}'

  item_formula_TX42690010:
    __construct:
      item: '@item_TX42690010'
      formulaString: '{PTX3317020}*{PTR0150020}'

  item_formula_TX42690011:
    __construct:
      item: '@item_TX42690011'
      formulaString: '{PTX3317020}*{PTR0150020}'

  item_formula_TX42690090:
    __construct:
      item: '@item_TX42690090'
      formulaString: '{TX3317020}*{TR0150020}'

  item_formula_TX42690150:
    __construct:
      item: '@item_TX42690150'
      formulaString: '{TX42690011}-{TX42690010}'

  item_formula_TX42690160:
    __construct:
      item: '@item_TX42690160'
      formulaString: '({PTX3317020}+{TX3312020})*({TR0150020}-{PTR0150020})'

  item_formula_TX42690170:
    __construct:
      item: '@item_TX42690170'
      formulaString: '{TX3312020}*{PTR0150020}'

  item_formula_TX42690180:
    __construct:
      item: '@item_TX42690180'
      formulaString: '{TX3311020}*{TR0150020}'

  item_formula_TX42690189:
    __construct:
      item: '@item_TX42690189'
      formulaString: '({TX3314020}+{TX3315020}+{TX3316020})*{TR0150020}'

  item_formula_TX42700010:
    __construct:
      item: '@item_TX42700010'
      formulaString: '{PTX3317030}*({PTR0130020}+0.75*{PTR0150020})'

  item_formula_TX42700011:
    __construct:
      item: '@item_TX42700011'
      formulaString: '{PTX3317030}*({PTR0130020}+0.75*{PTR0150020})'

  item_formula_TX42700090:
    __construct:
      item: '@item_TX42700090'
      formulaString: '{TX3317030}*({TR0130020}+0.75*{TR0150020})'

  item_formula_TX42700150:
    __construct:
      item: '@item_TX42700150'
      formulaString: '{TX42700011}-{TX42700010}'

  item_formula_TX42700160:
    __construct:
      item: '@item_TX42700160'
      formulaString: '({PTX3317030}+{TX3312030})*(({TR0130020}+0.75*{TR0150020})-({PTR0130020}+0.75*{PTR0150020}))'

  item_formula_TX42700170:
    __construct:
      item: '@item_TX42700170'
      formulaString: '{TX3312030}*({PTR0130020}+0.75*{PTR0150020})'

  item_formula_TX42700180:
    __construct:
      item: '@item_TX42700180'
      formulaString: '{TX3311030}*({PTR0130020}+0.75*{PTR0150020})'

  item_formula_TX42700189:
    __construct:
      item: '@item_TX42700189'
      formulaString: '({TX3314030}+{TX3315030}+{TX3316030})*({TR0130020}+0.75*{TR0150020})'

  item_formula_TX42710010:
    __construct:
      item: '@item_TX42710010'
      formulaString: '({TX42680010}+{TX42690010}+{TX42700010}+{TX3317040P})'

  item_formula_TX42710011:
    __construct:
      item: '@item_TX42710011'
      formulaString: '({TX42680010}+{TX42690010}+{TX42700010}+{TX3317040P})'

  item_formula_TX42710090:
    __construct:
      item: '@item_TX42710090'
      formulaString: '({TX42680090}+{TX42690090}+{TX42700090}+{TX3321040})'

  item_formula_TX42710150:
    __construct:
      item: '@item_TX42710150'
      formulaString: '{TX42710011}-{TX42710010}'

  item_formula_TX42710160:
    __construct:
      item: '@item_TX42710160'
      formulaString: '{TX42680160}+{TX42690160}+{TX42700160}'

  item_formula_TX42710170:
    __construct:
      item: '@item_TX42710170'
      formulaString: '{TX42680170}+{TX42690170}+{TX42700170}+{TX3312040}'

  item_formula_TX42710180:
    __construct:
      item: '@item_TX42710180'
      formulaString: '{TX42680180}+{TX42690180}+{TX42700180}+{TX3311040}'

  item_formula_TX42710189:
    __construct:
      item: '@item_TX42710189'
      formulaString: '{TX42680189}+{TX42690189}+{TX42700189}+{TX42711189}'

  item_formula_TX42711150:
    __construct:
      item: '@item_TX42711150'
      formulaString: '{TX3317041P}-{TX3317040P}'

  item_formula_TX42711189:
    __construct:
      item: '@item_TX42711189'
      formulaString: '{TX3130023}+{TX3130022}+{TX3316040}+{TX3319040}'

  item_formula_TX42720010:
    __construct:
      item: '@item_TX42720010'
      formulaString: '({PTX3320010})*{PTR0130020}'

  item_formula_TX42720011:
    __construct:
      item: '@item_TX42720011'
      formulaString: '({PTX3320010})*{PTR0130020}'

  item_formula_TX42720090:
    __construct:
      item: '@item_TX42720090'
      formulaString: '{TX3320010}*{TR0130020}'

  item_formula_TX42720150:
    __construct:
      item: '@item_TX42720150'
      formulaString: '{TX42720011}-{TX42720010}'

  item_formula_TX42720160:
    __construct:
      item: '@item_TX42720160'
      formulaString: '{PTX3320010}*({TR0130020}-{PTR0130020})'

  item_formula_TX42720189:
    __construct:
      item: '@item_TX42720189'
      formulaString: '{TX3319010}*{TR0130020}'

  item_formula_TX42730010:
    __construct:
      item: '@item_TX42730010'
      formulaString: '({PTX3320020})*{PTR0150020}'

  item_formula_TX42730011:
    __construct:
      item: '@item_TX42730011'
      formulaString: '({PTX3320020})*{PTR0150020}'

  item_formula_TX42730090:
    __construct:
      item: '@item_TX42730090'
      formulaString: '{TX3320020}*{TR0150020}'

  item_formula_TX42730150:
    __construct:
      item: '@item_TX42730150'
      formulaString: '{TX42730011}-{TX42730010}'

  item_formula_TX42730160:
    __construct:
      item: '@item_TX42730160'
      formulaString: '{PTX3320020}*({TR0150020}-{PTR0150020})'

  item_formula_TX42730189:
    __construct:
      item: '@item_TX42730189'
      formulaString: '{TX3319020}*{TR0150020}'

  item_formula_TX42740010:
    __construct:
      item: '@item_TX42740010'
      formulaString: '({PTX3320030})*({PTR0130020}+0.75*{PTR0150020})'

  item_formula_TX42740011:
    __construct:
      item: '@item_TX42740011'
      formulaString: '({PTX3320030})*({PTR0130020}+0.75*{PTR0150020})'

  item_formula_TX42740090:
    __construct:
      item: '@item_TX42740090'
      formulaString: '{TX3320030}*({TR0130020}+0.75*{TR0150020})'

  item_formula_TX42740150:
    __construct:
      item: '@item_TX42740150'
      formulaString: '{TX42740011}-{TX42740010}'

  item_formula_TX42740160:
    __construct:
      item: '@item_TX42740160'
      formulaString: '{PTX3320030}*(({TR0130020}+0.75*{TR0150020})-({PTR0130020}+0.75*{PTR0150020}))'

  item_formula_TX42740189:
    __construct:
      item: '@item_TX42740189'
      formulaString: '{TX3319030}*({TR0130020}+0.75*{TR0150020})'

  item_formula_TX42750011:
    __construct:
      item: '@item_TX42750011'
      formulaString: '{TX02840010P}'

  item_formula_TX42750150:
    __construct:
      item: '@item_TX42750150'
      formulaString: '{TX42750011}-{TX02840010P}'

  item_formula_TX42760011:
    __construct:
      item: '@item_TX42760011'
      formulaString: '{TX02840020P}'

  item_formula_TX42760150:
    __construct:
      item: '@item_TX42760150'
      formulaString: '{TX42760011}-{TX02840020P}'

  item_formula_TX42770010:
    __construct:
      item: '@item_TX42770010'
      formulaString: '{PTX42770090}'

  item_formula_TX42770011:
    __construct:
      item: '@item_TX42770011'
      formulaString: '{PTX42770090}'

  item_formula_TX42770020:
    __construct:
      item: '@item_TX42770020'
      formulaString: '{PTX42770100}'

  item_formula_TX42770021:
    __construct:
      item: '@item_TX42770021'
      formulaString: '{PTX42770100}'

  item_formula_TX42770030:
    __construct:
      item: '@item_TX42770030'
      formulaString: '{PTX42770110}'

  item_formula_TX42770031:
    __construct:
      item: '@item_TX42770031'
      formulaString: '{PTX42770110}'

  item_formula_TX42770040:
    __construct:
      item: '@item_TX42770040'
      formulaString: '{PTX42770120}'

  item_formula_TX42770041:
    __construct:
      item: '@item_TX42770041'
      formulaString: '{PTX42770120}'

  item_formula_TX42770090:
    __construct:
      item: '@item_TX42770090'
      formulaString: '{TX71450101}+{TX42710090}+{TX42780090}'

  item_formula_TX42770100:
    __construct:
      item: '@item_TX42770100'
      formulaString: '{TX71450111}+{TX02840020}'

  item_formula_TX42770110:
    __construct:
      item: '@item_TX42770110'
      formulaString: '{TX71450112}'

  item_formula_TX42770150:
    __construct:
      item: '@item_TX42770150'
      formulaString: '{TX42770011}+{TX42770021}+{TX42770031}+{TX42770041}-{TX42770010}-{TX42770020}-{TX42770030}-{TX42770040}'

  item_formula_TX42770160:
    __construct:
      item: '@item_TX42770160'
      formulaString: '{TX71450060}+{TX42710160}+{TX42780160}'

  item_formula_TX42770170:
    __construct:
      item: '@item_TX42770170'
      formulaString: '{TX71450070}+{TX42670170}+{TX42710170}'

  item_formula_TX42770180:
    __construct:
      item: '@item_TX42770180'
      formulaString: '{TX71450080}+{TX42670180}+{TX42710180}+{TX02831020}'

  item_formula_TX42770189:
    __construct:
      item: '@item_TX42770189'
      formulaString: '{TX71450090}+{TX42670189}+{TX42710189}+{TX42780189}'

  item_formula_TX42780010:
    __construct:
      item: '@item_TX42780010'
      formulaString: '({TX42720010}+{TX42730010}+{TX42740010}+{TX02840010P})'

  item_formula_TX42780090:
    __construct:
      item: '@item_TX42780090'
      formulaString: '({TX42720090}+{TX42730090}+{TX42740090}+{TX02840010})'

  item_formula_TX42780150:
    __construct:
      item: '@item_TX42780150'
      formulaString: '{TX42720150}+{TX42730150}+{TX42740150}+{TX42750150}+{TX42760150}'

  item_formula_TX42780160:
    __construct:
      item: '@item_TX42780160'
      formulaString: '{TX42720160}+{TX42730160}+{TX42740160}+{TX42750160}'

  item_formula_TX42780189:
    __construct:
      item: '@item_TX42780189'
      formulaString: '{TX42720189}+{TX42730189}+{TX42740189}+{TX02831010}'

  item_formula_TX42900020:
    __construct:
      item: '@item_TX42900020'
      formulaString: '{PTX42900100}'

  item_formula_TX42900040:
    __construct:
      item: '@item_TX42900040'
      formulaString: '{PTX42900120}'

  item_formula_TX42900100:
    __construct:
      item: '@item_TX42900100'
      formulaString: '{TX42770090}+{TX42770100}'

  item_formula_TX42900120:
    __construct:
      item: '@item_TX42900120'
      formulaString: '{TX42770110}+{TX42770120}'

  item_formula_TX42910020:
    __construct:
      item: '@item_TX42910020'
      formulaString: '{PTX42910100}'

  item_formula_TX42910040:
    __construct:
      item: '@item_TX42910040'
      formulaString: '{PTX42910120}'

  item_formula_TX42910100:
    __construct:
      item: '@item_TX42910100'
      formulaString: '(({TX42900100}+{TX42900120})\>=0?{TX42900120}+{TX3531116}:-{TX42900100}+{TX3531116})'

  item_formula_TX42910120:
    __construct:
      item: '@item_TX42910120'
      formulaString: '(({TX42900100}+{TX42900120})\<0?{TX42900100}+{TX3531126}:-{TX42900120}+{TX3531126})'

  item_formula_TX42920020:
    __construct:
      item: '@item_TX42920020'
      formulaString: '{PTX42920100}'

  item_formula_TX42920040:
    __construct:
      item: '@item_TX42920040'
      formulaString: '{PTX42920120}'

  item_formula_TX42920100:
    __construct:
      item: '@item_TX42920100'
      formulaString: '{TX42900100}+{TX42910100}'

  item_formula_TX42920120:
    __construct:
      item: '@item_TX42920120'
      formulaString: '{TX42900120}+{TX42910120}'

  item_formula_TX71200010:
    __construct:
      item: '@item_TX71200010'
      formulaString: '{PTX71200101}'

  item_formula_TX71200011:
    __construct:
      item: '@item_TX71200011'
      formulaString: '{PTX71200101}'

  item_formula_TX71200020:
    __construct:
      item: '@item_TX71200020'
      formulaString: '{PTX71200111}'

  item_formula_TX71200021:
    __construct:
      item: '@item_TX71200021'
      formulaString: '{PTX71200111}'

  item_formula_TX71200030:
    __construct:
      item: '@item_TX71200030'
      formulaString: '{PTX71200112}'

  item_formula_TX71200031:
    __construct:
      item: '@item_TX71200031'
      formulaString: '{PTX71200112}'

  item_formula_TX71200040:
    __construct:
      item: '@item_TX71200040'
      formulaString: '{PTX71200113}'

  item_formula_TX71200041:
    __construct:
      item: '@item_TX71200041'
      formulaString: '{PTX71200113}'

  item_formula_TX71200050:
    __construct:
      item: '@item_TX71200050'
      formulaString: '{TX71200011}+{TX71200021}+{TX71200031}+{TX71200041}-{TX71200010}-{TX71200020}-{TX71200030}-{TX71200040}'

  item_formula_TX71200060:
    __construct:
      item: '@item_TX71200060'
      formulaString: '({TR0190020}-{TR0190020P})*({TX91200070P}+{TX91200090})'

  item_formula_TX71200070:
    __construct:
      item: '@item_TX71200070'
      formulaString: '{TX91200090}*{PTR0190020}'

  item_formula_TX71200080:
    __construct:
      item: '@item_TX71200080'
      formulaString: '({TX91200060}-{PTX91200060}+{TX91200095})*{TR0190020}'

  item_formula_TX71200090:
    __construct:
      item: '@item_TX71200090'
      formulaString: '{TX91200101}*{TR0190020}'

  item_formula_TX71200101:
    __construct:
      item: '@item_TX71200101'
      formulaString: '({TX91200070}\>=0?{TX91200070}*{TR0190020}:0)'

  item_formula_TX71200111:
    __construct:
      item: '@item_TX71200111'
      formulaString: '({TX91200060}\>0?{TX91200060}*{TR0190020}:0)'

  item_formula_TX71200112:
    __construct:
      item: '@item_TX71200112'
      formulaString: '({TX91200070}\<0?{TX91200070}*{TR0190020}:0)'

  item_formula_TX71200113:
    __construct:
      item: '@item_TX71200113'
      formulaString: '({TX91200060}\<0?{TX91200060}*{TR0190020}:0)'

  item_formula_TX71210010:
    __construct:
      item: '@item_TX71210010'
      formulaString: '{PTX71210101}'

  item_formula_TX71210011:
    __construct:
      item: '@item_TX71210011'
      formulaString: '{TX71210010}'

  item_formula_TX71210020:
    __construct:
      item: '@item_TX71210020'
      formulaString: '{PTX71210111}'

  item_formula_TX71210021:
    __construct:
      item: '@item_TX71210021'
      formulaString: '{TX71210020}'

  item_formula_TX71210030:
    __construct:
      item: '@item_TX71210030'
      formulaString: '{PTX71210112}'

  item_formula_TX71210031:
    __construct:
      item: '@item_TX71210031'
      formulaString: '{TX71210030}'

  item_formula_TX71210040:
    __construct:
      item: '@item_TX71210040'
      formulaString: '{PTX71210113}'

  item_formula_TX71210041:
    __construct:
      item: '@item_TX71210041'
      formulaString: '{TX71210040}'

  item_formula_TX71210050:
    __construct:
      item: '@item_TX71210050'
      formulaString: '{TX71210011}+{TX71210021}+{TX71210031}+{TX71210041}-{TX71210010}-{TX71210020}-{TX71210030}-{TX71210040}'

  item_formula_TX71210060:
    __construct:
      item: '@item_TX71210060'
      formulaString: '({TR0190020}-{PTR0190020})*({PTX91210070}+{TX91210090})'

  item_formula_TX71210070:
    __construct:
      item: '@item_TX71210070'
      formulaString: '{TX91210090}*{PTR0190020}'

  item_formula_TX71210080:
    __construct:
      item: '@item_TX71210080'
      formulaString: '({TX91210060}-{PTX91210060}+{TX91210095})*{TR0190020}'

  item_formula_TX71210090:
    __construct:
      item: '@item_TX71210090'
      formulaString: '{TX91210101}*{TR0190020}'

  item_formula_TX71210101:
    __construct:
      item: '@item_TX71210101'
      formulaString: '({TX91210070}\>=0?{TX91210070}*{TR0190020}:0)'

  item_formula_TX71210111:
    __construct:
      item: '@item_TX71210111'
      formulaString: '({TX91210060}\>0?{TX91210060}*{TR0190020}:0)'

  item_formula_TX71210112:
    __construct:
      item: '@item_TX71210112'
      formulaString: '({TX91210070}\<0?{TX91210070}*{TR0190020}:0)'

  item_formula_TX71210113:
    __construct:
      item: '@item_TX71210113'
      formulaString: '({TX91210060}\<0?{TX91210060}*{TR0190020}:0)'

  item_formula_TX71220010:
    __construct:
      item: '@item_TX71220010'
      formulaString: '{PTX71220101}'

  item_formula_TX71220011:
    __construct:
      item: '@item_TX71220011'
      formulaString: '{PTX71220101}'

  item_formula_TX71220020:
    __construct:
      item: '@item_TX71220020'
      formulaString: '{PTX71220111}'

  item_formula_TX71220021:
    __construct:
      item: '@item_TX71220021'
      formulaString: '{PTX71220111}'

  item_formula_TX71220030:
    __construct:
      item: '@item_TX71220030'
      formulaString: '{PTX71220112}'

  item_formula_TX71220031:
    __construct:
      item: '@item_TX71220031'
      formulaString: '{PTX71220112}'

  item_formula_TX71220040:
    __construct:
      item: '@item_TX71220040'
      formulaString: '{PTX71220113}'

  item_formula_TX71220041:
    __construct:
      item: '@item_TX71220041'
      formulaString: '{PTX71220113}'

  item_formula_TX71220050:
    __construct:
      item: '@item_TX71220050'
      formulaString: '{TX71220011}+{TX71220021}+{TX71220031}+{TX71220041}-{TX71220010}-{TX71220020}-{TX71220030}-{TX71220040}'

  item_formula_TX71220060:
    __construct:
      item: '@item_TX71220060'
      formulaString: '({TR0190020}-{PTR0190020})*({PTX91220070}+{TX91220090})'

  item_formula_TX71220070:
    __construct:
      item: '@item_TX71220070'
      formulaString: '{TX91220090}*{PTR0190020}'

  item_formula_TX71220080:
    __construct:
      item: '@item_TX71220080'
      formulaString: '({TX91220060}-{PTX91220060}+{TX91220095})*{TR0190020}'

  item_formula_TX71220090:
    __construct:
      item: '@item_TX71220090'
      formulaString: '{TX91220101}*{TR0190020}'

  item_formula_TX71220101:
    __construct:
      item: '@item_TX71220101'
      formulaString: '({TX91220070}\>=0?{TX91220070}*{TR0190020}:0)'

  item_formula_TX71220111:
    __construct:
      item: '@item_TX71220111'
      formulaString: '({TX91220060}\>0?{TX91220060}*{TR0190020}:0)'

  item_formula_TX71220112:
    __construct:
      item: '@item_TX71220112'
      formulaString: '({TX91220070}\<0?{TX91220070}*{TR0190020}:0)'

  item_formula_TX71220113:
    __construct:
      item: '@item_TX71220113'
      formulaString: '({TX91220060}\<0?{TX91220060}*{TR0190020}:0)'

  item_formula_TX71230010:
    __construct:
      item: '@item_TX71230010'
      formulaString: '{PTX71230101}'

  item_formula_TX71230011:
    __construct:
      item: '@item_TX71230011'
      formulaString: '{PTX71230101}'

  item_formula_TX71230020:
    __construct:
      item: '@item_TX71230020'
      formulaString: '{PTX71230111}'

  item_formula_TX71230021:
    __construct:
      item: '@item_TX71230021'
      formulaString: '{PTX71230111}'

  item_formula_TX71230030:
    __construct:
      item: '@item_TX71230030'
      formulaString: '{PTX71230112}'

  item_formula_TX71230031:
    __construct:
      item: '@item_TX71230031'
      formulaString: '{PTX71230112}'

  item_formula_TX71230040:
    __construct:
      item: '@item_TX71230040'
      formulaString: '{PTX71230113}'

  item_formula_TX71230041:
    __construct:
      item: '@item_TX71230041'
      formulaString: '{PTX71230113}'

  item_formula_TX71230050:
    __construct:
      item: '@item_TX71230050'
      formulaString: '{TX71230011}+{TX71230021}+{TX71230031}+{TX71230041}-{TX71230010}-{TX71230020}-{TX71230030}-{TX71230040}'

  item_formula_TX71230060:
    __construct:
      item: '@item_TX71230060'
      formulaString: '({TR0190020}-{PTR0190020})*({PTX91230070}+{TX91230090})'

  item_formula_TX71230070:
    __construct:
      item: '@item_TX71230070'
      formulaString: '{TX91230090}*{PTR0190020}'

  item_formula_TX71230080:
    __construct:
      item: '@item_TX71230080'
      formulaString: '({TX91230060}-{PTX91230060}+{TX91230095})*{TR0190020}'

  item_formula_TX71230090:
    __construct:
      item: '@item_TX71230090'
      formulaString: '{TX91230101}*{TR0190020}'

  item_formula_TX71230101:
    __construct:
      item: '@item_TX71230101'
      formulaString: '({TX91230070}\>=0?{TX91230070}*{TR0190020}:0)'

  item_formula_TX71230111:
    __construct:
      item: '@item_TX71230111'
      formulaString: '({TX91230060}\>=0?{TX91230060}*{TR0190020}:0)'

  item_formula_TX71230112:
    __construct:
      item: '@item_TX71230112'
      formulaString: '({TX91230070}\<0?{TX91230070}*{TR0190020}:0)'

  item_formula_TX71230113:
    __construct:
      item: '@item_TX71230113'
      formulaString: '({TX91230060}\<0?{TX91230060}*{TR0190020}:0)'

  item_formula_TX71240010:
    __construct:
      item: '@item_TX71240010'
      formulaString: '{PTX71240101}'

  item_formula_TX71240011:
    __construct:
      item: '@item_TX71240011'
      formulaString: '{PTX71240101}'

  item_formula_TX71240020:
    __construct:
      item: '@item_TX71240020'
      formulaString: '{PTX71240111}'

  item_formula_TX71240021:
    __construct:
      item: '@item_TX71240021'
      formulaString: '{PTX71240111}'

  item_formula_TX71240030:
    __construct:
      item: '@item_TX71240030'
      formulaString: '{PTX71240112}'

  item_formula_TX71240031:
    __construct:
      item: '@item_TX71240031'
      formulaString: '{PTX71240112}'

  item_formula_TX71240040:
    __construct:
      item: '@item_TX71240040'
      formulaString: '{PTX71240113}'

  item_formula_TX71240041:
    __construct:
      item: '@item_TX71240041'
      formulaString: '{PTX71240113}'

  item_formula_TX71240050:
    __construct:
      item: '@item_TX71240050'
      formulaString: '{TX71240011}+{TX71240021}+{TX71240031}+{TX71240041}-{TX71240010}-{TX71240020}-{TX71240030}-{TX71240040}'

  item_formula_TX71240060:
    __construct:
      item: '@item_TX71240060'
      formulaString: '({TR0190020}-{PTR0190020})*({PTX91240070}+{TX91240090})'

  item_formula_TX71240070:
    __construct:
      item: '@item_TX71240070'
      formulaString: '{TX91240090}*{PTR0190020}'

  item_formula_TX71240080:
    __construct:
      item: '@item_TX71240080'
      formulaString: '({TX91240060}-{PTX91240060}+{TX91240095})*{TR0190020}'

  item_formula_TX71240090:
    __construct:
      item: '@item_TX71240090'
      formulaString: '{TX91240101}*{TR0190020}'

  item_formula_TX71240101:
    __construct:
      item: '@item_TX71240101'
      formulaString: '({TX91240070}\>=0?{TX91240070}*{TR0190020}:0)'

  item_formula_TX71240111:
    __construct:
      item: '@item_TX71240111'
      formulaString: '({TX91240060}\>0?{TX91240060}*{TR0190020}:0)'

  item_formula_TX71240112:
    __construct:
      item: '@item_TX71240112'
      formulaString: '({TX91240070}\<0?{TX91240070}*{TR0190020}:0)'

  item_formula_TX71240113:
    __construct:
      item: '@item_TX71240113'
      formulaString: '({TX91240060}\<0?{TX91240060}*{TR0190020}:0)'

  item_formula_TX71250010:
    __construct:
      item: '@item_TX71250010'
      formulaString: '{PTX71250101}'

  item_formula_TX71250011:
    __construct:
      item: '@item_TX71250011'
      formulaString: '{PTX71250101}'

  item_formula_TX71250020:
    __construct:
      item: '@item_TX71250020'
      formulaString: '{PTX71250111}'

  item_formula_TX71250021:
    __construct:
      item: '@item_TX71250021'
      formulaString: '{PTX71250111}'

  item_formula_TX71250030:
    __construct:
      item: '@item_TX71250030'
      formulaString: '{PTX71250112}'

  item_formula_TX71250031:
    __construct:
      item: '@item_TX71250031'
      formulaString: '{PTX71250112}'

  item_formula_TX71250040:
    __construct:
      item: '@item_TX71250040'
      formulaString: '{PTX71250113}'

  item_formula_TX71250041:
    __construct:
      item: '@item_TX71250041'
      formulaString: '{PTX71250113}'

  item_formula_TX71250050:
    __construct:
      item: '@item_TX71250050'
      formulaString: '{TX71250011}+{TX71250021}+{TX71250031}+{TX71250041}'

  item_formula_TX71250060:
    __construct:
      item: '@item_TX71250060'
      formulaString: '({TR0190020}-{TR0190020P})*({PTX91250070}+{TX91250090})'

  item_formula_TX71250070:
    __construct:
      item: '@item_TX71250070'
      formulaString: '{TX91250090}*{PTR0190020}'

  item_formula_TX71250080:
    __construct:
      item: '@item_TX71250080'
      formulaString: '({TX91250060}-{PTX91250060}+{TX91250095})*{TR0190020}'

  item_formula_TX71250090:
    __construct:
      item: '@item_TX71250090'
      formulaString: '{TX91250101}*{TR0190020}'

  item_formula_TX71250101:
    __construct:
      item: '@item_TX71250101'
      formulaString: '({TX91250070}\>=0?{TX91250070}*{TR0190020}:0)'

  item_formula_TX71250111:
    __construct:
      item: '@item_TX71250111'
      formulaString: '({TX91250060}\>0?{TX91250060}*{TR0190020}:0)'

  item_formula_TX71250112:
    __construct:
      item: '@item_TX71250112'
      formulaString: '({TX91250070}\<0?{TX91250070}*{TR0190020}:0)'

  item_formula_TX71250113:
    __construct:
      item: '@item_TX71250113'
      formulaString: '({TX91250060}\<0?{TX91250060}*{TR0190020}:0)'

  item_formula_TX71260010:
    __construct:
      item: '@item_TX71260010'
      formulaString: '{PTX71260101}'

  item_formula_TX71260011:
    __construct:
      item: '@item_TX71260011'
      formulaString: '{PTX71260101}'

  item_formula_TX71260020:
    __construct:
      item: '@item_TX71260020'
      formulaString: '{PTX71260111}'

  item_formula_TX71260021:
    __construct:
      item: '@item_TX71260021'
      formulaString: '{PTX71260111}'

  item_formula_TX71260030:
    __construct:
      item: '@item_TX71260030'
      formulaString: '{PTX71260112}'

  item_formula_TX71260031:
    __construct:
      item: '@item_TX71260031'
      formulaString: '{PTX71260112}'

  item_formula_TX71260040:
    __construct:
      item: '@item_TX71260040'
      formulaString: '{PTX71260113}'

  item_formula_TX71260041:
    __construct:
      item: '@item_TX71260041'
      formulaString: '{PTX71260113}'

  item_formula_TX71260050:
    __construct:
      item: '@item_TX71260050'
      formulaString: '{TX71260011}+{TX71260021}+{TX71260031}+{TX71260041}'

  item_formula_TX71260060:
    __construct:
      item: '@item_TX71260060'
      formulaString: '({TR0190020}-{PTR0190020})*({PTX91260070}+{TX91260090})'

  item_formula_TX71260070:
    __construct:
      item: '@item_TX71260070'
      formulaString: '{TX91260090}*{PTR0190020}'

  item_formula_TX71260080:
    __construct:
      item: '@item_TX71260080'
      formulaString: '({TX91260060}-{PTX91260060}+{TX91260095})*{TR0190020}'

  item_formula_TX71260090:
    __construct:
      item: '@item_TX71260090'
      formulaString: '{TX91260101}*{TR0190020}'

  item_formula_TX71260101:
    __construct:
      item: '@item_TX71260101'
      formulaString: '({TX91260070}\>=0?{TX91260070}*{TR0190020}:0)'

  item_formula_TX71260111:
    __construct:
      item: '@item_TX71260111'
      formulaString: '({TX91260060}\>0?{TX91260060}*{TR0190020}:0)'

  item_formula_TX71260112:
    __construct:
      item: '@item_TX71260112'
      formulaString: '({TX91260070}\<0?{TX91260070}*{TR0190020}:0)'

  item_formula_TX71260113:
    __construct:
      item: '@item_TX71260113'
      formulaString: '({TX91260060}\<0?{TX91260060}*{TR0190020}:0)'

  item_formula_TX71300010:
    __construct:
      item: '@item_TX71300010'
      formulaString: '{PTX71300101}'

  item_formula_TX71300011:
    __construct:
      item: '@item_TX71300011'
      formulaString: '{PTX71300101}'

  item_formula_TX71300020:
    __construct:
      item: '@item_TX71300020'
      formulaString: '{PTX71300111}'

  item_formula_TX71300021:
    __construct:
      item: '@item_TX71300021'
      formulaString: '{PTX71300111}'

  item_formula_TX71300030:
    __construct:
      item: '@item_TX71300030'
      formulaString: '{PTX71300112}'

  item_formula_TX71300031:
    __construct:
      item: '@item_TX71300031'
      formulaString: '{PTX71300112}'

  item_formula_TX71300040:
    __construct:
      item: '@item_TX71300040'
      formulaString: '{PTX71300113}'

  item_formula_TX71300041:
    __construct:
      item: '@item_TX71300041'
      formulaString: '{PTX71300113}'

  item_formula_TX71300050:
    __construct:
      item: '@item_TX71300050'
      formulaString: '{TX71300011}+{TX71300021}+{TX71300031}+{TX71300041}'

  item_formula_TX71300060:
    __construct:
      item: '@item_TX71300060'
      formulaString: '({TR0190020}-{PTR0190020})*({PTX91300070}+{TX91300090})'

  item_formula_TX71300070:
    __construct:
      item: '@item_TX71300070'
      formulaString: '{TX91300090}*{PTR0190020}'

  item_formula_TX71300080:
    __construct:
      item: '@item_TX71300080'
      formulaString: '({TX91300060}-{PTX91300060}+{TX91300095})*{TR0190020}'

  item_formula_TX71300090:
    __construct:
      item: '@item_TX71300090'
      formulaString: '{TX91300101}*{TR0190020}'

  item_formula_TX71300101:
    __construct:
      item: '@item_TX71300101'
      formulaString: '({TX91300070}\>=0?{TX91300070}*{TR0190020}:0)'

  item_formula_TX71300111:
    __construct:
      item: '@item_TX71300111'
      formulaString: '({TX91300060}\>0?{TX91300060}*{TR0190020}:0)'

  item_formula_TX71300112:
    __construct:
      item: '@item_TX71300112'
      formulaString: '({TX91300070}\<0?{TX91300070}*{TR0190020}:0)'

  item_formula_TX71300113:
    __construct:
      item: '@item_TX71300113'
      formulaString: '({TX91300060}\<0?{TX91300060}*{TR0190020}:0)'

  item_formula_TX71310010:
    __construct:
      item: '@item_TX71310010'
      formulaString: '{PTX71310101}'

  item_formula_TX71310011:
    __construct:
      item: '@item_TX71310011'
      formulaString: '{PTX71310101}'

  item_formula_TX71310020:
    __construct:
      item: '@item_TX71310020'
      formulaString: '{PTX71310111}'

  item_formula_TX71310021:
    __construct:
      item: '@item_TX71310021'
      formulaString: '{PTX71310111}'

  item_formula_TX71310030:
    __construct:
      item: '@item_TX71310030'
      formulaString: '{PTX71310112}'

  item_formula_TX71310031:
    __construct:
      item: '@item_TX71310031'
      formulaString: '{PTX71310112}'

  item_formula_TX71310040:
    __construct:
      item: '@item_TX71310040'
      formulaString: '{PTX71310113}'

  item_formula_TX71310041:
    __construct:
      item: '@item_TX71310041'
      formulaString: '{PTX71310113}'

  item_formula_TX71310050:
    __construct:
      item: '@item_TX71310050'
      formulaString: '{TX71310011}+{TX71310021}+{TX71310031}+{TX71310041}'

  item_formula_TX71310060:
    __construct:
      item: '@item_TX71310060'
      formulaString: '({TR0190020}-{PTR0190020})*({PTX91310070}+{TX91310090})'

  item_formula_TX71310070:
    __construct:
      item: '@item_TX71310070'
      formulaString: '{TX91310090}*{PTR0190020}'

  item_formula_TX71310080:
    __construct:
      item: '@item_TX71310080'
      formulaString: '({TX91310060}-{PTX91310060}+{TX91310095})*{TR0190020}'

  item_formula_TX71310090:
    __construct:
      item: '@item_TX71310090'
      formulaString: '{TX91310101}*{TR0190020}'

  item_formula_TX71310101:
    __construct:
      item: '@item_TX71310101'
      formulaString: '({TX91310070}\>=0?{TX91310070}*{TR0190020}:0)'

  item_formula_TX71310111:
    __construct:
      item: '@item_TX71310111'
      formulaString: '({TX91310060}\>0?{TX91310060}*{TR0190020}:0)'

  item_formula_TX71310112:
    __construct:
      item: '@item_TX71310112'
      formulaString: '({TX91310070}\<0?{TX91310070}*{TR0190020}:0)'

  item_formula_TX71310113:
    __construct:
      item: '@item_TX71310113'
      formulaString: '({TX91310060}\<0?{TX91310060}*{TR0190020}:0)'

  item_formula_TX71320010:
    __construct:
      item: '@item_TX71320010'
      formulaString: '{PTX71320101}'

  item_formula_TX71320011:
    __construct:
      item: '@item_TX71320011'
      formulaString: '{PTX71320101}'

  item_formula_TX71320020:
    __construct:
      item: '@item_TX71320020'
      formulaString: '{PTX71320111}'

  item_formula_TX71320021:
    __construct:
      item: '@item_TX71320021'
      formulaString: '{PTX71320111}'

  item_formula_TX71320030:
    __construct:
      item: '@item_TX71320030'
      formulaString: '{PTX71320112}'

  item_formula_TX71320031:
    __construct:
      item: '@item_TX71320031'
      formulaString: '{PTX71320112}'

  item_formula_TX71320040:
    __construct:
      item: '@item_TX71320040'
      formulaString: '{PTX71320113}'

  item_formula_TX71320041:
    __construct:
      item: '@item_TX71320041'
      formulaString: '{PTX71320113}'

  item_formula_TX71320050:
    __construct:
      item: '@item_TX71320050'
      formulaString: '{TX71320011}+{TX71320021}+{TX71320031}+{TX71320041}'

  item_formula_TX71320060:
    __construct:
      item: '@item_TX71320060'
      formulaString: '({TR0190020}-{PTR0190020})*({PTX91320070}+{TX91320090})'

  item_formula_TX71320070:
    __construct:
      item: '@item_TX71320070'
      formulaString: '{TX91320090}*{PTR0190020}'

  item_formula_TX71320080:
    __construct:
      item: '@item_TX71320080'
      formulaString: '({TX91320060}-{PTX91320060}+{TX91320095})*{TR0190020}'

  item_formula_TX71320090:
    __construct:
      item: '@item_TX71320090'
      formulaString: '{TX91320101}*{TR0190020}'

  item_formula_TX71320101:
    __construct:
      item: '@item_TX71320101'
      formulaString: '({TX91320070}\>=0?{TX91320070}*{TR0190020}:0)'

  item_formula_TX71320111:
    __construct:
      item: '@item_TX71320111'
      formulaString: '({TX91320060}\>0?{TX91320060}*{TR0190020}:0)'

  item_formula_TX71320112:
    __construct:
      item: '@item_TX71320112'
      formulaString: '({TX91320070}\<0?{TX91320070}*{TR0190020}:0)'

  item_formula_TX71320113:
    __construct:
      item: '@item_TX71320113'
      formulaString: '({TX91320060}\<0?{TX91320060}*{TR0190020}:0)'

  item_formula_TX71330010:
    __construct:
      item: '@item_TX71330010'
      formulaString: '{PTX71330101}'

  item_formula_TX71330011:
    __construct:
      item: '@item_TX71330011'
      formulaString: '{PTX71330101}'

  item_formula_TX71330020:
    __construct:
      item: '@item_TX71330020'
      formulaString: '{PTX71330111}'

  item_formula_TX71330021:
    __construct:
      item: '@item_TX71330021'
      formulaString: '{PTX71330111}'

  item_formula_TX71330030:
    __construct:
      item: '@item_TX71330030'
      formulaString: '{PTX71330112}'

  item_formula_TX71330031:
    __construct:
      item: '@item_TX71330031'
      formulaString: '{PTX71330112}'

  item_formula_TX71330040:
    __construct:
      item: '@item_TX71330040'
      formulaString: '{PTX71330113}'

  item_formula_TX71330041:
    __construct:
      item: '@item_TX71330041'
      formulaString: '{PTX71330113}'

  item_formula_TX71330050:
    __construct:
      item: '@item_TX71330050'
      formulaString: '{TX71330011}+{TX71330021}+{TX71330031}+{TX71330041}'

  item_formula_TX71330060:
    __construct:
      item: '@item_TX71330060'
      formulaString: '({TR0190020}-{PTR0190020})*({PTX91330070}+{TX91330090})'

  item_formula_TX71330070:
    __construct:
      item: '@item_TX71330070'
      formulaString: '{TX91330090}*{PTR0190020}'

  item_formula_TX71330080:
    __construct:
      item: '@item_TX71330080'
      formulaString: '({TX91330060}-{PTX91330060}+{TX91330095})*{TR0190020}'

  item_formula_TX71330090:
    __construct:
      item: '@item_TX71330090'
      formulaString: '{TX91330101}*{TR0190020}'

  item_formula_TX71330101:
    __construct:
      item: '@item_TX71330101'
      formulaString: '({TX91330070}\>=0?{TX91330070}*{TR0190020}:0)'

  item_formula_TX71330111:
    __construct:
      item: '@item_TX71330111'
      formulaString: '({TX91330060}\>0?{TX91330060}*{TR0190020}:0)'

  item_formula_TX71330112:
    __construct:
      item: '@item_TX71330112'
      formulaString: '({TX91330070}\<0?{TX91330070}*{TR0190020}:0)'

  item_formula_TX71330113:
    __construct:
      item: '@item_TX71330113'
      formulaString: '({TX91330060}\<0?{TX91330060}*{TR0190020}:0)'

  item_formula_TX71360010:
    __construct:
      item: '@item_TX71360010'
      formulaString: '{PTX71360101}'

  item_formula_TX71360011:
    __construct:
      item: '@item_TX71360011'
      formulaString: '{PTX71360101}'

  item_formula_TX71360020:
    __construct:
      item: '@item_TX71360020'
      formulaString: '{PTX71360111}'

  item_formula_TX71360021:
    __construct:
      item: '@item_TX71360021'
      formulaString: '{PTX71360111}'

  item_formula_TX71360030:
    __construct:
      item: '@item_TX71360030'
      formulaString: '{PTX71360112}'

  item_formula_TX71360031:
    __construct:
      item: '@item_TX71360031'
      formulaString: '{PTX71360112}'

  item_formula_TX71360040:
    __construct:
      item: '@item_TX71360040'
      formulaString: '{PTX71360113}'

  item_formula_TX71360041:
    __construct:
      item: '@item_TX71360041'
      formulaString: '{PTX71360113}'

  item_formula_TX71360050:
    __construct:
      item: '@item_TX71360050'
      formulaString: '{TX71360011}+{TX71360021}+{TX71360031}+{TX71360041}'

  item_formula_TX71360060:
    __construct:
      item: '@item_TX71360060'
      formulaString: '({TR0190020}-{PTR0190020})*({PTX91360070}+{TX91360090})'

  item_formula_TX71360070:
    __construct:
      item: '@item_TX71360070'
      formulaString: '{TX91360090}*{PTR0190020}'

  item_formula_TX71360080:
    __construct:
      item: '@item_TX71360080'
      formulaString: '({TX91360060}-{PTX91360060}+{TX91360095})*{TR0190020}'

  item_formula_TX71360090:
    __construct:
      item: '@item_TX71360090'
      formulaString: '{TX91360101}*{TR0190020}'

  item_formula_TX71360101:
    __construct:
      item: '@item_TX71360101'
      formulaString: '({TX91360070}\>=0?{TX91360070}*{TR0190020}:0)'

  item_formula_TX71360111:
    __construct:
      item: '@item_TX71360111'
      formulaString: '({TX91360060}\>0?{TX91360060}*{TR0190020}:0)'

  item_formula_TX71360112:
    __construct:
      item: '@item_TX71360112'
      formulaString: '({TX91360070}\<0?{TX91360070}*{TR0190020}:0)'

  item_formula_TX71360113:
    __construct:
      item: '@item_TX71360113'
      formulaString: '({TX91360060}\<0?{TX91360060}*{TR0190020}:0)'

  item_formula_TX71370010:
    __construct:
      item: '@item_TX71370010'
      formulaString: '{PTX71370101}'

  item_formula_TX71370011:
    __construct:
      item: '@item_TX71370011'
      formulaString: '{PTX71370101}'

  item_formula_TX71370020:
    __construct:
      item: '@item_TX71370020'
      formulaString: '{PTX71370111}'

  item_formula_TX71370021:
    __construct:
      item: '@item_TX71370021'
      formulaString: '{PTX71370111}'

  item_formula_TX71370030:
    __construct:
      item: '@item_TX71370030'
      formulaString: '{PTX71370112}'

  item_formula_TX71370031:
    __construct:
      item: '@item_TX71370031'
      formulaString: '{PTX71370112}'

  item_formula_TX71370040:
    __construct:
      item: '@item_TX71370040'
      formulaString: '{PTX71370113}'

  item_formula_TX71370041:
    __construct:
      item: '@item_TX71370041'
      formulaString: '{PTX71370113}'

  item_formula_TX71370050:
    __construct:
      item: '@item_TX71370050'
      formulaString: '{TX71370011}+{TX71370021}+{TX71370031}+{TX71370041}'

  item_formula_TX71370060:
    __construct:
      item: '@item_TX71370060'
      formulaString: '({TR0190020}-{PTR0190020})*({PTX91370070}+{TX91370090})'

  item_formula_TX71370070:
    __construct:
      item: '@item_TX71370070'
      formulaString: '{TX91370090}*{PTR0190020}'

  item_formula_TX71370080:
    __construct:
      item: '@item_TX71370080'
      formulaString: '({TX91370060}-{PTX91370060}+{TX91370095})*{TR0190020}'

  item_formula_TX71370090:
    __construct:
      item: '@item_TX71370090'
      formulaString: '{TX91370101}*{TR0190020}'

  item_formula_TX71370101:
    __construct:
      item: '@item_TX71370101'
      formulaString: '({TX91370070}\>=0?{TX91370070}*{TR0190020}:0)'

  item_formula_TX71370111:
    __construct:
      item: '@item_TX71370111'
      formulaString: '({TX91370060}\>0?{TX91370060}*{TR0190020}:0)'

  item_formula_TX71370112:
    __construct:
      item: '@item_TX71370112'
      formulaString: '({TX91370070}\<0?{TX91370070}*{TR0190020}:0)'

  item_formula_TX71370113:
    __construct:
      item: '@item_TX71370113'
      formulaString: '({TX91370060}\<0?{TX91370060}*{TR0190020}:0)'

  item_formula_TX71380010:
    __construct:
      item: '@item_TX71380010'
      formulaString: '{PTX71380101}'

  item_formula_TX71380011:
    __construct:
      item: '@item_TX71380011'
      formulaString: '{PTX71380101}'

  item_formula_TX71380020:
    __construct:
      item: '@item_TX71380020'
      formulaString: '{PTX71380111}'

  item_formula_TX71380021:
    __construct:
      item: '@item_TX71380021'
      formulaString: '{PTX71380111}'

  item_formula_TX71380030:
    __construct:
      item: '@item_TX71380030'
      formulaString: '{PTX71380112}'

  item_formula_TX71380031:
    __construct:
      item: '@item_TX71380031'
      formulaString: '{PTX71380112}'

  item_formula_TX71380040:
    __construct:
      item: '@item_TX71380040'
      formulaString: '{PTX71380113}'

  item_formula_TX71380041:
    __construct:
      item: '@item_TX71380041'
      formulaString: '{PTX71380113}'

  item_formula_TX71380050:
    __construct:
      item: '@item_TX71380050'
      formulaString: '{TX71380011}+{TX71380021}+{TX71380031}+{TX71380041}'

  item_formula_TX71380060:
    __construct:
      item: '@item_TX71380060'
      formulaString: '({TR0190020}-{PTR0190020})*({PTX91380070}+{TX91380090})'

  item_formula_TX71380070:
    __construct:
      item: '@item_TX71380070'
      formulaString: '{TX91380090}*{PTR0190020}'

  item_formula_TX71380080:
    __construct:
      item: '@item_TX71380080'
      formulaString: '({TX91380060}-{PTX91380060}+{TX91380095})*{TR0190020}'

  item_formula_TX71380090:
    __construct:
      item: '@item_TX71380090'
      formulaString: '{TX91380101}*{TR0190020}'

  item_formula_TX71380101:
    __construct:
      item: '@item_TX71380101'
      formulaString: '({TX91380070}\>=0?{TX91380070}*{TR0190020}:0)'

  item_formula_TX71380111:
    __construct:
      item: '@item_TX71380111'
      formulaString: '({TX91380060}\>0?{TX91380060}*{TR0190020}:0)'

  item_formula_TX71380112:
    __construct:
      item: '@item_TX71380112'
      formulaString: '({TX91380070}\<0?{TX91380070}*{TR0190020}:0)'

  item_formula_TX71380113:
    __construct:
      item: '@item_TX71380113'
      formulaString: '({TX91380060}\<0?{TX91380060}*{TR0190020}:0)'

  item_formula_TX71400010:
    __construct:
      item: '@item_TX71400010'
      formulaString: '{PTX71400101}'

  item_formula_TX71400011:
    __construct:
      item: '@item_TX71400011'
      formulaString: '{PTX71400101}'

  item_formula_TX71400020:
    __construct:
      item: '@item_TX71400020'
      formulaString: '{PTX71400111}'

  item_formula_TX71400021:
    __construct:
      item: '@item_TX71400021'
      formulaString: '{PTX71400111}'

  item_formula_TX71400030:
    __construct:
      item: '@item_TX71400030'
      formulaString: '{PTX71400112}'

  item_formula_TX71400031:
    __construct:
      item: '@item_TX71400031'
      formulaString: '{PTX71400112}'

  item_formula_TX71400040:
    __construct:
      item: '@item_TX71400040'
      formulaString: '{PTX71400113}'

  item_formula_TX71400041:
    __construct:
      item: '@item_TX71400041'
      formulaString: '{PTX71400113}'

  item_formula_TX71400050:
    __construct:
      item: '@item_TX71400050'
      formulaString: '{TX71400011}+{TX71400021}+{TX71400031}+{TX71400041}'

  item_formula_TX71400060:
    __construct:
      item: '@item_TX71400060'
      formulaString: '({TR0190020}-{PTR0190020})*({PTX91400070}+{TX91400090})'

  item_formula_TX71400070:
    __construct:
      item: '@item_TX71400070'
      formulaString: '{TX91400090}*{PTR0190020}'

  item_formula_TX71400080:
    __construct:
      item: '@item_TX71400080'
      formulaString: '({TX91400060}-{PTX91400060}+{TX91400095})*{TR0190020}'

  item_formula_TX71400090:
    __construct:
      item: '@item_TX71400090'
      formulaString: '{TX91400101}*{TR0190020}'

  item_formula_TX71400101:
    __construct:
      item: '@item_TX71400101'
      formulaString: '({TX91400070}\>=0?{TX91400070}*{TR0190020}:0)'

  item_formula_TX71400111:
    __construct:
      item: '@item_TX71400111'
      formulaString: '({TX91400060}\>0?{TX91400060}*{TR0190020}:0)'

  item_formula_TX71400112:
    __construct:
      item: '@item_TX71400112'
      formulaString: '({TX91400070}\<0?{TX91400070}*{TR0190020}:0)'

  item_formula_TX71400113:
    __construct:
      item: '@item_TX71400113'
      formulaString: '({TX91400060}\<0?{TX91400060}*{TR0190020}:0)'

  item_formula_TX71420010:
    __construct:
      item: '@item_TX71420010'
      formulaString: '{PTX71420101}'

  item_formula_TX71420011:
    __construct:
      item: '@item_TX71420011'
      formulaString: '{PTX71420101}'

  item_formula_TX71420020:
    __construct:
      item: '@item_TX71420020'
      formulaString: '{PTX71420111}'

  item_formula_TX71420021:
    __construct:
      item: '@item_TX71420021'
      formulaString: '{PTX71420111}'

  item_formula_TX71420030:
    __construct:
      item: '@item_TX71420030'
      formulaString: '{PTX71420112}'

  item_formula_TX71420031:
    __construct:
      item: '@item_TX71420031'
      formulaString: '{PTX71420112}'

  item_formula_TX71420040:
    __construct:
      item: '@item_TX71420040'
      formulaString: '{PTX71420113}'

  item_formula_TX71420041:
    __construct:
      item: '@item_TX71420041'
      formulaString: '{PTX71420113}'

  item_formula_TX71420050:
    __construct:
      item: '@item_TX71420050'
      formulaString: '{TX71420011}+{TX71420021}+{TX71420031}+{TX71420041}'

  item_formula_TX71420060:
    __construct:
      item: '@item_TX71420060'
      formulaString: '({TR0190020}-{PTR0190020})*({PTX91420070}+{TX91420090})'

  item_formula_TX71420070:
    __construct:
      item: '@item_TX71420070'
      formulaString: '{TX91420090}*{PTR0190020}'

  item_formula_TX71420080:
    __construct:
      item: '@item_TX71420080'
      formulaString: '({TX91420060}-{PTX91420060}+{TX91420095})*{TR0190020}'

  item_formula_TX71420090:
    __construct:
      item: '@item_TX71420090'
      formulaString: '{TX91420101}*{TR0190020}'

  item_formula_TX71420101:
    __construct:
      item: '@item_TX71420101'
      formulaString: '({TX91420070}\>=0?{TX91420070}*{TR0190020}:0)'

  item_formula_TX71420111:
    __construct:
      item: '@item_TX71420111'
      formulaString: '({TX91420060}\>0?{TX91420060}*{TR0190020}:0)'

  item_formula_TX71420112:
    __construct:
      item: '@item_TX71420112'
      formulaString: '({TX91420070}\<0?{TX91420070}*{TR0190020}:0)'

  item_formula_TX71420113:
    __construct:
      item: '@item_TX71420113'
      formulaString: '({TX91420060}\<0?{TX91420060}*{TR0190020}:0)'

  item_formula_TX71430010:
    __construct:
      item: '@item_TX71430010'
      formulaString: '{PTX71430101}'

  item_formula_TX71430011:
    __construct:
      item: '@item_TX71430011'
      formulaString: '{PTX71430101}'

  item_formula_TX71430020:
    __construct:
      item: '@item_TX71430020'
      formulaString: '{PTX71430111}'

  item_formula_TX71430021:
    __construct:
      item: '@item_TX71430021'
      formulaString: '{PTX71430111}'

  item_formula_TX71430030:
    __construct:
      item: '@item_TX71430030'
      formulaString: '{PTX71430112}'

  item_formula_TX71430031:
    __construct:
      item: '@item_TX71430031'
      formulaString: '{PTX71430112}'

  item_formula_TX71430040:
    __construct:
      item: '@item_TX71430040'
      formulaString: '{PTX71430113}'

  item_formula_TX71430041:
    __construct:
      item: '@item_TX71430041'
      formulaString: '{PTX71430113}'

  item_formula_TX71430050:
    __construct:
      item: '@item_TX71430050'
      formulaString: '{TX71430011}+{TX71430021}+{TX71430031}+{TX71430041}'

  item_formula_TX71430060:
    __construct:
      item: '@item_TX71430060'
      formulaString: '({TR0190020}-{PTR0190020})*({PTX91430070}+{TX91430090})'

  item_formula_TX71430070:
    __construct:
      item: '@item_TX71430070'
      formulaString: '{TX91430090}*{PTR0190020}'

  item_formula_TX71430080:
    __construct:
      item: '@item_TX71430080'
      formulaString: '({TX91430060}-{PTX91430060}+{TX91430095})*{TR0190020}'

  item_formula_TX71430090:
    __construct:
      item: '@item_TX71430090'
      formulaString: '{TX91430101}*{TR0190020}'

  item_formula_TX71430101:
    __construct:
      item: '@item_TX71430101'
      formulaString: '({TX91430070}\>=0?{TX91430070}*{TR0190020}:0)'

  item_formula_TX71430111:
    __construct:
      item: '@item_TX71430111'
      formulaString: '({TX91430060}\>0?{TX91430060}*{TR0190020}:0)'

  item_formula_TX71430112:
    __construct:
      item: '@item_TX71430112'
      formulaString: '({TX91430070}\<0?{TX91430070}*{TR0190020}:0)'

  item_formula_TX71430113:
    __construct:
      item: '@item_TX71430113'
      formulaString: '({TX91430060}\<0?{TX91430060}*{TR0190020}:0)'

  item_formula_TX71450010:
    __construct:
      item: '@item_TX71450010'
      formulaString: '{TX71200010}+{TX71210010}+{TX71230010}+{TX71240010}+{TX71250010}+{TX71300010}+{TX71310010}+{TX71320010}+{TX71330010}+{TX71360010}+{TX71370010}+{TX71380010}+{TX71400010}+{TX71420010}+{TX71430010}'

  item_formula_TX71450011:
    __construct:
      item: '@item_TX71450011'
      formulaString: '{TX71200010}+{TX71210010}+{TX71230010}+{TX71240010}+{TX71250010}+{TX71300010}+{TX71310010}+{TX71320010}+{TX71330010}+{TX71360010}+{TX71370010}+{TX71380010}+{TX71400010}+{TX71420010}+{TX71430010}'

  item_formula_TX71450020:
    __construct:
      item: '@item_TX71450020'
      formulaString: '{TX71200020}+{TX71210020}+{TX71230020}+{TX71240020}+{TX71250020}+{TX71300020}+{TX71310020}+{TX71320020}+{TX71330020}+{TX71360020}+{TX71370020}+{TX71380020}+{TX71400020}+{TX71420020}+{TX71430020}'

  item_formula_TX71450021:
    __construct:
      item: '@item_TX71450021'
      formulaString: '{TX71200020}+{TX71210020}+{TX71230020}+{TX71240020}+{TX71250020}+{TX71300020}+{TX71310020}+{TX71320020}+{TX71330020}+{TX71360020}+{TX71370020}+{TX71380020}+{TX71400020}+{TX71420020}+{TX71430020}'

  item_formula_TX71450030:
    __construct:
      item: '@item_TX71450030'
      formulaString: '{TX71200030}+{TX71210030}+{TX71230030}+{TX71240030}+{TX71250030}+{TX71300030}+{TX71310030}+{TX71320030}+{TX71330030}+{TX71360030}+{TX71370030}+{TX71380030}+{TX71400030}+{TX71420030}+{TX71430030}'

  item_formula_TX71450031:
    __construct:
      item: '@item_TX71450031'
      formulaString: '{TX71200030}+{TX71210030}+{TX71230030}+{TX71240030}+{TX71250030}+{TX71300030}+{TX71310030}+{TX71320030}+{TX71330030}+{TX71360030}+{TX71370030}+{TX71380030}+{TX71400030}+{TX71420030}+{TX71430030}'

  item_formula_TX71450040:
    __construct:
      item: '@item_TX71450040'
      formulaString: '{TX71200040}+{TX71210040}+{TX71230040}+{TX71240040}+{TX71250040}+{TX71300040}+{TX71310040}+{TX71320040}+{TX71330040}+{TX71360040}+{TX71370040}+{TX71380040}+{TX71400040}+{TX71420040}+{TX71430040}'

  item_formula_TX71450041:
    __construct:
      item: '@item_TX71450041'
      formulaString: '{TX71200040}+{TX71210040}+{TX71230040}+{TX71240040}+{TX71250040}+{TX71300040}+{TX71310040}+{TX71320040}+{TX71330040}+{TX71360040}+{TX71370040}+{TX71380040}+{TX71400040}+{TX71420040}+{TX71430040}'

  item_formula_TX71450050:
    __construct:
      item: '@item_TX71450050'
      formulaString: '{TX71450011}+{TX71450021}+{TX71450031}+{TX71450041}'

  item_formula_TX71450060:
    __construct:
      item: '@item_TX71450060'
      formulaString: '{TX71200060}+{TX71210060}+{TX71230060}+{TX71240060}+{TX71250060}+{TX71300060}+{TX71310060}+{TX71320060}+{TX71330060}+{TX71360060}+{TX71370060}+{TX71380060}+{TX71400060}+{TX71420060}+{TX71430060}'

  item_formula_TX71450070:
    __construct:
      item: '@item_TX71450070'
      formulaString: '{TX71200070}+{TX71210070}+{TX71230070}+{TX71240070}+{TX71250070}+{TX71300070}+{TX71310070}+{TX71320070}+{TX71330070}+{TX71360070}+{TX71370070}+{TX71380070}+{TX71400070}+{TX71420070}+{TX71430070}'

  item_formula_TX71450080:
    __construct:
      item: '@item_TX71450080'
      formulaString: '{TX71200080}+{TX71210080}+{TX71230080}+{TX71240080}+{TX71250080}+{TX71300080}+{TX71310080}+{TX71320080}+{TX71330080}+{TX71360080}+{TX71370080}+{TX71380080}+{TX71400080}+{TX71420080}+{TX71430080}'

  item_formula_TX71450090:
    __construct:
      item: '@item_TX71450090'
      formulaString: '{TX71200090}+{TX71210090}+{TX71230090}+{TX71240090}+{TX71250090}+{TX71300090}+{TX71310090}+{TX71320090}+{TX71330090}+{TX71360090}+{TX71370090}+{TX71380090}+{TX71400090}+{TX71420090}+{TX71430090}'

  item_formula_TX71450101:
    __construct:
      item: '@item_TX71450101'
      formulaString: '{TX71200101}+{TX71210101}+{TX71230101}+{TX71240101}+{TX71250101}+{TX71300101}+{TX71310101}+{TX71320101}+{TX71330101}+{TX71360101}+{TX71370101}+{TX71380101}+{TX71400101}+{TX71420101}+{TX71430101}'

  item_formula_TX71450111:
    __construct:
      item: '@item_TX71450111'
      formulaString: '{TX71200111}+{TX71210111}+{TX71230111}+{TX71240111}+{TX71250111}+{TX71300111}+{TX71310111}+{TX71320111}+{TX71330111}+{TX71360111}+{TX71370111}+{TX71380111}+{TX71400111}+{TX71420111}+{TX71430111}'

  item_formula_TX71450112:
    __construct:
      item: '@item_TX71450112'
      formulaString: '{TX71200112}+{TX71210112}+{TX71230112}+{TX71240112}+{TX71250112}+{TX71300112}+{TX71310112}+{TX71320112}+{TX71330112}+{TX71360112}+{TX71370112}+{TX71380112}+{TX71400112}+{TX71420112}+{TX71430112}'

  item_formula_TX71450113:
    __construct:
      item: '@item_TX71450113'
      formulaString: '{TX71200113}+{TX71210113}+{TX71230113}+{TX71240113}+{TX71250113}+{TX71300113}+{TX71310113}+{TX71320113}+{TX71330113}+{TX71360113}+{TX71370113}+{TX71380113}+{TX71400113}+{TX71420113}+{TX71430113}'

  item_formula_TX91200010P:
    __construct:
      item: '@item_TX91200010P'
      formulaString: '{PTX91200010}'

  item_formula_TX91200020P:
    __construct:
      item: '@item_TX91200020P'
      formulaString: '{PTX91200020}'

  item_formula_TX91200030:
    __construct:
      item: '@item_TX91200030'
      formulaString: '{TX91200020}-{TX91200010}'

  item_formula_TX91200030P:
    __construct:
      item: '@item_TX91200030P'
      formulaString: '{PTX91200030}'

  item_formula_TX91200040P:
    __construct:
      item: '@item_TX91200040P'
      formulaString: '{PTX91200040}'

  item_formula_TX91200050P:
    __construct:
      item: '@item_TX91200050P'
      formulaString: '{PTX91200050}'

  item_formula_TX91200060P:
    __construct:
      item: '@item_TX91200060P'
      formulaString: '{PTX91200060}'

  item_formula_TX91200070:
    __construct:
      item: '@item_TX91200070'
      formulaString: '{TX91200030}-{TX91200040}-{TX91200050}-{TX91200060}'

  item_formula_TX91200070P:
    __construct:
      item: '@item_TX91200070P'
      formulaString: '{PTX91200070}'

  item_formula_TX91200100:
    __construct:
      item: '@item_TX91200100'
      formulaString: '{PTX91200020}+{TX91200080}+{TX91200090}'

  item_formula_TX91200101:
    __construct:
      item: '@item_TX91200101'
      formulaString: '{TX91200070}-{PTX91200070}-{TX91200090}-{TX91200095}'

  item_formula_TX91200111:
    __construct:
      item: '@item_TX91200111'
      formulaString: '-{PTX91200040}+{TX91200040}-{TX91200080}-{TX91200085}'

  item_formula_TX91210010P:
    __construct:
      item: '@item_TX91210010P'
      formulaString: '{PTX91210010}'

  item_formula_TX91210020P:
    __construct:
      item: '@item_TX91210020P'
      formulaString: '{PTX91210020}'

  item_formula_TX91210030:
    __construct:
      item: '@item_TX91210030'
      formulaString: '{TX91210020}-{TX91210010}'

  item_formula_TX91210030P:
    __construct:
      item: '@item_TX91210030P'
      formulaString: '{PTX91210030}'

  item_formula_TX91210040P:
    __construct:
      item: '@item_TX91210040P'
      formulaString: '{PTX91210040}'

  item_formula_TX91210050P:
    __construct:
      item: '@item_TX91210050P'
      formulaString: '{PTX91210050}'

  item_formula_TX91210060P:
    __construct:
      item: '@item_TX91210060P'
      formulaString: '{PTX91210060}'

  item_formula_TX91210070:
    __construct:
      item: '@item_TX91210070'
      formulaString: '{TX91210030}-{TX91210040}-{TX91210050}-{TX91210060}'

  item_formula_TX91210070P:
    __construct:
      item: '@item_TX91210070P'
      formulaString: '{PTX91210070}'

  item_formula_TX91210100:
    __construct:
      item: '@item_TX91210100'
      formulaString: '{PTX91210020}+{TX91210080}+{TX91210090}'

  item_formula_TX91210101:
    __construct:
      item: '@item_TX91210101'
      formulaString: '{TX91210070}-{PTX91210070}-{TX91210090}-{TX91210095}'

  item_formula_TX91210111:
    __construct:
      item: '@item_TX91210111'
      formulaString: '-{PTX91210040}+{TX91210040}-{TX91210080}-{TX91210085}'

  item_formula_TX91220010:
    __construct:
      item: '@item_TX91220010'
      formulaString: '{TX91200010}+{TX91210010}'

  item_formula_TX91220010P:
    __construct:
      item: '@item_TX91220010P'
      formulaString: '{PTX91220010}'

  item_formula_TX91220020:
    __construct:
      item: '@item_TX91220020'
      formulaString: '{TX91200020}+{TX91210020}'

  item_formula_TX91220020P:
    __construct:
      item: '@item_TX91220020P'
      formulaString: '{PTX91220020}'

  item_formula_TX91220030:
    __construct:
      item: '@item_TX91220030'
      formulaString: '{TX91200030}+{TX91210030}'

  item_formula_TX91220030P:
    __construct:
      item: '@item_TX91220030P'
      formulaString: '{PTX91220030}'

  item_formula_TX91220040:
    __construct:
      item: '@item_TX91220040'
      formulaString: '{TX91200040}+{TX91210040}'

  item_formula_TX91220040P:
    __construct:
      item: '@item_TX91220040P'
      formulaString: '{PTX91220040}'

  item_formula_TX91220050:
    __construct:
      item: '@item_TX91220050'
      formulaString: '{TX91200050}+{TX91210050}'

  item_formula_TX91220050P:
    __construct:
      item: '@item_TX91220050P'
      formulaString: '{PTX91220050}'

  item_formula_TX91220060:
    __construct:
      item: '@item_TX91220060'
      formulaString: '{TX91200060}+{TX91210060}'

  item_formula_TX91220060P:
    __construct:
      item: '@item_TX91220060P'
      formulaString: '{PTX91220060}'

  item_formula_TX91220070:
    __construct:
      item: '@item_TX91220070'
      formulaString: '{TX91200070}+{TX91210070}'

  item_formula_TX91220070P:
    __construct:
      item: '@item_TX91220070P'
      formulaString: '{PTX91220070}'

  item_formula_TX91220080:
    __construct:
      item: '@item_TX91220080'
      formulaString: '{TX91200080}+{TX91210080}'

  item_formula_TX91220085:
    __construct:
      item: '@item_TX91220085'
      formulaString: '{TX91200085}+{TX91210085}'

  item_formula_TX91220090:
    __construct:
      item: '@item_TX91220090'
      formulaString: '{TX91200090}+{TX91210090}'

  item_formula_TX91220095:
    __construct:
      item: '@item_TX91220095'
      formulaString: '{TX91200095}+{TX91210095}'

  item_formula_TX91220100:
    __construct:
      item: '@item_TX91220100'
      formulaString: '{PTX91220020}+{TX91220080}+{TX91220090}'

  item_formula_TX91220101:
    __construct:
      item: '@item_TX91220101'
      formulaString: '{TX91200101}+{TX91210101}'

  item_formula_TX91220111:
    __construct:
      item: '@item_TX91220111'
      formulaString: '{TX91200111}+{TX91210111}'

  item_formula_TX91230010P:
    __construct:
      item: '@item_TX91230010P'
      formulaString: '{PTX91230010}'

  item_formula_TX91230020P:
    __construct:
      item: '@item_TX91230020P'
      formulaString: '{PTX91230020}'

  item_formula_TX91230030:
    __construct:
      item: '@item_TX91230030'
      formulaString: '{TX91230020}-{TX91230010}'

  item_formula_TX91230030P:
    __construct:
      item: '@item_TX91230030P'
      formulaString: '{PTX91230030}'

  item_formula_TX91230040P:
    __construct:
      item: '@item_TX91230040P'
      formulaString: '{PTX91230040}'

  item_formula_TX91230050P:
    __construct:
      item: '@item_TX91230050P'
      formulaString: '{PTX91230050}'

  item_formula_TX91230060P:
    __construct:
      item: '@item_TX91230060P'
      formulaString: '{PTX91230060}'

  item_formula_TX91230070:
    __construct:
      item: '@item_TX91230070'
      formulaString: '{TX91230030}-{TX91230040}-{TX91230050}-{TX91230060}'

  item_formula_TX91230070P:
    __construct:
      item: '@item_TX91230070P'
      formulaString: '{PTX91230070}'

  item_formula_TX91230100:
    __construct:
      item: '@item_TX91230100'
      formulaString: '{PTX91230020}+{TX91230080}+{TX91230090}'

  item_formula_TX91230101:
    __construct:
      item: '@item_TX91230101'
      formulaString: '{TX91230070}-{PTX91230070}-{TX91230090}-{TX91230095}'

  item_formula_TX91230111:
    __construct:
      item: '@item_TX91230111'
      formulaString: '-{PTX91230040}+{TX91230040}-{TX91230080}-{TX91230085}'

  item_formula_TX91240010P:
    __construct:
      item: '@item_TX91240010P'
      formulaString: '{PTX91240010}'

  item_formula_TX91240020P:
    __construct:
      item: '@item_TX91240020P'
      formulaString: '{PTX91240020}'

  item_formula_TX91240030:
    __construct:
      item: '@item_TX91240030'
      formulaString: '{TX91240020}-{TX91240010}'

  item_formula_TX91240030P:
    __construct:
      item: '@item_TX91240030P'
      formulaString: '{PTX91240030}'

  item_formula_TX91240040P:
    __construct:
      item: '@item_TX91240040P'
      formulaString: '{PTX91240040}'

  item_formula_TX91240050P:
    __construct:
      item: '@item_TX91240050P'
      formulaString: '{PTX91240050}'

  item_formula_TX91240060P:
    __construct:
      item: '@item_TX91240060P'
      formulaString: '{PTX91240060}'

  item_formula_TX91240070:
    __construct:
      item: '@item_TX91240070'
      formulaString: '{TX91240030}-{TX91240040}-{TX91240050}-{TX91240060}'

  item_formula_TX91240070P:
    __construct:
      item: '@item_TX91240070P'
      formulaString: '{PTX91240070}'

  item_formula_TX91240100:
    __construct:
      item: '@item_TX91240100'
      formulaString: '{PTX91240020}+{TX91240080}+{TX91240090}'

  item_formula_TX91240101:
    __construct:
      item: '@item_TX91240101'
      formulaString: '{TX91240070}-{PTX91240070}-{TX91240090}-{TX91240095}'

  item_formula_TX91240111:
    __construct:
      item: '@item_TX91240111'
      formulaString: '-{PTX91240040}+{TX91240040}-{TX91240080}-{TX91240085}'

  item_formula_TX91250010P:
    __construct:
      item: '@item_TX91250010P'
      formulaString: '{PTX91250010}'

  item_formula_TX91250020P:
    __construct:
      item: '@item_TX91250020P'
      formulaString: '{PTX91250020}'

  item_formula_TX91250030:
    __construct:
      item: '@item_TX91250030'
      formulaString: '{TX91250020}-{TX91250010}'

  item_formula_TX91250030P:
    __construct:
      item: '@item_TX91250030P'
      formulaString: '{PTX91250030}'

  item_formula_TX91250040P:
    __construct:
      item: '@item_TX91250040P'
      formulaString: '{PTX91250040}'

  item_formula_TX91250050P:
    __construct:
      item: '@item_TX91250050P'
      formulaString: '{PTX91250050}'

  item_formula_TX91250060P:
    __construct:
      item: '@item_TX91250060P'
      formulaString: '{PTX91250060}'

  item_formula_TX91250070:
    __construct:
      item: '@item_TX91250070'
      formulaString: '{TX91250030}-{TX91250040}-{TX91250050}-{TX91250060}'

  item_formula_TX91250070P:
    __construct:
      item: '@item_TX91250070P'
      formulaString: '{PTX91250070}'

  item_formula_TX91250100:
    __construct:
      item: '@item_TX91250100'
      formulaString: '{PTX91250020}+{TX91250080}+{TX91250090}'

  item_formula_TX91250101:
    __construct:
      item: '@item_TX91250101'
      formulaString: '{TX91250070}-{PTX91250070}-{TX91250090}-{TX91250095}'

  item_formula_TX91250111:
    __construct:
      item: '@item_TX91250111'
      formulaString: '-{PTX91250040}+{TX91250040}-{TX91250080}-{TX91250085}'

  item_formula_TX91260010:
    __construct:
      item: '@item_TX91260010'
      formulaString: '{TX91240010}+{TX91250010}'

  item_formula_TX91260010P:
    __construct:
      item: '@item_TX91260010P'
      formulaString: '{PTX91260010}'

  item_formula_TX91260020:
    __construct:
      item: '@item_TX91260020'
      formulaString: '{TX91240020}+{TX91250020}'

  item_formula_TX91260020P:
    __construct:
      item: '@item_TX91260020P'
      formulaString: '{PTX91260020}'

  item_formula_TX91260030:
    __construct:
      item: '@item_TX91260030'
      formulaString: '{TX91240030}+{TX91250030}'

  item_formula_TX91260030P:
    __construct:
      item: '@item_TX91260030P'
      formulaString: '{PTX91260030}'

  item_formula_TX91260040:
    __construct:
      item: '@item_TX91260040'
      formulaString: '{TX91240040}+{TX91250040}'

  item_formula_TX91260040P:
    __construct:
      item: '@item_TX91260040P'
      formulaString: '{PTX91260040}'

  item_formula_TX91260050:
    __construct:
      item: '@item_TX91260050'
      formulaString: '{TX91240050}+{TX91250050}'

  item_formula_TX91260050P:
    __construct:
      item: '@item_TX91260050P'
      formulaString: '{PTX91260050}'

  item_formula_TX91260060:
    __construct:
      item: '@item_TX91260060'
      formulaString: '{TX91240060}+{TX91250060}'

  item_formula_TX91260060P:
    __construct:
      item: '@item_TX91260060P'
      formulaString: '{PTX91260060}'

  item_formula_TX91260070:
    __construct:
      item: '@item_TX91260070'
      formulaString: '{TX91240070}+{TX91250070}'

  item_formula_TX91260070P:
    __construct:
      item: '@item_TX91260070P'
      formulaString: '{PTX91260070}'

  item_formula_TX91260080:
    __construct:
      item: '@item_TX91260080'
      formulaString: '{TX91240080}+{TX91250080}'

  item_formula_TX91260085:
    __construct:
      item: '@item_TX91260085'
      formulaString: '{TX91240085}+{TX91250085}'

  item_formula_TX91260090:
    __construct:
      item: '@item_TX91260090'
      formulaString: '{TX91240090}+{TX91250090}'

  item_formula_TX91260095:
    __construct:
      item: '@item_TX91260095'
      formulaString: '{TX91240095}+{TX91250095}'

  item_formula_TX91260100:
    __construct:
      item: '@item_TX91260100'
      formulaString: '{PTX91260020}+{TX91260080}+{TX91260090}'

  item_formula_TX91260101:
    __construct:
      item: '@item_TX91260101'
      formulaString: '{TX91240101}+{TX91250101}'

  item_formula_TX91260111:
    __construct:
      item: '@item_TX91260111'
      formulaString: '{TX91240111}+{TX91250111}'

  item_formula_TX91300010P:
    __construct:
      item: '@item_TX91300010P'
      formulaString: '{PTX91300010}'

  item_formula_TX91300020P:
    __construct:
      item: '@item_TX91300020P'
      formulaString: '{PTX91300020}'

  item_formula_TX91300030:
    __construct:
      item: '@item_TX91300030'
      formulaString: '{TX91300020}-{TX91300010}'

  item_formula_TX91300030P:
    __construct:
      item: '@item_TX91300030P'
      formulaString: '{PTX91300030}'

  item_formula_TX91300040P:
    __construct:
      item: '@item_TX91300040P'
      formulaString: '{PTX91300040}'

  item_formula_TX91300050P:
    __construct:
      item: '@item_TX91300050P'
      formulaString: '{PTX91300050}'

  item_formula_TX91300060P:
    __construct:
      item: '@item_TX91300060P'
      formulaString: '{PTX91300060}'

  item_formula_TX91300070:
    __construct:
      item: '@item_TX91300070'
      formulaString: '{TX91300030}-{TX91300040}-{TX91300050}-{TX91300060}'

  item_formula_TX91300070P:
    __construct:
      item: '@item_TX91300070P'
      formulaString: '{PTX91300070}'

  item_formula_TX91300100:
    __construct:
      item: '@item_TX91300100'
      formulaString: '{PTX91300020}+{TX91300080}+{TX91300090}'

  item_formula_TX91300101:
    __construct:
      item: '@item_TX91300101'
      formulaString: '{TX91300070}-{PTX91300070}-{TX91300090}-{TX91300095}'

  item_formula_TX91300111:
    __construct:
      item: '@item_TX91300111'
      formulaString: '-{PTX91300040}+{TX91300040}-{TX91300080}-{TX91300085}'

  item_formula_TX91310010P:
    __construct:
      item: '@item_TX91310010P'
      formulaString: '{PTX91310010}'

  item_formula_TX91310020P:
    __construct:
      item: '@item_TX91310020P'
      formulaString: '{PTX91310020}'

  item_formula_TX91310030:
    __construct:
      item: '@item_TX91310030'
      formulaString: '{TX91310020}-{TX91310010}'

  item_formula_TX91310030P:
    __construct:
      item: '@item_TX91310030P'
      formulaString: '{PTX91310030}'

  item_formula_TX91310040P:
    __construct:
      item: '@item_TX91310040P'
      formulaString: '{PTX91310040}'

  item_formula_TX91310050P:
    __construct:
      item: '@item_TX91310050P'
      formulaString: '{PTX91310050}'

  item_formula_TX91310060P:
    __construct:
      item: '@item_TX91310060P'
      formulaString: '{PTX91310060}'

  item_formula_TX91310070:
    __construct:
      item: '@item_TX91310070'
      formulaString: '{TX91310030}-{TX91310040}-{TX91310050}-{TX91310060}'

  item_formula_TX91310070P:
    __construct:
      item: '@item_TX91310070P'
      formulaString: '{PTX91310070}'

  item_formula_TX91310100:
    __construct:
      item: '@item_TX91310100'
      formulaString: '{PTX91310020}+{TX91310080}+{TX91310090}'

  item_formula_TX91310101:
    __construct:
      item: '@item_TX91310101'
      formulaString: '{TX91310070}-{PTX91310070}-{TX91310090}-{TX91310095}'

  item_formula_TX91310111:
    __construct:
      item: '@item_TX91310111'
      formulaString: '-{PTX91310040}+{TX91310040}-{TX91310080}-{TX91310085}'

  item_formula_TX91320010P:
    __construct:
      item: '@item_TX91320010P'
      formulaString: '{PTX91320010}'

  item_formula_TX91320020P:
    __construct:
      item: '@item_TX91320020P'
      formulaString: '{PTX91320020}'

  item_formula_TX91320030:
    __construct:
      item: '@item_TX91320030'
      formulaString: '{TX91320020}-{TX91320010}'

  item_formula_TX91320030P:
    __construct:
      item: '@item_TX91320030P'
      formulaString: '{PTX91320030}'

  item_formula_TX91320040P:
    __construct:
      item: '@item_TX91320040P'
      formulaString: '{PTX91320040}'

  item_formula_TX91320050P:
    __construct:
      item: '@item_TX91320050P'
      formulaString: '{PTX91320050}'

  item_formula_TX91320060P:
    __construct:
      item: '@item_TX91320060P'
      formulaString: '{PTX91320060}'

  item_formula_TX91320070:
    __construct:
      item: '@item_TX91320070'
      formulaString: '{TX91320030}-{TX91320040}-{TX91320050}-{TX91320060}'

  item_formula_TX91320070P:
    __construct:
      item: '@item_TX91320070P'
      formulaString: '{PTX91320070}'

  item_formula_TX91320100:
    __construct:
      item: '@item_TX91320100'
      formulaString: '{PTX91320020}+{TX91320080}+{TX91320090}'

  item_formula_TX91320101:
    __construct:
      item: '@item_TX91320101'
      formulaString: '{TX91320070}-{PTX91320070}-{TX91320090}-{TX91320095}'

  item_formula_TX91320111:
    __construct:
      item: '@item_TX91320111'
      formulaString: '-{PTX91320040}+{TX91320040}-{TX91320080}-{TX91320085}'

  item_formula_TX91330010P:
    __construct:
      item: '@item_TX91330010P'
      formulaString: '{PTX91330010}'

  item_formula_TX91330020P:
    __construct:
      item: '@item_TX91330020P'
      formulaString: '{PTX91330020}'

  item_formula_TX91330030:
    __construct:
      item: '@item_TX91330030'
      formulaString: '{TX91330020}-{TX91330010}'

  item_formula_TX91330030P:
    __construct:
      item: '@item_TX91330030P'
      formulaString: '{PTX91330030}'

  item_formula_TX91330040P:
    __construct:
      item: '@item_TX91330040P'
      formulaString: '{PTX91330040}'

  item_formula_TX91330050P:
    __construct:
      item: '@item_TX91330050P'
      formulaString: '{PTX91330050}'

  item_formula_TX91330060P:
    __construct:
      item: '@item_TX91330060P'
      formulaString: '{PTX91330060}'

  item_formula_TX91330070:
    __construct:
      item: '@item_TX91330070'
      formulaString: '{TX91330030}-{TX91330040}-{TX91330050}-{TX91330060}'

  item_formula_TX91330070P:
    __construct:
      item: '@item_TX91330070P'
      formulaString: '{PTX91330070}'

  item_formula_TX91330100:
    __construct:
      item: '@item_TX91330100'
      formulaString: '{PTX91330020}+{TX91330080}+{TX91330090}'

  item_formula_TX91330101:
    __construct:
      item: '@item_TX91330101'
      formulaString: '{TX91330070}-{PTX91330070}-{TX91330090}-{TX91330095}'

  item_formula_TX91330111:
    __construct:
      item: '@item_TX91330111'
      formulaString: '-{PTX91330040}+{TX91330040}-{TX91330080}-{TX91330085}'

  item_formula_TX91360010P:
    __construct:
      item: '@item_TX91360010P'
      formulaString: '{PTX91360010}'

  item_formula_TX91360020P:
    __construct:
      item: '@item_TX91360020P'
      formulaString: '{PTX91360020}'

  item_formula_TX91360030:
    __construct:
      item: '@item_TX91360030'
      formulaString: '{TX91360020}-{TX91360010}'

  item_formula_TX91360030P:
    __construct:
      item: '@item_TX91360030P'
      formulaString: '{PTX91360030}'

  item_formula_TX91360040P:
    __construct:
      item: '@item_TX91360040P'
      formulaString: '{PTX91360040}'

  item_formula_TX91360050P:
    __construct:
      item: '@item_TX91360050P'
      formulaString: '{PTX91360050}'

  item_formula_TX91360060P:
    __construct:
      item: '@item_TX91360060P'
      formulaString: '{PTX91360060}'

  item_formula_TX91360070:
    __construct:
      item: '@item_TX91360070'
      formulaString: '{TX91360030}-{TX91360040}-{TX91360050}-{TX91360060}'

  item_formula_TX91360070P:
    __construct:
      item: '@item_TX91360070P'
      formulaString: '{PTX91360070}'

  item_formula_TX91360100:
    __construct:
      item: '@item_TX91360100'
      formulaString: '{PTX91360020}+{TX91360080}+{TX91360090}'

  item_formula_TX91360101:
    __construct:
      item: '@item_TX91360101'
      formulaString: '{TX91360070}-{PTX91360070}-{TX91360090}-{TX91360095}'

  item_formula_TX91360111:
    __construct:
      item: '@item_TX91360111'
      formulaString: '-{PTX91360040}+{TX91360040}-{TX91360080}-{TX91360085}'

  item_formula_TX91370010P:
    __construct:
      item: '@item_TX91370010P'
      formulaString: '{PTX91370010}'

  item_formula_TX91370020P:
    __construct:
      item: '@item_TX91370020P'
      formulaString: '{PTX91370020}'

  item_formula_TX91370030:
    __construct:
      item: '@item_TX91370030'
      formulaString: '{TX91370020}-{TX91370010}'

  item_formula_TX91370030P:
    __construct:
      item: '@item_TX91370030P'
      formulaString: '{PTX91370030}'

  item_formula_TX91370040P:
    __construct:
      item: '@item_TX91370040P'
      formulaString: '{PTX91370040}'

  item_formula_TX91370050P:
    __construct:
      item: '@item_TX91370050P'
      formulaString: '{PTX91370050}'

  item_formula_TX91370060P:
    __construct:
      item: '@item_TX91370060P'
      formulaString: '{PTX91370060}'

  item_formula_TX91370070:
    __construct:
      item: '@item_TX91370070'
      formulaString: '{TX91370030}-{TX91370040}-{TX91370050}-{TX91370060}'

  item_formula_TX91370070P:
    __construct:
      item: '@item_TX91370070P'
      formulaString: '{PTX91370070}'

  item_formula_TX91370100:
    __construct:
      item: '@item_TX91370100'
      formulaString: '{PTX91370020}+{TX91370080}+{TX91370090}'

  item_formula_TX91370101:
    __construct:
      item: '@item_TX91370101'
      formulaString: '{TX91370070}-{PTX91370070}-{TX91370090}-{TX91370095}'

  item_formula_TX91370111:
    __construct:
      item: '@item_TX91370111'
      formulaString: '-{PTX91370040}+{TX91370040}-{TX91370080}-{TX91370085}'

  item_formula_TX91380010P:
    __construct:
      item: '@item_TX91380010P'
      formulaString: '{PTX91380010}'

  item_formula_TX91380020P:
    __construct:
      item: '@item_TX91380020P'
      formulaString: '{PTX91380020}'

  item_formula_TX91380030:
    __construct:
      item: '@item_TX91380030'
      formulaString: '{TX91380020}-{TX91380010}'

  item_formula_TX91380030P:
    __construct:
      item: '@item_TX91380030P'
      formulaString: '{PTX91380030}'

  item_formula_TX91380040P:
    __construct:
      item: '@item_TX91380040P'
      formulaString: '{PTX91380040}'

  item_formula_TX91380050P:
    __construct:
      item: '@item_TX91380050P'
      formulaString: '{PTX91380050}'

  item_formula_TX91380060P:
    __construct:
      item: '@item_TX91380060P'
      formulaString: '{PTX91380060}'

  item_formula_TX91380070:
    __construct:
      item: '@item_TX91380070'
      formulaString: '{TX91380030}-{TX91380040}-{TX91380050}-{TX91380060}'

  item_formula_TX91380070P:
    __construct:
      item: '@item_TX91380070P'
      formulaString: '{PTX91380070}'

  item_formula_TX91380100:
    __construct:
      item: '@item_TX91380100'
      formulaString: '{PTX91380020}+{TX91380080}+{TX91380090}'

  item_formula_TX91380101:
    __construct:
      item: '@item_TX91380101'
      formulaString: '{TX91380070}-{PTX91380070}-{TX91380090}-{TX91380095}'

  item_formula_TX91380111:
    __construct:
      item: '@item_TX91380111'
      formulaString: '-{PTX91380040}+{TX91380040}-{TX91380080}-{TX91380085}'

  item_formula_TX91400010P:
    __construct:
      item: '@item_TX91400010P'
      formulaString: '{PTX91400010}'

  item_formula_TX91400020P:
    __construct:
      item: '@item_TX91400020P'
      formulaString: '{PTX91400020}'

  item_formula_TX91400030:
    __construct:
      item: '@item_TX91400030'
      formulaString: '{TX91400020}-{TX91400010}'

  item_formula_TX91400030P:
    __construct:
      item: '@item_TX91400030P'
      formulaString: '{PTX91400030}'

  item_formula_TX91400040P:
    __construct:
      item: '@item_TX91400040P'
      formulaString: '{PTX91400040}'

  item_formula_TX91400050P:
    __construct:
      item: '@item_TX91400050P'
      formulaString: '{PTX91400050}'

  item_formula_TX91400060P:
    __construct:
      item: '@item_TX91400060P'
      formulaString: '{PTX91400060}'

  item_formula_TX91400070:
    __construct:
      item: '@item_TX91400070'
      formulaString: '{TX91400030}-{TX91400040}-{TX91400050}-{TX91400060}'

  item_formula_TX91400070P:
    __construct:
      item: '@item_TX91400070P'
      formulaString: '{PTX91400070}'

  item_formula_TX91400100:
    __construct:
      item: '@item_TX91400100'
      formulaString: '{PTX91400020}+{TX91400080}+{TX91400090}'

  item_formula_TX91400101:
    __construct:
      item: '@item_TX91400101'
      formulaString: '{TX91400070}-{PTX91400070}-{TX91400090}-{TX91400095}'

  item_formula_TX91400111:
    __construct:
      item: '@item_TX91400111'
      formulaString: '-{PTX91400040}+{TX91400040}-{TX91400080}-{TX91400085}'

  item_formula_TX91420010P:
    __construct:
      item: '@item_TX91420010P'
      formulaString: '{PTX91420010}'

  item_formula_TX91420020P:
    __construct:
      item: '@item_TX91420020P'
      formulaString: '{PTX91420020}'

  item_formula_TX91420030:
    __construct:
      item: '@item_TX91420030'
      formulaString: '{TX91420020}-{TX91420010}'

  item_formula_TX91420030P:
    __construct:
      item: '@item_TX91420030P'
      formulaString: '{PTX91420030}'

  item_formula_TX91420040P:
    __construct:
      item: '@item_TX91420040P'
      formulaString: '{PTX91420040}'

  item_formula_TX91420050P:
    __construct:
      item: '@item_TX91420050P'
      formulaString: '{PTX91420050}'

  item_formula_TX91420060P:
    __construct:
      item: '@item_TX91420060P'
      formulaString: '{PTX91420060}'

  item_formula_TX91420070:
    __construct:
      item: '@item_TX91420070'
      formulaString: '{TX91420030}-{TX91420040}-{TX91420050}-{TX91420060}'

  item_formula_TX91420070P:
    __construct:
      item: '@item_TX91420070P'
      formulaString: '{PTX91420070}'

  item_formula_TX91420100:
    __construct:
      item: '@item_TX91420100'
      formulaString: '{PTX91420020}+{TX91420080}+{TX91420090}'

  item_formula_TX91420101:
    __construct:
      item: '@item_TX91420101'
      formulaString: '{TX91420070}-{PTX91420070}-{TX91420090}-{TX91420095}'

  item_formula_TX91420111:
    __construct:
      item: '@item_TX91420111'
      formulaString: '-{PTX91420040}+{TX91420040}-{TX91420080}-{TX91420085}'

  item_formula_TX91430010P:
    __construct:
      item: '@item_TX91430010P'
      formulaString: '{PTX91430010}'

  item_formula_TX91430020P:
    __construct:
      item: '@item_TX91430020P'
      formulaString: '{PTX91430020}'

  item_formula_TX91430030:
    __construct:
      item: '@item_TX91430030'
      formulaString: '{TX91430020}-{TX91430010}'

  item_formula_TX91430030P:
    __construct:
      item: '@item_TX91430030P'
      formulaString: '{PTX91430030}'

  item_formula_TX91430040P:
    __construct:
      item: '@item_TX91430040P'
      formulaString: '{PTX91430040}'

  item_formula_TX91430050P:
    __construct:
      item: '@item_TX91430050P'
      formulaString: '{PTX91430050}'

  item_formula_TX91430060P:
    __construct:
      item: '@item_TX91430060P'
      formulaString: '{PTX91430060}'

  item_formula_TX91430070:
    __construct:
      item: '@item_TX91430070'
      formulaString: '{TX91430030}-{TX91430040}-{TX91430050}-{TX91430060}'

  item_formula_TX91430070P:
    __construct:
      item: '@item_TX91430070P'
      formulaString: '{PTX91430070}'

  item_formula_TX91430100:
    __construct:
      item: '@item_TX91430100'
      formulaString: '{PTX91430020}+{TX91430080}+{TX91430090}'

  item_formula_TX91430101:
    __construct:
      item: '@item_TX91430101'
      formulaString: '{TX91430070}-{PTX91430070}-{TX91430090}-{TX91430095}'

  item_formula_TX91430111:
    __construct:
      item: '@item_TX91430111'
      formulaString: '-{PTX91430040}+{TX91430040}-{TX91430080}-{TX91430085}'

  item_formula_TX91450010:
    __construct:
      item: '@item_TX91450010'
      formulaString: '{TX91200010}+{TX91210010}+{TX91230010}+{TX91240010}+{TX91250010}+{TX91300010}+{TX91310010}+{TX91320010}+{TX91330010}+{TX91360010}+{TX91370010}+{TX91380010}+{TX91400010}+{TX91420010}+{TX91430010}'

  item_formula_TX91450010P:
    __construct:
      item: '@item_TX91450010P'
      formulaString: '{PTX91450010}'

  item_formula_TX91450020:
    __construct:
      item: '@item_TX91450020'
      formulaString: '{TX91200020}+{TX91210020}+{TX91230020}+{TX91240020}+{TX91250020}+{TX91300020}+{TX91310020}+{TX91320020}+{TX91330020}+{TX91360020}+{TX91370020}+{TX91380020}+{TX91400020}+{TX91420020}+{TX91430020}'

  item_formula_TX91450020P:
    __construct:
      item: '@item_TX91450020P'
      formulaString: '{PTX91450020}'

  item_formula_TX91450030:
    __construct:
      item: '@item_TX91450030'
      formulaString: '{TX91200030}+{TX91210030}+{TX91230030}+{TX91240030}+{TX91250030}+{TX91300030}+{TX91310030}+{TX91320030}+{TX91330030}+{TX91360030}+{TX91370030}+{TX91380030}+{TX91400030}+{TX91420030}+{TX91430030}'

  item_formula_TX91450030P:
    __construct:
      item: '@item_TX91450030P'
      formulaString: '{PTX91450030}'

  item_formula_TX91450040:
    __construct:
      item: '@item_TX91450040'
      formulaString: '{TX91200040}+{TX91210040}+{TX91230040}+{TX91240040}+{TX91250040}+{TX91300040}+{TX91310040}+{TX91320040}+{TX91330040}+{TX91360040}+{TX91370040}+{TX91380040}+{TX91400040}+{TX91420040}+{TX91430040}'

  item_formula_TX91450040P:
    __construct:
      item: '@item_TX91450040P'
      formulaString: '{PTX91450040}'

  item_formula_TX91450050:
    __construct:
      item: '@item_TX91450050'
      formulaString: '{TX91200050}+{TX91210050}+{TX91230050}+{TX91240050}+{TX91250050}+{TX91300050}+{TX91310050}+{TX91320050}+{TX91330050}+{TX91360050}+{TX91370050}+{TX91380050}+{TX91400050}+{TX91420050}+{TX91430050}'

  item_formula_TX91450050P:
    __construct:
      item: '@item_TX91450050P'
      formulaString: '{PTX91450050}'

  item_formula_TX91450060:
    __construct:
      item: '@item_TX91450060'
      formulaString: '{TX91200060}+{TX91210060}+{TX91230060}+{TX91240060}+{TX91250060}+{TX91300060}+{TX91310060}+{TX91320060}+{TX91330060}+{TX91360060}+{TX91370060}+{TX91380060}+{TX91400060}+{TX91420060}+{TX91430060}'

  item_formula_TX91450060P:
    __construct:
      item: '@item_TX91450060P'
      formulaString: '{PTX91450060}'

  item_formula_TX91450070:
    __construct:
      item: '@item_TX91450070'
      formulaString: '{TX91200070}+{TX91210070}+{TX91230070}+{TX91240070}+{TX91250070}+{TX91300070}+{TX91310070}+{TX91320070}+{TX91330070}+{TX91360070}+{TX91370070}+{TX91380070}+{TX91400070}+{TX91420070}+{TX91430070}'

  item_formula_TX91450070P:
    __construct:
      item: '@item_TX91450070P'
      formulaString: '{PTX91450070}'

  item_formula_TX91450080:
    __construct:
      item: '@item_TX91450080'
      formulaString: '{TX91200080}+{TX91210080}+{TX91230080}+{TX91240080}+{TX91250080}+{TX91300080}+{TX91310080}+{TX91320080}+{TX91330080}+{TX91360080}+{TX91370080}+{TX91380080}+{TX91400080}+{TX91420080}+{TX91430080}'

  item_formula_TX91450085:
    __construct:
      item: '@item_TX91450085'
      formulaString: '{TX91200085}+{TX91210085}+{TX91230085}+{TX91240085}+{TX91250085}+{TX91300085}+{TX91310085}+{TX91320085}+{TX91330085}+{TX91360085}+{TX91370085}+{TX91380085}+{TX91400085}+{TX91420085}+{TX91430085}'

  item_formula_TX91450090:
    __construct:
      item: '@item_TX91450090'
      formulaString: '{TX91200090}+{TX91210090}+{TX91230090}+{TX91240090}+{TX91250090}+{TX91300090}+{TX91310090}+{TX91320090}+{TX91330090}+{TX91360090}+{TX91370090}+{TX91380090}+{TX91400090}+{TX91420090}+{TX91430090}'

  item_formula_TX91450095:
    __construct:
      item: '@item_TX91450095'
      formulaString: '{TX91200095}+{TX91210095}+{TX91230095}+{TX91240095}+{TX91250095}+{TX91300095}+{TX91310095}+{TX91320095}+{TX91330095}+{TX91360095}+{TX91370095}+{TX91380095}+{TX91400095}+{TX91420095}+{TX91430095}'

  item_formula_TX91450100:
    __construct:
      item: '@item_TX91450100'
      formulaString: '{TX91200100}+{TX91210100}+{TX91230100}+{TX91240100}+{TX91250100}+{TX91300100}+{TX91310100}+{TX91320100}+{TX91330100}+{TX91360100}+{TX91370100}+{TX91380100}+{TX91400100}+{TX91420100}+{TX91430100}'

  item_formula_TX91450101:
    __construct:
      item: '@item_TX91450101'
      formulaString: '{TX91200101}+{TX91210101}+{TX91230101}+{TX91240101}+{TX91250101}+{TX91300101}+{TX91310101}+{TX91320101}+{TX91330101}+{TX91360101}+{TX91370101}+{TX91380101}+{TX91400101}+{TX91420101}+{TX91430101}'

  item_formula_TX91450111:
    __construct:
      item: '@item_TX91450111'
      formulaString: '{TX91200111}+{TX91210111}+{TX91230111}+{TX91240111}+{TX91250111}+{TX91300111}+{TX91310111}+{TX91320111}+{TX91330111}+{TX91360111}+{TX91370111}+{TX91380111}+{TX91400111}+{TX91420111}+{TX91430111}'
