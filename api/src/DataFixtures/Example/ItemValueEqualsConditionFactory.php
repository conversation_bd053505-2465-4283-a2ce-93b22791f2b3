<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Entity\Workflow\Condition\ItemValueEqualsCondition;

/**
 * @extends ModelFactory<ItemValueEqualsCondition>
 */
final class ItemValueEqualsConditionFactory extends ModelFactory
{
    protected function defaults(): array
    {
        return [
            'value' => self::faker()->text(),
            'transition' => TransitionFactory::new(),
        ];
    }

    public static function class(): string
    {
        return ItemValueEqualsCondition::class;
    }
}
