<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use Tests\U2\TestUtils;
use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Entity\MasterFileSection;

/**
 * @extends ModelFactory<MasterFileSection>
 */
final class MasterFileSectionFactory extends ModelFactory
{
    protected function defaults(): array
    {
        return [
            'content' => self::faker()->text(),
            'editable' => true,
            'include' => true,
            'name' => self::faker()->text(),
            'orderPosition' => self::faker()->randomNumber(),
            'required' => false,
        ];
    }

    protected function initialize(): static
    {
        return $this->afterInstantiate(function (MasterFileSection $file, array $attributes): void {
            /** @var array{
             *      "id"?: int,
             *  } $attributes
             */
            if (\array_key_exists('id', $attributes)) {
                TestUtils::setId($file, $attributes['id']);
            }
        });
    }

    public static function class(): string
    {
        return MasterFileSection::class;
    }
}
