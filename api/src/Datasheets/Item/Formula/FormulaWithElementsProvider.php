<?php

declare(strict_types=1);
namespace U2\Datasheets\Item\Formula;

use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\Cache\TagAwareCacheInterface;
use U2\Entity\Item;
use U2\Entity\ItemFormula;
use U2\Exception\FormulaNotFoundException;
use U2\Repository\CachedItemRepository;
use U2\Repository\ItemFormulaRepository;

#[Autoconfigure(lazy: true)]
class FormulaWithElementsProvider
{
    private const string cacheKey = 'formulas-all-non-flat';

    /**
     * @var array<int, FormulaWithElements>
     */
    private array $formulas;

    public function __construct(
        private readonly ItemFormulaRepository $itemFormulaRepository,
        private readonly FormulaWithElementsFactory $formulaWithElementsFactory,
        private readonly CachedItemRepository $itemRepository,
        private readonly TagAwareCacheInterface $tenantCache,
    ) {
    }

    /**
     * @return array<int,FormulaWithElements>
     */
    public function all(): array
    {
        return array_values($this->getFormulas());
    }

    /**
     * @throws FormulaNotFoundException
     */
    public function get(Item $item): FormulaWithElements
    {
        if ($this->has($item)) {
            return $this->getFormulas()[$item->getId()];
        }

        throw new FormulaNotFoundException("No formula found for item '{$item->getRefId()}' ({$item->getId()}).", 0, null, $item);
    }

    public function has(Item $item): bool
    {
        return \array_key_exists($item->getId(), $this->getFormulas());
    }

    /**
     * @return FormulaWithElements[]
     */
    public function getForNonEditableItems(): array
    {
        $calculatedFormulas = [];
        $nonEditableItems = $this->itemRepository->findNonEditableItemsThatHaveAFormula();

        array_walk(
            $nonEditableItems,
            function (Item $item) use (&$calculatedFormulas): void {
                $calculatedFormulas[] = $this->get($item);
            }
        );

        return $calculatedFormulas;
    }

    /**
     * @return array<int, FormulaWithElements>
     */
    protected function getFormulas(): array
    {
        if (isset($this->formulas)) {
            return $this->formulas;
        }

        $this->formulas = $this->tenantCache->get(self::cacheKey, function (ItemInterface $cacheItem): array {
            $cacheItem->tag('formula');
            $formulas = [];

            /* @var ItemFormula $itemFormula */
            foreach ($this->itemFormulaRepository->findAll() as $itemFormula) {
                $item = $itemFormula->getItem();

                /** @var int $id */
                $id = $item->getId();
                $formulas[$id] = $this->formulaWithElementsFactory->create($item);
            }

            return $formulas;
        });

        return $this->formulas;
    }
}
