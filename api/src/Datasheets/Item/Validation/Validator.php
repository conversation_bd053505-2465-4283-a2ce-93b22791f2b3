<?php

declare(strict_types=1);
namespace U2\Datasheets\Item\Validation;

use Symfony\Component\Validator\Validator\ValidatorInterface;
use U2\Datasheets\Item\Formula\ItemDependentsRecursive;
use U2\Entity\Datasheet;
use U2\Entity\Item;
use U2\Repository\DatasheetRepository;

class Validator
{
    public function __construct(
        private readonly ValidatorInterface $validator,
        private readonly DatasheetRepository $datasheetRepository,
        private readonly ItemDependentsRecursive $itemDependentsRecursive,
    ) {
    }

    /**
     * @param Item[] $items
     *
     * @throws \Exception
     */
    public function validate(array $items): ValidationReport
    {
        $report = new ValidationReport();
        foreach ($items as $item) {
            $report->addItem($item);

            $validationErrors = $this->validateItem($item);
            $report->addErrorsForItem($item->getId(), $validationErrors);
        }

        return $report;
    }

    /**
     * @throws \Exception
     *
     * @return array<value-of<ErrorTypes>, string>
     */
    private function validateItem(Item $item): array
    {
        $errors = [];

        $isItemUsed = $this->existsInAnyDatasheet($item) || 0 !== \count($this->itemDependentsRecursive->get($item->getId()));
        if (!$isItemUsed) {
            $errors[ErrorTypes::NOT_USED->value] = "{$item->getId()} is not used in any layout or formula and can be removed";
        }

        $violationList = $this->validator->validate($item);
        foreach ($violationList as $violation) {
            $message = (string) $violation->getMessage();
            switch ($violation->getCode()) {
                case ErrorTypes::CIRCULAR_REFERENCE->value:
                    $errors[ErrorTypes::CIRCULAR_REFERENCE->value] = $message;
                    break;
                case ErrorTypes::FORMULA_REQUIRED->value:
                    $errors[ErrorTypes::FORMULA_REQUIRED->value] = $message;
                    break;
                default:
                    throw new \Exception("Unhandled violation message: $message");
            }
        }

        return $errors;
    }

    private function existsInAnyDatasheet(Item $item): bool
    {
        return array_any(
            $this->datasheetRepository->findAll(),
            fn ($datasheet): bool => $this->existsInDatasheet($item, $datasheet)
        );
    }

    private function existsInDatasheet(Item $item, Datasheet $datasheet): bool
    {
        $fields = $datasheet->getFields()->toArray();

        return array_any(
            $fields,
            static fn ($field): bool => $field->getItem()->getId() === $item->getId()
        );
    }
}
