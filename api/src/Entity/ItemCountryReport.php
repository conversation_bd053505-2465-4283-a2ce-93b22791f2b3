<?php

declare(strict_types=1);
namespace U2\Entity;

use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Delete;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Link;
use ApiPlatform\Metadata\Patch;
use ApiPlatform\Metadata\Post;
use ApiPlatform\OpenApi\Model\Operation;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Order;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Bridge\Doctrine\Types\UlidType;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Uid\Ulid;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Api\Provider\DatasheetCollectionItemCountryReportsProvider;
use U2\Entity\Interfaces\Blameable;
use U2\Entity\Interfaces\Entity;
use U2\Repository\ItemCountryReportRepository;
use U2\Security\UserRoles;
use U2\Security\Voter\VoterAttributes;

#[UniqueEntity('name')]
#[ORM\Entity(repositoryClass: ItemCountryReportRepository::class)]
#[ORM\Table(name: 'dtm_item_country_report')]
#[ApiResource(
    operations: [
        new Delete(
            security: 'is_granted("' . UserRoles::Admin->value . '")'
        ),
        new Get(
            normalizationContext: ['groups' => ['item-country-report:read']],
        ),
        new GetCollection(
            normalizationContext: ['groups' => ['item-country-report:read']],
        ),
        new Post(
            normalizationContext: ['groups' => ['item-country-report:read']],
            denormalizationContext: ['groups' => ['item-country-report:write']],
            security: 'is_granted("' . UserRoles::Admin->value . '")'
        ),
        new Patch(
            normalizationContext: ['groups' => ['item-country-report:read']],
            denormalizationContext: ['groups' => ['item-country-report:write']],
            security: 'is_granted("' . UserRoles::Admin->value . '")'
        ),
    ],
)]
#[GetCollection(
    uriTemplate: '/layout-collections/{id}/item-country-reports',
    uriVariables: ['id' => new Link(fromClass: DatasheetCollection::class)],
    openapi: new Operation(description: 'Retrieves a collection of item collection reports associated with a layout collection'),
    paginationEnabled: false,
    normalizationContext: ['groups' => ['item-country-report:read']],
    security: 'is_granted("' . VoterAttributes::read . '", findEntity("U2\\\\Entity\\\\DatasheetCollection", request.get("id"))) or is_granted("' . UserRoles::Admin->value . '")',
    provider: DatasheetCollectionItemCountryReportsProvider::class,
)]
class ItemCountryReport implements Blameable, Entity
{
    #[ORM\Id]
    #[ORM\Column(type: UlidType::NAME, unique: true)]
    #[Groups(groups: ['item-country-report:read'])]
    private Ulid $id;

    #[Assert\NotBlank]
    #[ORM\Column(unique: true, nullable: false)]
    #[Groups(groups: ['item-country-report:read', 'item-country-report:write'])]
    private string $name;

    /**
     * @var Collection<int, ItemCountryReportItemEntry>
     */
    #[Assert\Count(min: 1, max: 12)]
    #[ORM\OneToMany(
        mappedBy: 'itemCountryReport',
        targetEntity: ItemCountryReportItemEntry::class,
        cascade: ['persist'],
        fetch: 'EXTRA_LAZY',
        orphanRemoval: true
    )]
    #[ORM\OrderBy(['position' => Order::Ascending->value])]
    private Collection $itemEntries;

    #[Gedmo\Blameable(on: 'create')]
    #[Groups(groups: ['item-country-report:read'])]
    #[ORM\ManyToOne(targetEntity: User::class)]
    protected ?User $createdBy = null;

    #[Gedmo\Blameable(on: 'update')]
    #[Groups(groups: ['item-country-report:read'])]
    #[ORM\ManyToOne(targetEntity: User::class)]
    protected ?User $updatedBy = null;

    public function __construct()
    {
        $this->id = new Ulid();
        $this->itemEntries = new ArrayCollection();
    }

    public function getId(): Ulid
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    /**
     * @param Item[] $items
     */
    #[Groups(groups: ['item-country-report:write'])]
    public function setItems(array $items): void
    {
        $this->itemEntries = new ArrayCollection();
        foreach (array_values($items) as $index => $item) {
            $this->itemEntries->add(new ItemCountryReportItemEntry($this, $item, $index));
        }
    }

    /**
     * @return Collection<int, Item>
     */
    #[Groups(groups: ['item-country-report:read'])]
    public function getItems(): Collection
    {
        return $this->itemEntries->map(fn (ItemCountryReportItemEntry $entry): Item => $entry->getItem());
    }

    public function getCreatedBy(): ?User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?User $createdBy = null): void
    {
        $this->createdBy = $createdBy;
    }

    public function getUpdatedBy(): ?User
    {
        return $this->updatedBy;
    }

    public function setUpdatedBy(?User $updatedBy = null): void
    {
        $this->updatedBy = $updatedBy;
    }
}
