<?php

declare(strict_types=1);
namespace U2\Entity\Task\TaskType;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Entity\Configuration\Field\BusinessActivity;
use U2\Entity\Interfaces\Periodable;
use U2\Entity\Interfaces\Transferable;
use U2\Entity\Period;
use U2\Entity\Task\TaskType;
use U2\EntityMetadata\Attribute\ReadableName;
use U2\EntityMetadata\Attribute\ShortName;
use U2\Repository\MainBusinessActivityRepository;
use U2\Task\TaskTypeKnowledge;
use U2\Validator as U2Assert;

#[ORM\Entity(repositoryClass: MainBusinessActivityRepository::class)]
#[ORM\Table(name: 'tpm_main_business_activity')]
#[ReadableName(value: 'u2_tpm.main_business_activity')]
#[ShortName(value: TaskTypeKnowledge::taskTypeClassToShortNameMap[self::class])]
class MainBusinessActivity extends TaskType implements Transferable, Periodable
{
    #[Assert\NotNull]
    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Period::class)]
    protected ?Period $period = null;

    #[Assert\NotNull]
    #[U2Assert\Enabled]
    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: BusinessActivity::class)]
    private ?BusinessActivity $businessActivity = null;

    public function getPeriod(): ?Period
    {
        return $this->period;
    }

    public function setPeriod(Period $period): void
    {
        $this->period = $period;
    }

    public function getBusinessActivity(): ?BusinessActivity
    {
        return $this->businessActivity;
    }

    public function setBusinessActivity(?BusinessActivity $businessActivity): void
    {
        $this->businessActivity = $businessActivity;
    }

    public static function getWorkflowBindingId(): string
    {
        return 'tpm_main_business_activity';
    }

    public static function getWorkflowBindingName(): string
    {
        return 'TPM Main Business Activity';
    }

    public static function getTaskType(): string
    {
        return 'tpm_main_business_activity';
    }

    public function getDisplayName(): string
    {
        return "#{$this->id} - {$this->businessActivity?->getName()}: {$this->unit?->getRefId()}, {$this->period?->getName()}";
    }
}
