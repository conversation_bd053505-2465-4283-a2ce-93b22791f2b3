<?php

declare(strict_types=1);
namespace U2\EventListener\Import;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use U2\Dto\Unit\TaxNumber\TaxNumber as TaxNumberDto;
use U2\Entity\Address;
use U2\Entity\Country;
use U2\Entity\PermanentEstablishment;
use U2\Entity\TaxNumber;
use U2\Event\Import\PreBindDataImportEvent;
use U2\Exception\ImportInterpreterException;

class PermanentEstablishmentImportSubscriber implements EventSubscriberInterface
{
    public static function getSubscribedEvents(): array
    {
        return [
            PreBindDataImportEvent::class => [
                ['setDefaultTaxNumber'],
                ['setBillingAddress'],
                ['setPostalAddress'],
            ],
        ];
    }

    public function setDefaultTaxNumber(PreBindDataImportEvent $event): void
    {
        $permanentEstablishment = $event->getEntity();
        if (!$permanentEstablishment instanceof PermanentEstablishment) {
            return;
        }

        $interpretedData = $event->getInterpretedData();
        if (false === \array_key_exists('defaultTaxNumber', $interpretedData)) {
            return;
        }

        $unitCountry = $permanentEstablishment->getCountry();
        if (null === $unitCountry && false === \array_key_exists('country', $interpretedData)) {
            throw new ImportInterpreterException('Unable to import a unit with a tax number when no country is given.');
        }

        if (null === $unitCountry && null === $interpretedData['country']) {
            throw new ImportInterpreterException('Unable to import a unit with a tax number when no country is given.');
        }

        $value = $interpretedData['defaultTaxNumber'];
        if (\is_string($value)) {
            $country = $interpretedData['country'] ?? $unitCountry;
            \assert($country instanceof Country);

            $defaultTaxNumber = $permanentEstablishment->getTaxNumbers()
                ->filter(static fn (TaxNumber $element): bool => $element->getCountry() === $country)
                ->first();

            if ($defaultTaxNumber instanceof TaxNumber) {
                $permanentEstablishment->removeTaxNumber($defaultTaxNumber);
            }
            $permanentEstablishment->addTaxNumber(TaxNumber::fromDto(new TaxNumberDto($value, $permanentEstablishment, $country)));
        }

        unset($interpretedData['defaultTaxNumber']);

        $event->setInterpretedData($interpretedData);
    }

    public function setBillingAddress(PreBindDataImportEvent $event): void
    {
        $data = $event->getInterpretedData();
        $permanentEstablishment = $event->getEntity();
        if (false === ($permanentEstablishment instanceof PermanentEstablishment)) {
            return;
        }

        if (null !== $permanentEstablishment->getBillingAddress()) {
            return;
        }

        if (
            \array_key_exists('billingAddress.line1', $data)
            || \array_key_exists('billingAddress.line2', $data)
            || \array_key_exists('billingAddress.line3', $data)
            || \array_key_exists('billingAddress.postCode', $data)
            || \array_key_exists('billingAddress.city', $data)
            || \array_key_exists('billingAddress.state', $data)
            || \array_key_exists('billingAddress.country', $data)
        ) {
            $permanentEstablishment->setBillingAddress(new Address());
        }
    }

    public function setPostalAddress(PreBindDataImportEvent $event): void
    {
        $data = $event->getInterpretedData();
        $permanentEstablishment = $event->getEntity();
        if (false === ($permanentEstablishment instanceof PermanentEstablishment)) {
            return;
        }

        if (null !== $permanentEstablishment->getPostalAddress()) {
            return;
        }

        if (
            \array_key_exists('postalAddress.line1', $data)
            || \array_key_exists('postalAddress.line2', $data)
            || \array_key_exists('postalAddress.line3', $data)
            || \array_key_exists('postalAddress.postCode', $data)
            || \array_key_exists('postalAddress.city', $data)
            || \array_key_exists('postalAddress.state', $data)
            || \array_key_exists('postalAddress.country', $data)
        ) {
            $permanentEstablishment->setPostalAddress(new Address());
        }
    }
}
