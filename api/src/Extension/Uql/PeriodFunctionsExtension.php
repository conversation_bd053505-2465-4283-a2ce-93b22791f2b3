<?php

declare(strict_types=1);
namespace U2\Extension\Uql;

use U2\DataSourcery\Extension\UqlExtensionInterface;
use U2\DataSourcery\Extension\UqlFunction;
use U2\Repository\PeriodRepository;

class PeriodFunctionsExtension implements UqlExtensionInterface
{
    public function __construct(private readonly PeriodRepository $repository)
    {
    }

    public function getFunctions(): array
    {
        return [
            'openPeriods' => new UqlFunction('openPeriods', $this, 'getOpenPeriods'),
            'closedPeriods' => new UqlFunction('closedPeriods', $this, 'getClosedPeriods'),
        ];
    }

    /**
     * @return list<string|null>
     */
    public function getOpenPeriods(): array
    {
        $periods = $this->repository->findBy(['closed' => false]);
        $periodNames = [];
        foreach ($periods as $period) {
            $periodNames[] = $period->getName();
        }

        return $periodNames;
    }

    /**
     * @return list<string|null>
     */
    public function getClosedPeriods(): array
    {
        $periods = $this->repository->findBy(['closed' => true]);
        $periodNames = [];
        foreach ($periods as $period) {
            $periodNames[] = $period->getName();
        }

        return $periodNames;
    }
}
