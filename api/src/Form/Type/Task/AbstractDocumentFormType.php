<?php

declare(strict_types=1);
namespace U2\Form\Type\Task;

use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;

use function Symfony\Component\Translation\t;

use U2\Document\DocumentTypes;
use U2\Entity\DocumentTemplate;
use U2\EventListener\UnitHierarchySnapshotUnitSelectorSubscriber;
use U2\Form\Type\PeriodType;
use U2\Form\Type\UnitHierarchySnapshotUnitSelectorType;
use U2\Form\Type\UnitHierarchyType;
use U2\Repository\DocumentTemplateRepository;
use U2\Task\FieldStateResolver;

abstract class AbstractDocumentFormType extends AbstractTaskTypeFormType
{
    public function __construct(
        FieldStateResolver $fieldStateProvider,
        private readonly UnitHierarchySnapshotUnitSelectorSubscriber $snapshotUnitSelectorSubscriber,
        private readonly DocumentTemplateRepository $documentTemplateRepository,
        private readonly DocumentTypes $documentTypes,
    ) {
        parent::__construct($fieldStateProvider);
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);

        $builder
            ->add(
                'name',
                null,
                [
                    'label' => t('u2_tpm.name'),
                ]
            )
            ->add(
                'period',
                PeriodType::class,
            )
            ->add(
                'unitHierarchy',
                UnitHierarchyType::class,
                [
                    'label' => t('u2_tpm.unit_hierarchy'),
                    'required' => true,
                    'choice_label' => 'name',
                ]
            )
            ->add(
                'units',
                UnitHierarchySnapshotUnitSelectorType::class,
                [
                    'property_path' => 'snapshot',
                    'label' => t('u2_tpm.snapshot'),
                    'error_bubbling' => false,
                ]
            );

        // populate snapshot field from units and vice versa
        $builder->addEventSubscriber($this->snapshotUnitSelectorSubscriber);

        $builder->addEventListener(
            FormEvents::PRE_SET_DATA,
            function (FormEvent $event): void {
                $form = $event->getForm();

                $data = $event->getData();

                if (null === $data) {
                    return;
                }

                // add template selector if creating a new document file
                // check if the document object is "new"
                if (!$data->getId()) {
                    $class = $form->getConfig()->getDataClass();
                    $baseTemplates = $this->documentTemplateRepository->findBy(['type' => $this->documentTypes->getTypeByClass($class)]);

                    // add selector for base template
                    $options = [
                        'label' => t('u2_tpm.base_template'),
                        'choices' => $baseTemplates,
                        'choice_label' => 'name',
                        'class' => DocumentTemplate::class,
                        'required' => false,
                    ];

                    $form->add('baseTemplate', EntityType::class, $options);
                }
            }
        );
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $documentClass = $this->getDocumentClass();
        $resolver->setDefaults(
            [
                'data_class' => $documentClass,
                'save_prompt' => true,
            ]
        );
    }

    abstract protected function getDocumentClass(): string;
}
