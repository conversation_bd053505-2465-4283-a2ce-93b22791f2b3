<?php

declare(strict_types=1);
namespace U2\Form\Type\Task\Field;

use Symfony\Component\Form\FormBuilderInterface;
use U2\Form\Type\ToggleButtonType;

class SingleEconomicOperationFieldAdder implements FieldAdder
{
    public static function add(FormBuilderInterface $builder, array $options = []): void
    {
        $builder->add(
            'singleEconomicOperation',
            ToggleButtonType::class,
            $options
        );
    }
}
