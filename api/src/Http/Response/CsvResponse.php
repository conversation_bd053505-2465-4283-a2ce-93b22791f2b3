<?php

declare(strict_types=1);
namespace U2\Http\Response;

use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpFoundation\StreamedResponse;
use U2\Util\DateTime;
use U2\Util\SpreadsheetFactory;

class CsvResponse extends StreamedResponse
{
    /**
     * @param array<int,array<string,string>> $data
     * @param array<int,string>|null          $csvHeaders
     */
    public function __construct(array $data, ?array $csvHeaders = null, ?string $filename = null, int $status = Response::HTTP_OK, array $headers = [])
    {
        if (0 === \count($data)) {
            throw new \InvalidArgumentException('Data must not be empty');
        }

        if (null === $csvHeaders) {
            $csvHeaders = array_keys($data[0]);
        }

        array_unshift($data, $csvHeaders);

        if (null === $filename) {
            $filename = 'export ' . DateTime::createNow()->format(\DATE_W3C) . '.csv';
        }

        // Create new Spreadsheet object
        $spreadsheet = SpreadsheetFactory::createSpreadsheet();
        $spreadsheet->getProperties()
            ->setTitle($filename);

        $spreadsheet->setActiveSheetIndex(0);
        $activeSheet = $spreadsheet->getActiveSheet();
        $activeSheet->fromArray($data);

        $writer = SpreadsheetFactory::createWriter($spreadsheet, 'Csv');

        parent::__construct(
            static function () use ($writer): void {
                $writer->save('php://output');
            },
            $status,
            array_merge(
                [
                    'Content-Type' => 'text/csv',
                    'Cache-Control' => 'max-age=0',
                    'Content-Disposition' => HeaderUtils::makeDisposition(ResponseHeaderBag::DISPOSITION_ATTACHMENT, $filename, $filename),
                ],
                $headers
            ),
        );
    }
}
