<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Factory;

use U2\Entity\Currency;
use U2\Entity\ExchangeRate;
use U2\Entity\Period;
use U2\Import\Configuration\Field\BooleanFieldConfiguration;
use U2\Import\Configuration\Field\LiteralFieldConfiguration;
use U2\Import\Configuration\Field\LookupFieldConfiguration;
use U2\Import\Configuration\ImportConfig;

class ExchangeRateConfigurationFactory implements ConfigurationFactory
{
    public static function create(): ImportConfig
    {
        return new ImportConfig(
            class: ExchangeRate::class,
            fields: [
                new LiteralFieldConfiguration(
                    id: 'exchangeRateType',
                    label: 'Exchange Rate Type',
                    help: '<p>Defined using a number code. The following values are valid:</p><ul><li>Average: 1</li><li>Current: 2</li></ul>',
                ),
                new LookupFieldConfiguration(
                    id: 'period',
                    label: 'Period',
                    class: Period::class,
                    lookupField: 'name',
                ),
                new LookupFieldConfiguration(
                    id: 'inputCurrency',
                    label: 'Input Currency',
                    class: Currency::class,
                    lookupField: 'iso4217code',
                ),
                new LookupFieldConfiguration(
                    id: 'outputCurrency',
                    label: 'Output Currency',
                    class: Currency::class,
                    lookupField: 'iso4217code',
                ),
                new LiteralFieldConfiguration(
                    id: 'value',
                    label: 'Value',
                ),
                new BooleanFieldConfiguration(
                    id: 'isIndirect',
                    label: 'Is Indirect',
                    help: 'Boolean',
                ),
            ],
            updateMatchFields: ['exchangeRateType', 'period', 'inputCurrency', 'outputCurrency'],
            factory: null,
            factoryArguments: [],
            help: null
        );
    }
}
