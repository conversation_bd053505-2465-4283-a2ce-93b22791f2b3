<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Factory;

use U2\Entity\Item;
use U2\Import\Configuration\Field\BooleanFieldConfiguration;
use U2\Import\Configuration\Field\LiteralFieldConfiguration;
use U2\Import\Configuration\ImportConfig;

class ItemConfigurationFactory implements ConfigurationFactory
{
    public static function create(): ImportConfig
    {
        return new ImportConfig(
            class: Item::class,
            fields: [
                new LiteralFieldConfiguration(
                    id: 'name',
                    label: 'Name',
                ),
                new BooleanFieldConfiguration(
                    id: 'editable',
                    label: 'Editable',
                    help: 'Boolean',
                ),
                new LiteralFieldConfiguration(
                    id: 'refId',
                    label: 'RefId',
                ),
                new LiteralFieldConfiguration(
                    id: 'description',
                    label: 'Description',
                ),
                new LiteralFieldConfiguration(
                    id: 'type',
                    label: 'Type',
                    help: '<p>Possible values are:</p><ul><li>Checkbox</li><li>Diff</li><li>Value</li><li>Number</li><li>Rate</li><li>Text</li></ul>',
                ),
                new LiteralFieldConfiguration(
                    id: 'exchangeMethod',
                    label: 'Exchange Method',
                    help: '<p>Only required for items of type money. Defined using a number code. The following values are valid:</p><ul><li>Average exchange rate: 1</li><li>Current exchange rate: 2</li><li>Average exchange rate of previous period: 3</li><li>Latest exchange rate of previous period: 4</li></ul>',
                ),
            ],
            updateMatchFields: ['refId'],
            factory: 'U2\Datasheets\Item\ItemFactory:create',
            factoryArguments: ['type', 'refId', 'name', 'description', 'editable'],
            help: null
        );
    }
}
