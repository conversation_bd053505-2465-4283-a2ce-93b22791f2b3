<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Factory\Task;

use U2\Entity\Task\TaskType\TaxCredit;
use U2\Import\Configuration\Factory\ConfigurationFactory;
use U2\Import\Configuration\Field\Factory\Task\AdditionFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\AssigneeFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\BeginningOfYearFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\ConsumptionFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\CorrectionFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\CreditTypeFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\DueDateFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\PeriodFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\ReporterFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\RestrictionsFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\StatusFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\TaskDescriptionFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\TaxTypeFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\UnitFieldFactory;
use U2\Import\Configuration\ImportConfig;

class TaxCreditConfigurationFactory implements ConfigurationFactory
{
    public static function create(): ImportConfig
    {
        return new ImportConfig(
            class: TaxCredit::class,
            fields: [
                AssigneeFieldFactory::create(),
                DueDateFieldFactory::create(),
                ReporterFieldFactory::create(),
                PeriodFieldFactory::create(),
                UnitFieldFactory::create(),
                TaxTypeFieldFactory::create(),
                CreditTypeFieldFactory::create(),
                BeginningOfYearFieldFactory::create(),
                CorrectionFieldFactory::create(),
                ConsumptionFieldFactory::create(),
                AdditionFieldFactory::create(),
                RestrictionsFieldFactory::create(),
                TaskDescriptionFieldFactory::create(),
                StatusFieldFactory::create(),
            ],
            updateMatchFields: [],
            factory: 'U2\Task\TaskTypeFactory:createWithDefaults',
            factoryArguments: [TaxCredit::class],
            help: null
        );
    }
}
