<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Factory\Task;

use U2\Entity\Task\TaskType\UnitPeriod;
use U2\Import\Configuration\Factory\ConfigurationFactory;
use U2\Import\Configuration\Field\Factory\Task\AssigneeFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\DueDateFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\LayoutCollectionsFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\PeriodFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\ReporterFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\StatusFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\TaskDescriptionFieldFactory;
use U2\Import\Configuration\Field\Factory\Task\UnitFieldFactory;
use U2\Import\Configuration\ImportConfig;

class UnitPeriodConfigurationFactory implements ConfigurationFactory
{
    public static function create(): ImportConfig
    {
        return new ImportConfig(
            class: UnitPeriod::class,
            fields: [
                AssigneeFieldFactory::create(),
                DueDateFieldFactory::create(),
                ReporterFieldFactory::create(),
                LayoutCollectionsFieldFactory::create(),
                UnitFieldFactory::create(),
                PeriodFieldFactory::create(),
                StatusFieldFactory::create(),
                TaskDescriptionFieldFactory::create(),
            ],
            updateMatchFields: [],
            factory: 'U2\Task\TaskTypeFactory:createWithDefaults',
            factoryArguments: [UnitPeriod::class],
            help: null
        );
    }
}
