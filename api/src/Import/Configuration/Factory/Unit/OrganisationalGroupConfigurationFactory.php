<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Factory\Unit;

use U2\Entity\Auditor;
use U2\Entity\Currency;
use U2\Entity\OrganisationalGroup;
use U2\Entity\User;
use U2\Import\Configuration\Factory\ConfigurationFactory;
use U2\Import\Configuration\Field\DateFieldConfiguration;
use U2\Import\Configuration\Field\LiteralFieldConfiguration;
use U2\Import\Configuration\Field\LookupFieldConfiguration;
use U2\Import\Configuration\ImportConfig;

class OrganisationalGroupConfigurationFactory implements ConfigurationFactory
{
    public static function create(): ImportConfig
    {
        return new ImportConfig(
            class: OrganisationalGroup::class,
            fields: [
                new LiteralFieldConfiguration(
                    id: 'refId',
                    label: 'RefId',
                ),
                new LiteralFieldConfiguration(
                    id: 'name',
                    label: 'Name',
                ),
                new LookupFieldConfiguration(
                    id: 'currency',
                    label: 'Currency',
                    class: Currency::class,
                    lookupField: 'iso4217code',
                ),
                new LookupFieldConfiguration(
                    id: 'auditor',
                    label: 'Auditor',
                    class: Auditor::class,
                    lookupField: 'name',
                ),
                new LookupFieldConfiguration(
                    id: 'taxAdvisor',
                    label: 'Tax Advisor',
                    class: Auditor::class,
                    lookupField: 'name',
                ),
                new LookupFieldConfiguration(
                    id: 'contactUser',
                    label: 'Contact User',
                    class: User::class,
                    lookupField: 'username',
                ),
                new DateFieldConfiguration(
                    id: 'validFrom',
                    label: 'Valid From',
                ),
                new DateFieldConfiguration(
                    id: 'validTo',
                    label: 'Valid To',
                ),
                new LiteralFieldConfiguration(
                    id: 'description',
                    label: 'Description',
                ),
            ],
            updateMatchFields: ['refId'],
            factory: null,
            factoryArguments: [],
            help: null
        );
    }
}
