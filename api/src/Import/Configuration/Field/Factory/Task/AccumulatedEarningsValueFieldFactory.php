<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Field\Factory\Task;

use U2\Import\Configuration\Field\MoneyAmountFieldConfiguration;

class AccumulatedEarningsValueFieldFactory implements FieldFactory
{
    public static function create(): MoneyAmountFieldConfiguration
    {
        return new MoneyAmountFieldConfiguration(
            id: 'accumulatedEarningsValue.baseValue',
            label: 'Accumulated Earnings Value',
        );
    }
}
