<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Field\Factory\Task;

use U2\Import\Configuration\Field\DateFieldConfiguration;

class ContractExpiryDateFieldFactory implements FieldFactory
{
    public static function create(): DateFieldConfiguration
    {
        return new DateFieldConfiguration(
            id: 'contractExpiryDate',
            label: 'Contract Expiry Date',
            help: 'Maturity date/end date of contract (if no maturity, enter 31.12.9999)',
        );
    }
}
