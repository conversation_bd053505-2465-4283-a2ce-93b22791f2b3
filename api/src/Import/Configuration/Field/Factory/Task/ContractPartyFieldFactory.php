<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Field\Factory\Task;

use U2\Import\Configuration\Field\LiteralFieldConfiguration;

class ContractPartyFieldFactory implements FieldFactory
{
    public static function create(): LiteralFieldConfiguration
    {
        return new LiteralFieldConfiguration(
            id: 'contractParty',
            label: 'Contract Party',
            help: 'One of: borrower, creditor',
        );
    }
}
