<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Field\Factory\Task;

use U2\Import\Configuration\Field\MoneyAmountFieldConfiguration;

class ContractualAmountFieldFactory implements FieldFactory
{
    public static function create(): MoneyAmountFieldConfiguration
    {
        return new MoneyAmountFieldConfiguration(
            id: 'contractualAmount.baseValue',
            label: 'Contractual amount of transaction at transaction date',
        );
    }
}
