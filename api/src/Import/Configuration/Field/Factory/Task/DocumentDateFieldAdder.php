<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Field\Factory\Task;

use U2\Import\Configuration\Field\DateTimeFieldConfiguration;

class DocumentDateFieldAdder implements FieldFactory
{
    public static function create(): DateTimeFieldConfiguration
    {
        return new DateTimeFieldConfiguration(
            id: 'documentDate',
            label: 'Document Date',
        );
    }
}
