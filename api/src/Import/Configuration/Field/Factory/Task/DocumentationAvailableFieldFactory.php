<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Field\Factory\Task;

use U2\Import\Configuration\Field\BooleanFieldConfiguration;

class DocumentationAvailableFieldFactory implements FieldFactory
{
    public static function create(): BooleanFieldConfiguration
    {
        return new BooleanFieldConfiguration(
            id: 'documentationAvailable',
            label: 'Documenation Available',
            help: 'Boolean',
        );
    }
}
