<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Field\Factory\Task;

use U2\Entity\Item;
use U2\Import\Configuration\Field\LookupFieldConfiguration;

class ItemFieldFactory implements FieldFactory
{
    public static function create(): LookupFieldConfiguration
    {
        return new LookupFieldConfiguration(
            id: 'item',
            label: 'Item',
            class: Item::class,
            lookupField: 'refId',
        );
    }
}
