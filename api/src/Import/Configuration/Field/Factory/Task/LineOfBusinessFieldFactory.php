<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Field\Factory\Task;

use U2\Entity\Configuration\Field\LineOfBusiness;
use U2\Import\Configuration\Field\LookupFieldConfiguration;

class LineOfBusinessFieldFactory implements FieldFactory
{
    public static function create(): LookupFieldConfiguration
    {
        return new LookupFieldConfiguration(
            id: 'lineOfBusiness',
            label: 'Line of Business',
            class: LineOfBusiness::class,
            lookupField: 'name',
        );
    }
}
