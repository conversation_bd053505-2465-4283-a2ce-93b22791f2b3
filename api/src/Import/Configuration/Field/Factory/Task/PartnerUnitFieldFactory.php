<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Field\Factory\Task;

use U2\Entity\Unit;
use U2\Import\Configuration\Field\FieldConfigurationInterface;
use U2\Import\Configuration\Field\LookupFieldConfiguration;

class PartnerUnitFieldFactory implements FieldFactory
{
    public static function create(): FieldConfigurationInterface
    {
        return new LookupFieldConfiguration(
            id: 'partnerUnit',
            label: 'Partner Unit',
            class: Unit::class,
            lookupField: 'refId',
        );
    }
}
