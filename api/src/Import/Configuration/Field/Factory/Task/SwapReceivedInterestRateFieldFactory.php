<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Field\Factory\Task;

use U2\Import\Configuration\Field\LiteralFieldConfiguration;

class SwapReceivedInterestRateFieldFactory implements FieldFactory
{
    public static function create(): LiteralFieldConfiguration
    {
        return new LiteralFieldConfiguration(
            id: 'swapReceivedInterestRate',
            label: 'Swap received Interest Rate (for Buyer)',
        );
    }
}
