<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Field\Factory\Task;

use U2\Import\Configuration\Field\MoneyAmountFieldConfiguration;

class TotalReinsuranceRecoverableFieldFactory implements FieldFactory
{
    public static function create(): MoneyAmountFieldConfiguration
    {
        return new MoneyAmountFieldConfiguration(
            id: 'totalReinsuranceRecoverable.baseValue',
            label: 'Total reinsurance Recoverable at Reporting date',
        );
    }
}
