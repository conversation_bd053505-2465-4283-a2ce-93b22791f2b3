<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Field\Factory\Task;

use U2\Import\Configuration\Field\MoneyAmountFieldConfiguration;

class ValueAtStartDateFieldFactory implements FieldFactory
{
    public static function create(): MoneyAmountFieldConfiguration
    {
        return new MoneyAmountFieldConfiguration(
            id: 'valueAtStartDate.baseValue',
            label: 'Value of transaction at starting date',
        );
    }
}
