<?php

declare(strict_types=1);
namespace U2\Menu;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use U2\Contract\ContractTableType;
use U2\Entity\AuthorizationItem;
use U2\Entity\Task\TaskType\Contract;
use U2\Event\Menu\ConfigureToolsMenuEvent;
use U2\Module\Module;
use U2\Module\ModuleStatusChecker;
use U2\Security\Authorization\AuthorizationRight;

class ContractManagementMenuSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private readonly AuthorizationCheckerInterface $authorizationChecker,
        private readonly TaskTypeMenuBuilder $genericMenuLayoutBuilder,
        private readonly ModuleStatusChecker $enabledStatusChecker,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ConfigureToolsMenuEvent::class => 'onMenuConfigureTools',
        ];
    }

    public function onMenuConfigureTools(ConfigureToolsMenuEvent $event): void
    {
        if (!$this->enabledStatusChecker->isEnabled(Module::contractManagement)) {
            return;
        }

        if (!$this->authorizationChecker->isGranted(AuthorizationItem::Contract->value . ':' . AuthorizationRight::READ->value)) {
            return;
        }

        $factory = $event->getFactory();

        $contractMenu = $this->genericMenuLayoutBuilder->init($factory, Contract::class)
            ->setMenuTitle('u2_contractmanagement.contract.plural')
            ->setTableViewConfigurationFullyQualifiedClass(ContractTableType::class)
            ->build();

        $toolsMenu = $event->getMenu();
        $toolsMenu->addChild($contractMenu);
    }
}
