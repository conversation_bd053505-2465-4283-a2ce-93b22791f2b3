<?php

declare(strict_types=1);
namespace U2\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250613072052 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Go through all authorizations and remove "ACCESS", "WRITE", "TRANSFER".';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
               UPDATE authorization
               SET rights = (
                   SELECT IFNULL(
                       JSON_ARRAYAGG(value),
                       JSON_ARRAY()
                   )
                   FROM JSON_TABLE(
                       rights,
                       '$[*]' COLUMNS (value VARCHAR(255) PATH '$')
                   ) AS jt
                   WHERE value NOT IN ('ACCESS', 'WRITE', 'TRANSFER')
               )
               WHERE JSON_LENGTH(rights) IS NOT NULL
            SQL
        );
    }
}
