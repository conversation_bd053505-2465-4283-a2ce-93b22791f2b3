<?php

declare(strict_types=1);
namespace U2\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250613072053 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove UNIT and PERIOD authorizations.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
              DELETE FROM authorization_profile_authorization
                WHERE authorization_id IN (
                    SELECT a.id
                    FROM authorization a
                    WHERE a.item = 'UNIT'
                    OR a.item = 'PERIOD'
                );
            SQL
        );

        $this->addSql(<<<'SQL'
              DELETE FROM authorization WHERE item = 'UNIT' OR item = 'PERIOD'
            SQL
        );
    }
}
