<?php

declare(strict_types=1);
namespace U2\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use U2\Entity\SystemMessage;
use U2\FeatureFlags\FeatureFlagsTrait;

/**
 * @extends ServiceEntityRepository<SystemMessage>
 */
class SystemMessageRepository extends ServiceEntityRepository
{
    use FeatureFlagsTrait;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SystemMessage::class);
    }

    /**
     * @return SystemMessage[]
     */
    public function getActiveSystemMessages(): array
    {
        return $this->getEntityManager()
            ->createQuery(<<<DQL
                SELECT s
                FROM U2\Entity\SystemMessage s
                WHERE (s.displayFrom <= :now AND :now < s.displayTo)
                OR
                (s.displayFrom IS NULL AND s.displayTo IS NULL)
                OR
                (s.displayFrom IS NULL AND s.displayTo > :now)
                OR
                (s.displayTo IS NULL AND s.displayFrom <= :now)
                DQL
            )
            ->setParameter('now', new \DateTime('now'))
            ->getResult();
    }
}
