<?php

declare(strict_types=1);
namespace U2\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use U2\Entity\Workflow\Transition;

/**
 * @extends ServiceEntityRepository<Transition>
 */
class TransitionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Transition::class);
    }
}
