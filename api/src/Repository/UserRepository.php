<?php

declare(strict_types=1);
namespace U2\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\PasswordUpgraderInterface;
use U2\Entity\Authorization;
use U2\Entity\AuthorizationItem;
use U2\Entity\AuthorizationProfile;
use U2\Entity\Unit;
use U2\Entity\User;
use U2\Security\UserRoles;

/**
 * @extends ServiceEntityRepository<User>
 */
class UserRepository extends ServiceEntityRepository implements PasswordUpgraderInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, User::class);
    }

    /**
     * @return array<User>
     */
    public function findAssignedToAuthorizationItem(AuthorizationItem $authorizationItem): array
    {
        // Create a query that selects users and only select these that belong to the authorization and or its profiles
        $qb = $this->createQueryBuilder('user');

        /** @var array<User> $users */
        $users = $qb->select('DISTINCT user')
            ->leftJoin(Authorization::class, 'authorization', 'WITH', 'authorization.item = :authorizationItem')
            ->leftJoin(AuthorizationProfile::class, 'profile', 'WITH', 'authorization MEMBER OF profile.authorizations')
            ->leftJoin('authorization.groups', 'authorizationGroup')
            ->leftJoin('profile.groups', 'profileGroup')
            ->where('user MEMBER OF profile.directUsers')
            ->orWhere('user MEMBER OF profileGroup.users')
            ->orWhere('user MEMBER OF authorizationGroup.users')
            ->orWhere('user MEMBER OF authorization.directUsers')
            ->getQuery()
            ->setParameters([
                'authorizationItem' => $authorizationItem,
            ])
            ->getResult();

        return $users;
    }

    /**
     * @return User[]
     */
    public function findByUsernameOrEmail(string $usernameOrEmail): array
    {
        $qb = $this->createQueryBuilder('user');
        $qb->select(
            [
                'user',
                'contact',
            ]
        )
            ->leftJoin('user.contact', 'contact')
            ->where('contact.email = :email')
            ->orWhere('user.username = :username');

        return $qb->getQuery()
            ->setParameters([
                'email' => $usernameOrEmail,
                'username' => $usernameOrEmail,
            ])
            ->getResult();
    }

    /**
     * @return User[]
     */
    public function findAdmins(): array
    {
        $qb = $this->createQueryBuilder('user');
        $qb
            ->select(['user', 'contact'])
            ->leftJoin('user.contact', 'contact')
            ->leftJoin('user.groups', 'g')
            ->where('user.accountExpires IS NULL or user.accountExpires > :now')
            ->andWhere('user.userRoles LIKE :role OR g.roles LIKE :role')
            ->groupBy('user.id');

        /** @var User[] $result */
        $result = $qb->getQuery()
            ->setParameter('role', '%' . UserRoles::Admin->value . '%')
            ->setParameter('now', new \DateTime())
            ->getResult();

        return $result;
    }

    public function getByUsername(string $username): User
    {
        $user = $this->findByUsername($username);
        if (null === $user) {
            throw new UserNotFoundException();
        }

        return $user;
    }

    public function findByUsername(string $username): ?User
    {
        return $this->findOneBy(['username' => $username]);
    }

    public function getAllWithContacts(): array
    {
        $qb = $this->createQueryBuilder('user');
        $qb->select(
            [
                'user',
                'contact',
            ]
        )
            ->leftJoin('u.contact', 'contact');

        return $qb->getQuery()
            ->getResult();
    }

    /**
     * This method is used in the UserUnitsType form in order to force
     * doctrine to eagerly-load the User's contact, as it tries to
     * validate it even without hydrating the object.
     *
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function findWithJoinedContact(int $userId): User
    {
        /** @var User $singleResult */
        $singleResult = $this->createQueryBuilder('user')
            ->select([
                'user',
                'contact',
            ])
            ->where('user.id = :id')
            ->leftJoin('user.contact', 'contact')
            ->setParameter('id', $userId)
            ->getQuery()
            ->getSingleResult();

        return $singleResult;
    }

    /**
     * @return User[]
     */
    public function findAssignedToUnitInherited(Unit $unit): array
    {
        $qb = $this->createQueryBuilder('user');
        $qb->select('DISTINCT user')
            ->leftJoin('user.groups', 'user_group')
            ->leftJoin('user_group.units', 'unit')
            ->where('unit = :unit');

        return $qb->getQuery()
            ->setParameter('unit', $unit)
            ->getResult();
    }

    /**
     * @return User[]
     */
    public function findAssignedToUnitDirect(Unit $unit): array
    {
        $qb = $this->createQueryBuilder('user');
        $qb->select('user')
            ->leftJoin('user.units', 'unit')
            ->where('unit = :unit');

        return $qb->getQuery()
            ->setParameter('unit', $unit)
            ->getResult();
    }

    public function upgradePassword(PasswordAuthenticatedUserInterface $user, string $newHashedPassword): void
    {
        \assert($user instanceof User);
        $user->setEncodedPassword($newHashedPassword);
        $this->getEntityManager()->flush();
    }
}
