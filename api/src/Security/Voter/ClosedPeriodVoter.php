<?php

declare(strict_types=1);
namespace U2\Security\Voter;

use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Vote;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use U2\Entity\Interfaces\Periodable;
use U2\Entity\User;

/**
 * @extends Voter<VoterAttributes::addAttachment|VoterAttributes::removeAttachment|VoterAttributes::delete|VoterAttributes::removeReview|VoterAttributes::review|VoterAttributes::write, Periodable>
 */
class ClosedPeriodVoter extends Voter
{
    public function supportsAttribute(string $attribute): bool
    {
        $supportedAttributes = [
            VoterAttributes::addAttachment,
            VoterAttributes::removeAttachment,
            VoterAttributes::delete,
            VoterAttributes::removeReview,
            VoterAttributes::review,
            VoterAttributes::write,
        ];

        return \in_array($attribute, $supportedAttributes, true);
    }

    public function supportsType(string $subjectType): bool
    {
        return is_a($subjectType, Periodable::class, true);
    }

    protected function supports(string $attribute, mixed $subject): bool
    {
        return true;
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token, ?Vote $vote = null): bool
    {
        $user = $token->getUser();

        if (!$user instanceof User) {
            return false;
        }

        if (null === $subject->getPeriod()) {
            return true;
        }

        return !$subject->getPeriod()->isClosed();
    }
}
