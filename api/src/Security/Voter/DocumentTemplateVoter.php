<?php

declare(strict_types=1);
namespace U2\Security\Voter;

use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Vote;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use U2\Entity\DocumentTemplate;
use U2\Security\UserRoles;

/**
 * @extends Voter<VoterAttributes::read|VoterAttributes::write|VoterAttributes::manage|VoterAttributes::removeAttachment|VoterAttributes::addAttachment|DocumentVoterAttributes::viewContent|DocumentVoterAttributes::editContent, DocumentTemplate>
 */
class DocumentTemplateVoter extends Voter
{
    public function __construct(private readonly Security $security)
    {
    }

    public function supportsAttribute(string $attribute): bool
    {
        $supportedAttributes = [
            DocumentVoterAttributes::viewContent,
            DocumentVoterAttributes::editContent,
            VoterAttributes::read,
            VoterAttributes::write,
            VoterAttributes::manage,
            VoterAttributes::removeAttachment,
            VoterAttributes::addAttachment,
        ];

        return \in_array($attribute, $supportedAttributes, true);
    }

    public function supportsType(string $subjectType): bool
    {
        return is_a($subjectType, DocumentTemplate::class, true);
    }

    protected function supports(string $attribute, mixed $subject): bool
    {
        return true;
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token, ?Vote $vote = null): bool
    {
        return $this->security->isGranted(UserRoles::Admin->value);
    }
}
