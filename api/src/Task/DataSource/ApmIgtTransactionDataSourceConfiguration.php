<?php

declare(strict_types=1);
namespace U2\Task\DataSource;

use Symfony\Bundle\SecurityBundle\Security;
use U2\DataSourcery\DataSource\DataSourceBuilder;
use U2\DataSourcery\Transformer\ArrayMapTransformer;
use U2\Entity\Task\TaskType;
use U2\Event\DataSourcery\PostGenerateQueryBuilderEvent;
use U2\Money\LinkedBaseLocalGroupMoneyInterface;
use U2\Repository\UnitRepository;
use U2\Task\Field\ContractPartyTypes;
use U2\Task\Field\PurposeOfInstrumentTypes;
use U2\Task\Field\TypeOfProtectionTypes;
use U2\Task\Interfaces\Typeable;
use U2\User\CurrentUserProvider;
use U2\Workflow\WorkflowManager;

abstract class ApmIgtTransactionDataSourceConfiguration extends AbstractTaskTypeDataSourceConfiguration
{
    public function __construct(
        Security $security,
        CurrentUserProvider $currentUserProvider,
        WorkflowManager $workflowManager,
        UnitRepository $unitRepository,
        private readonly DataSourceConfigurationBaseLocalGroupMoneyFieldAdder $baseLocalGroupMoneyDataSourceConfigurationFieldAdder,
    ) {
        parent::__construct($currentUserProvider, $workflowManager, $unitRepository, $security);
    }

    public function buildDataSource(DataSourceBuilder $builder): void
    {
        parent::buildDataSource($builder);

        /** @var class-string<TaskType&LinkedBaseLocalGroupMoneyInterface> $entityClass */
        $entityClass = static::getEntityClass();
        $moneyFields = $entityClass::getLinkedBaseLocalGroupMoneyFields();
        $this->baseLocalGroupMoneyDataSourceConfigurationFieldAdder->add($builder, $entityClass);

        \assert(is_a($entityClass, Typeable::class, true));
        $typeClass = $entityClass::getTypeClass();
        TypeFieldAdder::add($builder, $typeClass::getReadableMap());

        PartnerUnitFieldAdder::add($builder);
        PeriodFieldAdder::add($builder);

        foreach ($entityClass::getAllFields() as $fieldName) {
            if (\in_array($fieldName, $moneyFields, true)) {
                continue;
            }

            $dataSourceFieldAdderClass = \sprintf("\U2\Task\DataSource\%sFieldAdder", ucfirst($fieldName));
            $dataSourceFieldAdderClass::add($builder);
        }

        $builder
            ->addTransformer(new ArrayMapTransformer(PurposeOfInstrumentTypes::getReadableMap(), 'PurposeOfInstrument'));

        $builder
            ->addTransformer(new ArrayMapTransformer(TypeOfProtectionTypes::getReadableMap(), 'TypeOfProtection'));

        $builder
            ->addTransformer(new ArrayMapTransformer(ContractPartyTypes::getReadableMap(), 'ContractParty'));

        $this->baseLocalGroupMoneyDataSourceConfigurationFieldAdder->add($builder, static::getEntityClass());
    }

    protected function addAuthorisationRestrictions(DataSourceBuilder $builder): void
    {
        if ($this->ignorePermissions()) {
            return;
        }

        $user = $this->currentUserProvider->get();
        $userAssignedUnits = $this->unitRepository->findUserAssigned($user);

        $builder
            ->addEventListener(
                PostGenerateQueryBuilderEvent::class,
                static function (PostGenerateQueryBuilderEvent $event) use ($userAssignedUnits): void {
                    $fromAlias = $event->fromAlias;
                    $queryBuilder = $event->queryBuilder;

                    $queryBuilder->andWhere(
                        $queryBuilder->expr()->orX(
                            $fromAlias . '.unit IN (:units)',
                            $fromAlias . '.partnerUnit IN (:units)'
                        )
                    );
                    $queryBuilder->setParameter(':units', $userAssignedUnits);
                });
    }

    abstract public static function getEntityClass(): string;
}
