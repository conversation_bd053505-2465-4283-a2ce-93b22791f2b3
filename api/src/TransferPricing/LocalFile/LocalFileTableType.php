<?php

declare(strict_types=1);
namespace U2\TransferPricing\LocalFile;

use function Symfony\Component\Translation\t;

use U2\Entity\Task\TaskType\LocalFile;
use U2\Table\View\Column\ColumnDefinitionCollection;
use U2\TransferPricing\AbstractDocumentTableType;

class LocalFileTableType extends AbstractDocumentTableType
{
    public function getName(): string
    {
        return 'u2_tpm_local_file';
    }

    /**
     * This is a helper method to mimic the behavior of a form type definition. This method is called on the
     * constructor and the column collection is passed so the implementer can ->add() columns as it would be
     * done with form fields.
     */
    public function buildTable(ColumnDefinitionCollection $columnDefinitionCollection): void
    {
        parent::buildTable($columnDefinitionCollection);

        $columnDefinitionCollection
            ->addAfter(
                'Name',
                'Country',
                null,
                [
                    'sortable' => true,
                    'filterable' => true,
                    'label' => t('u2.country'),
                    'name' => t('u2.country'),
                ]
            );
    }

    public static function getEntityClass(): string
    {
        return LocalFile::class;
    }
}
