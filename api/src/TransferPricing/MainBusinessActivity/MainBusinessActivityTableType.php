<?php

declare(strict_types=1);
namespace U2\TransferPricing\MainBusinessActivity;

use function Symfony\Component\Translation\t;

use U2\Entity\Task\TaskType\MainBusinessActivity;
use U2\Table\View\Column\ColumnDefinitionCollection;
use U2\Task\TableType\AbstractTaskTypeTableType as BaseAbstractIssueTableType;
use U2\Task\TableType\PeriodColumnAdder;

class MainBusinessActivityTableType extends BaseAbstractIssueTableType
{
    public function getName(): string
    {
        return 'u2_tpm_main_business_activity';
    }

    public function buildTable(ColumnDefinitionCollection $columnDefinitionCollection): void
    {
        parent::buildTable($columnDefinitionCollection);

        PeriodColumnAdder::add($columnDefinitionCollection);

        $columnDefinitionCollection
            ->addAfter(
                'Period',
                'UnitRefId',
                null,
                [
                    'filterable' => true,
                    'sortable' => true,
                    'name' => t('u2.unit_ref_id'),
                ]
            )
            ->addAfter(
                'UnitRefId',
                'UnitName',
                null,
                [
                    'filterable' => true,
                    'sortable' => true,
                    'label' => t('u2.unit_name'),
                    'name' => t('u2.unit_name'),
                ]
            )
            ->addAfter(
                'UnitName',
                'BusinessActivity',
                null,
                [
                    'filterable' => true,
                    'sortable' => true,
                    'name' => t('u2_tpm.business_activity'),
                ]
            );
    }

    public static function getEntityClass(): string
    {
        return MainBusinessActivity::class;
    }
}
