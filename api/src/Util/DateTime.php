<?php

declare(strict_types=1);
namespace U2\Util;

use U2\Entity\Interfaces\ValidDateRange;

class DateTime
{
    public static function createNow(): \DateTime
    {
        $dateTime = \DateTime::createFromFormat('U', (string) time());
        \assert(false !== $dateTime);

        return $dateTime;
    }

    public static function getUltimoDate(?\DateTime $referenceDate = null): \DateTime
    {
        if (null === $referenceDate) {
            $referenceDate = new \DateTime();
        }
        $month = (int) $referenceDate->format('n');
        $monthToBeSet = $month > 1 ? $month - 1 : $month;
        $referenceDate->setDate((int) $referenceDate->format('Y'), $monthToBeSet, 1);

        return new \DateTime($referenceDate->format('Y-m-t'));
    }

    public static function getLastDayOfMonth(?\DateTime $referenceDate = null): \DateTime
    {
        $referenceDate = null === $referenceDate ? new \DateTime() : clone $referenceDate;

        $referenceDate->setTime(0, 0, 0);

        $year = (int) $referenceDate->format('Y');
        $month = (int) $referenceDate->format('n');
        $year = 12 === $month ? $year + 1 : $year;
        $month = 12 === $month ? 1 : $month + 1;

        $referenceDate->setDate($year, $month, 1);

        return $referenceDate->sub(new \DateInterval('P1D'));
    }

    public static function getReadableDateRange(\DateTime $startDate, \DateTime $endDate): string
    {
        $startFormat = 'd.';
        if ($startDate->format('m') !== $endDate->format('m')) {
            $startFormat .= ' M';
        }
        if ($startDate->format('Y') !== $endDate->format('Y')) {
            $startFormat .= ' Y';
        }

        return $startDate->format($startFormat) . ' - ' . $endDate->format('d. M Y');
    }

    /**
     * @return \DateTime[]
     */
    public static function getTargetWeek(\DateTime $targetDate): array
    {
        $firstDayOfWeek = self::getFirstDayOfWeek($targetDate);

        $week[1] = $firstDayOfWeek;
        for ($i = 2; $i <= 7; ++$i) {
            $day = clone $firstDayOfWeek;
            $week[$i] = $day->modify('+' . ($i - 1) . ' day');
        }

        return $week;
    }

    public static function getFirstDayOfWeek(\DateTime $targetDate): \DateTime
    {
        $firstDayOfWeek = clone $targetDate;
        // check if the given date is the first day of the week
        if ('1' !== $firstDayOfWeek->format('w')) {
            // set to the first day of the week
            $firstDayOfWeek->modify('Last monday');
        }

        return $firstDayOfWeek;
    }

    public static function isInDateRange(ValidDateRange $object, \DateTime $date): bool
    {
        $validTo = $object->getValidTo();
        if ($validTo instanceof \DateTime && $validTo < $date) {
            return false;
        }

        return !($object->getValidFrom() instanceof \DateTime && $object->getValidFrom() > $date);
    }
}
