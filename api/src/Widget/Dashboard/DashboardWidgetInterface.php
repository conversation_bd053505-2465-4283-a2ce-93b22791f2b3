<?php

declare(strict_types=1);
namespace U2\Widget\Dashboard;

use Symfony\Component\DependencyInjection\Attribute\AutoconfigureTag;

#[AutoconfigureTag()]
interface DashboardWidgetInterface
{
    public function getName(): string;

    public function isConfigurable(): bool;

    /**
     * @return array<string, mixed>
     */
    public function getParameters(): array;

    /**
     * @param array<string, mixed> $parameters
     */
    public function setParameters(array $parameters): void;

    /**
     * @return array<string, mixed>
     */
    public function getData(): array;
}
