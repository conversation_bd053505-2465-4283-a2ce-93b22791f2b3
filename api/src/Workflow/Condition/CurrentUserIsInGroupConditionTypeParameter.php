<?php

declare(strict_types=1);
namespace U2\Workflow\Condition;

use <PERSON>ymfony\Component\Serializer\Attribute\Groups;

class CurrentUserIsInGroupConditionTypeParameter implements ConditionTypeParameter
{
    #[Groups(groups: ['condition_type:read'])]
    public static function getType(): string
    {
        return 'array';
    }

    #[Groups(groups: ['condition_type:read'])]
    public static function getProperty(): string
    {
        return 'groups';
    }

    #[Groups(groups: ['condition_type:read'])]
    public static function getAllowedValues(): array
    {
        return [
            'Groups' => 'An array of group ids',
        ];
    }
}
