<?php

declare(strict_types=1);
namespace U2\Xbrl\De\Schema\WwwItzbundDeElan01;

use U2\Xbrl\De\Schema\WwwItzbundDeElan01\ELMAKOM\ELMAHeaderAType;
use U2\Xbrl\De\Schema\WwwItzbundDeElan01\ELMAKOM\ELMAVerfahrenAType;

class ELMAKOM
{
    private ?string $eLMAKOMVersion = null;

    public function __construct(
        private ELMAHeaderAType $eLMAHeader,
        private ELMAVerfahrenAType $eLMAVerfahren,
    ) {
    }

    public function getELMAKOMVersion(): ?string
    {
        return $this->eLMAKOMVersion;
    }

    public function setELMAKOMVersion(string $eLMAKOMVersion): self
    {
        $this->eLMAKOMVersion = $eLMAKOMVersion;

        return $this;
    }

    public function getELMAHeader(): ?ELMAHeaderAType
    {
        return $this->eLMAHeader;
    }

    public function setELMAHeader(ELMAHeaderAType $eLMAHeader): self
    {
        $this->eLMAHeader = $eLMAHeader;

        return $this;
    }

    public function getELMAVerfahren(): ?ELMAVerfahrenAType
    {
        return $this->eLMAVerfahren;
    }

    public function setELMAVerfahren(ELMAVerfahrenAType $eLMAVerfahren): self
    {
        $this->eLMAVerfahren = $eLMAVerfahren;

        return $this;
    }
}
