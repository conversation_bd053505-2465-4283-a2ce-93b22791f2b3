<?php

declare(strict_types=1);
namespace U2\Xbrl\Oecd;

use Html2Text\Html2Text;
use U2\Entity\CountryByCountryReportSection;
use U2\Entity\Task\TaskType\CountryByCountryReport;

class AdditionalInformationTextExtractor
{
    /**
     * @return string[]
     */
    public static function extract(CountryByCountryReport $countryByCountryReport): array
    {
        return array_reduce(
            $countryByCountryReport->getSections()->toArray(),
            static function (array $carry, CountryByCountryReportSection $section): array {
                if (self::isAdditionalInformationSection($section)) {
                    $carry = array_merge($carry, self::splitSectionContent($section));
                }

                return $carry;
            },
            []
        );
    }

    private static function isAdditionalInformationSection(CountryByCountryReportSection $section): bool
    {
        return preg_match(
            '~^\s*additional\s+info(rmation)?\s*$~i',
            $section->getName()
        ) > 0;
    }

    /**
     * @return string[]
     */
    private static function splitSectionContent(CountryByCountryReportSection $section): array
    {
        $result = [];

        $plainText = (new Html2Text($section->getContent()))->getText();

        $maxLength = 4000;
        do {
            $result[] = mb_substr($plainText, 0, $maxLength);
            $plainText = mb_substr($plainText, $maxLength);
        } while (mb_strlen($plainText) > 0);

        return $result;
    }
}
