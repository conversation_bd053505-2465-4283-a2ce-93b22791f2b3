{%- if is_granted("TPM_TRANSACTION:READ") and format == 'html' -%}
  {% table_theme transaction_table 'task/table_theme.html.twig' %}
  <HorizontalScrollContainer>
    {{ table(transaction_table) }}
  </HorizontalScrollContainer>
  <p class="print:hidden">
    <AppLink to="{{ path('u2_transaction_list', {(uql_query_parameter): uql}) }}">
      {{- 'u2_tpm.transaction_details'|trans }} <svg-icon icon="chevron-right" size="small" class="align-middle"></svg-icon>
    </AppLink>
  </p>
{%- else -%}
  {% table_theme transaction_table 'task/table_theme_pdf.html.twig' %}
  {{- table(transaction_table) -}}
{%- endif -%}
