@clear-database
Feature: Auditor Authorisation
  As a user without admin permissions
  I should have no access to any feature of Auditors

  Background:
    Given the following Auditor:
      | Name     |
      | KPMG     |
      | Deloitte |
    And I am logged in

  Scenario: A User without admin rights tries to edit a Auditor
    When I go to "/configuration/auditors/1"
    Then I should see "403 Access Denied"

  Scenario: A User without admin rights tries to list the Auditors
    When I go to "/configuration/auditors"
    Then I should see "403 Access Denied"

  Scenario: A User without admin rights tries to create a Auditor
    When I go to "/configuration/auditors/new"
    Then I should see "403 Access Denied"
