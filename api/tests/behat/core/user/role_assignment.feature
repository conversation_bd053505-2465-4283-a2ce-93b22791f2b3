@clear-database
Feature: User Role Assignment
  In order to manage the roles of user
  As a user with ROLE_USER_GROUP_ADMIN
  I should be able to add and remove roles in the user edit page

  Scenario: A user group admin assigns a role to a user
    Given I am logged in as a user group administrator
    And there is a user named test_user
    When I am on "/users/2"
    Then I should not see "User Group Admin" in the "#assigned-roles" element
    When I click the "edit" button in "Assigned Roles"
    And I check "User Group Admin"
    And I click the "Save roles" button in the dialog
    Then I should be on "/users/2"
    And I should not see "User Group Admin" in the "#assigned-roles" element
