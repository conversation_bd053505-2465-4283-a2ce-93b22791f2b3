@clear-database
Feature: Tax Assessment Monitor Navigation - New
  In order to manage Tax Assessment Monitor
  As a user allowed to the TCM Tax Assessment Monitor
  I should be able to navigate through the Tax Assessment Monitor pages

  Background:
    Given the following Authorization:
      | Name                               | Item                       | Rights       |
      | Tax Assessment Monitor Full Access | TCM_TAX_ASSESSMENT_MONITOR | CREATE, READ |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                            | Initial Status | Transitions |
      | Tax Assessment Monitor Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id                 | Workflow                        |
      | tcm_tax_assessment_monitor | Tax Assessment Monitor Workflow |
    And I have the authorization "Tax Assessment Monitor Full Access"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/tcm/tax-assessment-monitor"
    When I click the "New" button in "Page Controls"
    Then I should be on "/tcm/tax-assessment-monitor/new"
