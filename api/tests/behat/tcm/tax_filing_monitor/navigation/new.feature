@clear-database
Feature: Tax Filing Monitor Navigation - New
  In order to manage Tax Filing Monitor
  As a user allowed to the TCM Tax Filing Monitor
  I should be able to navigate through the Tax Filing Monitor pages

  Background:
    Given the following Authorization:
      | Name                           | Item                   | Rights       |
      | Tax Filing Monitor Full Access | TCM_TAX_FILING_MONITOR | CREATE, READ |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                        | Initial Status | Transitions |
      | Tax Filing Monitor Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id             | Workflow                    |
      | tcm_tax_filing_monitor | Tax Filing Monitor Workflow |
    And I have the authorization "Tax Filing Monitor Full Access"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/tcm/tax-filing-monitor"
    When I click the "New" button in "Page Controls"
    Then I should be on "/tcm/tax-filing-monitor/new"
