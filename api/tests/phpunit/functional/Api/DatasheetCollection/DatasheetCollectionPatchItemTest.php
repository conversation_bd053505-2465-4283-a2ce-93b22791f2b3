<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\DatasheetCollection;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\DatasheetCollectionFactory;
use U2\DataFixtures\Example\DatasheetFactory;
use U2\DataFixtures\Example\ItemCountryReportFactory;
use U2\DataFixtures\Example\UserFactory;

/**
 * @covers \U2\Entity\DatasheetCollection
 */
class DatasheetCollectionPatchItemTest extends ApiTestCase
{
    public function test_updating_a_layout(): void
    {
        $admin = UserFactory::getAdmin()->_real();
        $layoutCollection = DatasheetCollectionFactory::createOne([
            'name' => 'Old Name',
            'public' => false,
            'itemCountryReports' => [
                $report1 = ItemCountryReportFactory::createOne(),
                $report2 = ItemCountryReportFactory::createOne(),
            ],
        ]);
        $report3 = ItemCountryReportFactory::createOne();

        $newLayoutIris = array_map(fn ($layout): string => "/api/layouts/{$layout->getId()}", DatasheetFactory::createMany(3));

        self::assertNotEquals($layoutCollection->getLayouts()->map(fn ($layout): string => "/api/layouts/{$layout->getId()}"), $newLayoutIris);

        $client = self::createClientWithAuth($admin);
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/layout-collections/%s', $layoutCollection->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'name' => 'New Name',
                    'public' => true,
                    'itemCountryReports' => [
                        "/api/item-country-reports/{$report1->getId()}",
                        "/api/item-country-reports/{$report3->getId()}",
                        "/api/item-country-reports/{$report2->getId()}",
                    ],
                ],
            ]
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonContains([
            '@id' => \sprintf('/api/layout-collections/%s', $layoutCollection->getId()),
            '@type' => 'LayoutCollection',
            'id' => $layoutCollection->getId()->toBase32(),
            'name' => 'New Name',
            'public' => true,
            'itemCountryReports' => [
                "/api/item-country-reports/{$report1->getId()}",
                "/api/item-country-reports/{$report3->getId()}",
                "/api/item-country-reports/{$report2->getId()}",
            ],
            'layoutCount' => 3,
        ]);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
    }

    public function test_update_item_as_an_unauthorized_user(): void
    {
        $unauthorizedUser = UserFactory::createOne(['username' => 'user'])->_real();
        $layoutCollection = DatasheetCollectionFactory::createOne();
        $client = self::createClientWithAuth($unauthorizedUser);
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/layout-collections/%s', $layoutCollection->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'name' => 'New Period Name',
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
