<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\Item;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\UserFactory;
use U2\Datasheets\Item\Formula\ItemDependents;
use U2\Datasheets\Item\ItemTypes;

/**
 * @covers \U2\Entity\Item
 */
class ItemPostTest extends ApiTestCase
{
    public function test_create_percent_item(): void
    {
        $admin = UserFactory::getAdmin();
        $client = self::createClientWithAuth($admin);

        // Ensure the cache is filled
        $itemDependents = static::getService(ItemDependents::class);
        $itemDependents->all();

        $cache = static::getContainer()->get('tenant.cache');
        $itemDependents = $cache->getItem(ItemDependents::cacheKey);
        self::assertTrue($itemDependents->isHit());

        // When
        $response = $client->request(
            HttpOperation::METHOD_POST,
            '/api/items',
            [
                'json' => [
                    'name' => 'Test Item',
                    'refId' => 'Test RefId',
                    'description' => 'Test Description',
                    'editable' => true,
                    'exchangeMethod' => 1,
                    'formulaReadable' => '',
                    'formula' => '1+1',  // Formula can be only written via formulaReadable
                    'type' => ItemTypes::CHECKBOX,
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_CREATED);

        $responseContent = json_decode($response->getContent());
        \assert($responseContent instanceof \stdClass && isset($responseContent->id));
        $id = $responseContent->id;

        self::assertJsonEquals([
            '@context' => '/api/contexts/Item',
            '@id' => '/api/items/' . $id,
            '@type' => 'Item',
            'id' => $id,
            'editable' => true,
            'formulaReadable' => null,
            'formula' => null,
            'type' => ItemTypes::CHECKBOX,
            'name' => 'Test Item',
            'refId' => 'Test RefId',
            'description' => 'Test Description',
            'exchangeMethod' => 1,
        ]);

        // Ensure cache tag has been invalidated
        $itemDependents = $cache->getItem(ItemDependents::cacheKey);
        self::assertFalse($itemDependents->isHit());
    }

    public function test_create_diff_item(): void
    {
        $admin = UserFactory::getAdmin();
        $client = self::createClientWithAuth($admin);

        // Ensure the cache is filled
        $itemDependents = static::getContainer()->get(ItemDependents::class);
        $itemDependents->all();

        $cache = static::getContainer()->get('tenant.cache');
        $itemDependents = $cache->getItem(ItemDependents::cacheKey);
        self::assertTrue($itemDependents->isHit());

        // When
        $response = $client->request(
            HttpOperation::METHOD_POST,
            '/api/items',
            [
                'json' => [
                    'name' => 'Test Item',
                    'refId' => 'Test RefId',
                    'description' => 'Test Description',
                    'editable' => false,
                    'exchangeMethod' => null,
                    'formula' => '2+2', // Formula can be only written via formulaReadable
                    'formulaReadable' => '1+1',
                    'type' => ItemTypes::DIFF,
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_CREATED);

        $responseContent = json_decode($response->getContent());
        \assert($responseContent instanceof \stdClass && isset($responseContent->id));
        $id = $responseContent->id;

        self::assertJsonEquals([
            '@context' => '/api/contexts/Item',
            '@id' => '/api/items/' . $id,
            '@type' => 'Item',
            'id' => $id,
            'name' => 'Test Item',
            'refId' => 'Test RefId',
            'description' => 'Test Description',
            'editable' => false,
            'exchangeMethod' => null,
            'formulaReadable' => '1+1',
            'formula' => '1+1',
            'type' => ItemTypes::DIFF,
        ]);

        // Ensure cache tag has been invalidated
        $itemDependents = $cache->getItem(ItemDependents::cacheKey);
        self::assertFalse($itemDependents->isHit());
    }

    public function test_create_money_item_without_exchange_rate_fails(): void
    {
        $admin = UserFactory::getAdmin();
        $client = self::createClientWithAuth($admin);

        // When
        $response = $client->request(
            HttpOperation::METHOD_POST,
            '/api/items',
            [
                'json' => [
                    'name' => 'Test Item',
                    'refId' => 'Test RefId',
                    'description' => 'Test Description',
                    'editable' => true,
                    'exchangeMethod' => null,
                    'formulaReadable' => null,
                    'type' => ItemTypes::MONEY,
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_UNPROCESSABLE_ENTITY);
        $responseContent = $response->toArray(false);
        \assert(isset($responseContent['detail']));
        self::assertSame($responseContent['detail'], 'exchangeMethod: This value should not be null.');
    }

    public function test_create_percent_item_as_unauthorized(): void
    {
        $user = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($user);

        // When
        $client->request(
            HttpOperation::METHOD_POST,
            '/api/items',
            [
                'json' => [
                    'name' => 'Test Item',
                    'refId' => 'Test RefId',
                    'description' => 'Test Description',
                    'editable' => true,
                    'exchangeMethod' => 1,
                    'formulaReadable' => null,
                    'type' => null,
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
