<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\ItemUnitHierarchyValue;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\Api\Resource\ItemUnitHierarchyValue;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\ItemFactory;
use U2\DataFixtures\Example\LegalUnitFactory;
use U2\DataFixtures\Example\NumberItemUnitValueFactory;
use U2\DataFixtures\Example\PeriodFactory;
use U2\DataFixtures\Example\UnitHierarchyDefinitionFactory;
use U2\DataFixtures\Example\UnitHierarchyFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\AuthorizationItem;
use U2\Security\Authorization\AuthorizationRight;

/**
 * @covers \U2\Api\Resource\NumberItemUnitHierarchyValue
 */
class NumberItemUnitHierarchyValueGetTest extends ApiTestCase
{
    public function test_get_item(): void
    {
        $unit1 = LegalUnitFactory::createOne(['refId' => 'Unit 1']);
        $unit2 = LegalUnitFactory::createOne(['refId' => 'Unit 2']);
        $unit3 = LegalUnitFactory::createOne(['refId' => 'Unit 3']);

        $user = UserFactory::createOne(
            [
                'authorizations' => [
                    AuthorizationFactory::new(
                        [
                            'item' => AuthorizationItem::UnitPeriod->value,
                            'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value],
                        ]
                    ),
                ],
                'units' => [$unit1->_real(), $unit2->_real(), $unit3->_real()],
            ]
        )->_real();
        $client = self::createClientWithAuth($user);

        $period = PeriodFactory::createOne();

        $unitHierarchy = UnitHierarchyFactory::createOne();
        UnitHierarchyDefinitionFactory::new()
            ->withUnitHierarchy($unitHierarchy)
            ->sequence(
                [
                    ['unit' => $unit1],
                    ['unit' => $unit2],
                    ['unit' => $unit3],
                ])->create();

        $item = ItemFactory::new()->editable()->number()->create();
        NumberItemUnitValueFactory::new()
            ->withItem($item)
            ->withValue('10')
            ->with([
                'unit' => $unit1,
                'period' => $period,
            ])
            ->create();

        NumberItemUnitValueFactory::new()
            ->withItem($item)
            ->withValue('5')
            ->with([
                'unit' => $unit2,
                'period' => $period,
            ])
            ->create();

        NumberItemUnitValueFactory::new()
            ->withItem($item)
            ->withValue('7')
            ->with([
                'unit' => $unit3,
                'period' => $period,
            ])
            ->create();

        self::assertSame(3, NumberItemUnitValueFactory::count());
        self::assertSame(3, UnitHierarchyDefinitionFactory::count());

        // When
        $unitHierarchyValueId = ItemUnitHierarchyValue::createId($item->_real(), $period->_real(), $unitHierarchy->_real());
        $requestUrl = \sprintf('/api/item-unit-hierarchy-values/%s', $unitHierarchyValueId);
        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl,
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        self::assertJsonEquals([
            '@context' => '/api/contexts/NumberItemUnitHierarchyValue',
            '@id' => $requestUrl,
            '@type' => 'NumberItemUnitHierarchyValue',
            'id' => $unitHierarchyValueId,
            'value' => '22.0000',
            'unitHierarchy' => '/api/unit-hierarchies/' . $unitHierarchy->getId(),
            'period' => '/api/periods/' . $period->getId(),
            'item' => '/api/items/' . $item->getId(),
        ]);
    }

    public function test_get_item_as_user_without_unit_period_read_authorization(): void
    {
        $user = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($user);

        $period = PeriodFactory::createOne();

        $unit = LegalUnitFactory::createOne(['refId' => 'Unit 1']);
        $unitHierarchy = UnitHierarchyFactory::createOne();
        UnitHierarchyDefinitionFactory::new()->withUnitHierarchy($unitHierarchy)->with(['unit' => $unit])->create();

        $item = ItemFactory::new()->editable()->number()->create();
        NumberItemUnitValueFactory::new()
            ->withItem($item)
            ->create([
                'unit' => $unit,
                'period' => $period,
            ]);

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/item-unit-hierarchy-values/%s', ItemUnitHierarchyValue::createId($item->_real(), $period->_real(), $unitHierarchy->_real())),
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_get_item_as_user_without_access_to_all_units_in_hierarchy(): void
    {
        $user = UserFactory::createOne(
            [
                'authorizations' => [
                    AuthorizationFactory::new(
                        [
                            'item' => AuthorizationItem::UnitPeriod->value,
                            'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value],
                        ]
                    ),
                ],
            ]
        )->_real();
        $client = self::createClientWithAuth($user);

        $period = PeriodFactory::createOne();

        $unit = LegalUnitFactory::createOne(['refId' => 'Unit 1']);
        $unitHierarchy = UnitHierarchyFactory::createOne();
        UnitHierarchyDefinitionFactory::new()->withUnitHierarchy($unitHierarchy)->with(['unit' => $unit])->create();

        $item = ItemFactory::new()->editable()->number()->create();
        NumberItemUnitValueFactory::new()
            ->withItem($item)
            ->create([
                'unit' => $unit,
                'period' => $period,
            ]);

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/item-unit-hierarchy-values/%s', ItemUnitHierarchyValue::createId($item->_real(), $period->_real(), $unitHierarchy->_real())),
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
