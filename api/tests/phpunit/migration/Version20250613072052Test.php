<?php

declare(strict_types=1);
namespace Tests\Migration\U2;

use U2\Migrations\Version20250602085139;
use U2\Migrations\Version20250613072052;

class Version20250613072052Test extends MigrationTestCase
{
    public function test_migration_removes_only_targeted_rights(): void
    {
        // Given
        $this->runCommand('doctrine:migrations:migrate', ['version' => Version20250602085139::class]);

        $entityManager = self::getEntityManager();
        $connection = $entityManager->getConnection();

        $connection->executeQuery(<<<SQL
                DELETE FROM authorization;

                insert into authorization (id, name, item, rights) values (11111, "test", "test", "[\\"ACCESS\\", \\"READ\\", \\"WRITE\\", \\"TRANSFER\\"]");
                insert into authorization (id, name, item, rights) values (11112, "test", "test", "[\\"READ\\", \\"WRITE\\"]");
                insert into authorization (id, name, item, rights) values (11113, "test", "test", "[\\"TRANSFER\\"]");
                insert into authorization (id, name, item, rights) values (11114, "test", "test", "[\\"ASSIGN\\"]");
                insert into authorization (id, name, item, rights) values (11115, "test", "test", "[\\"READ\\", \\"UPDATE\\"]");
                insert into authorization (id, name, item, rights) values (11116, "test", "test", "[]");
                insert into authorization (id, name, item, rights) values (11117, "test", "test", "[\\"ACCESS\\", \\"WRITE\\", \\"TRANSFER\\"]");
                insert into authorization (id, name, item, rights) values (11118, "test", "test", "[\\"READ\\", \\"ACCESS\\", \\"ASSIGN\\"]");
            SQL
        );

        self::assertSame([
            [
                'id' => 11111,
                'item' => 'test',
                'rights' => '["ACCESS", "READ", "WRITE", "TRANSFER"]',
            ],
            [
                'id' => 11112,
                'item' => 'test',
                'rights' => '["READ", "WRITE"]',
            ],
            [
                'id' => 11113,
                'item' => 'test',
                'rights' => '["TRANSFER"]',
            ],
            [
                'id' => 11114,
                'item' => 'test',
                'rights' => '["ASSIGN"]',
            ],
            [
                'id' => 11115,
                'item' => 'test',
                'rights' => '["READ", "UPDATE"]',
            ],
            [
                'id' => 11116,
                'item' => 'test',
                'rights' => '[]',
            ],
            [
                'id' => 11117,
                'item' => 'test',
                'rights' => '["ACCESS", "WRITE", "TRANSFER"]',
            ],
            [
                'id' => 11118,
                'item' => 'test',
                'rights' => '["READ", "ACCESS", "ASSIGN"]',
            ],
        ], $connection->executeQuery('select id, item, rights from authorization;')->fetchAllAssociative());

        // When
        $this->runCommand('doctrine:migrations:migrate', ['version' => Version20250613072052::class]);

        // Then
        self::assertSame([
            [
                'id' => 11111,
                'item' => 'test',
                'rights' => '["READ"]',
            ],
            [
                'id' => 11112,
                'item' => 'test',
                'rights' => '["READ"]',
            ],
            [
                'id' => 11113,
                'item' => 'test',
                'rights' => '[]',
            ],
            [
                'id' => 11114,
                'item' => 'test',
                'rights' => '["ASSIGN"]',
            ],
            [
                'id' => 11115,
                'item' => 'test',
                'rights' => '["READ", "UPDATE"]',
            ],
            [
                'id' => 11116,
                'item' => 'test',
                'rights' => '[]',
            ],
            [
                'id' => 11117,
                'item' => 'test',
                'rights' => '[]',
            ],
            [
                'id' => 11118,
                'item' => 'test',
                'rights' => '["READ", "ASSIGN"]',
            ],
        ], $connection->executeQuery('select id, item, rights from authorization;')->fetchAllAssociative());
    }
}
