<?php

declare(strict_types=1);
namespace Tests\Migration\U2;

use U2\Migrations\Version20250613072052;
use U2\Migrations\Version20250613072053;

class Version20250613072053Test extends MigrationTestCase
{
    public function test_up(): void
    {
        // Given
        $this->runCommand('doctrine:migrations:migrate', ['version' => Version20250613072052::class]);

        $entityManager = self::getEntityManager();
        $connection = $entityManager->getConnection();

        $connection->executeQuery(<<<SQL
            DELETE FROM authorization;
            DELETE FROM authorization_profile;
            DELETE FROM authorization_profile_authorization;

            INSERT INTO authorization (id, name, item, rights) VALUES
                (1, 'UNIT', 'UNIT', '["READ", "UPDATE"]'),
                (2, 'PERIOD', 'PERIOD', '["READ", "CREATE"]'),
                (3, 'OTHER_AUTH', 'OTHER_ITEM', '["READ", "DELETE"]'),
                (4, 'ANOTHER_AUTH', 'ANOTHER_ITEM', '["UPDATE", "CREATE"]');

            INSERT INTO authorization_profile (id, name) VALUES
                (1, 'Profile with UNIT auth only'),
                (2, 'Profile with PERIOD auth only'),
                (3, 'Profile with UNIT auth and OTHER'),
                (4, 'Profile with OTHER only'),
                (5, 'Profile with UNIT and PERIOD auths');

            INSERT INTO authorization_profile_authorization (authorization_profile_id, authorization_id) VALUES
                (1, 1),  -- Profile 1: UNIT auth only
                (2, 2),  -- Profile 2: PERIOD auth only
                (3, 1),  -- Profile 3: UNIT auth + OTHER
                (3, 3),
                (4, 3),  -- Profile 4: OTHER only
                (5, 1),  -- Profile 5: UNIT auth + PERIOD auth
                (5, 2);
            SQL
        );

        // Then
        self::assertSame([
            [
                'authorization_profile_id' => 1,
                'authorization_id' => 1,
            ],
            [
                'authorization_profile_id' => 2,
                'authorization_id' => 2,
            ],
            [
                'authorization_profile_id' => 3,
                'authorization_id' => 1,
            ],
            [
                'authorization_profile_id' => 3,
                'authorization_id' => 3,
            ],
            [
                'authorization_profile_id' => 4,
                'authorization_id' => 3,
            ],
            [
                'authorization_profile_id' => 5,
                'authorization_id' => 1,
            ],
            [
                'authorization_profile_id' => 5,
                'authorization_id' => 2,
            ],
        ], $connection->executeQuery(
            'SELECT * FROM authorization_profile_authorization ORDER BY authorization_profile_id, authorization_id'
        )->fetchAllAssociative()
        );

        self::assertSame([
            [
                'id' => 1,
                'name' => 'UNIT',
                'item' => 'UNIT',
                'rights' => '["READ", "UPDATE"]',
            ],
            [
                'id' => 2,
                'name' => 'PERIOD',
                'item' => 'PERIOD',
                'rights' => '["READ", "CREATE"]',
            ],
            [
                'id' => 3,
                'name' => 'OTHER_AUTH',
                'item' => 'OTHER_ITEM',
                'rights' => '["READ", "DELETE"]',
            ],
            [
                'id' => 4,
                'name' => 'ANOTHER_AUTH',
                'item' => 'ANOTHER_ITEM',
                'rights' => '["UPDATE", "CREATE"]',
            ],
        ], $connection->executeQuery(
            'SELECT * FROM authorization ORDER BY id'
        )->fetchAllAssociative()
        );

        self::assertSame([
            [
                'id' => 1,
                'name' => 'Profile with UNIT auth only',
            ],
            [
                'id' => 2,
                'name' => 'Profile with PERIOD auth only',
            ],
            [
                'id' => 3,
                'name' => 'Profile with UNIT auth and OTHER',
            ],
            [
                'id' => 4,
                'name' => 'Profile with OTHER only',
            ],
            [
                'id' => 5,
                'name' => 'Profile with UNIT and PERIOD auths',
            ],
        ], $connection->executeQuery(
            'SELECT * FROM authorization_profile ORDER BY id'
        )->fetchAllAssociative()
        );

        // When
        $this->runCommand('doctrine:migrations:migrate', ['version' => Version20250613072053::class]);

        // Then
        self::assertSame([
            [
                'id' => 3,
                'name' => 'OTHER_AUTH',
                'item' => 'OTHER_ITEM',
                'rights' => '["READ", "DELETE"]',
            ],
            [
                'id' => 4,
                'name' => 'ANOTHER_AUTH',
                'item' => 'ANOTHER_ITEM',
                'rights' => '["UPDATE", "CREATE"]',
            ]], $connection->executeQuery(
                'SELECT * FROM authorization ORDER BY id'
            )->fetchAllAssociative()
        );

        self::assertSame([
            [
                'a_id' => 3,
                'auth_name' => 'OTHER_AUTH',
                'auth_item' => 'OTHER_ITEM',
                'auth_rights' => '["READ", "DELETE"]',
                'profile_name' => 'Profile with UNIT auth and OTHER',
                'ap_id' => 3,
            ],

            [
                'a_id' => 3,
                'auth_name' => 'OTHER_AUTH',
                'auth_item' => 'OTHER_ITEM',
                'auth_rights' => '["READ", "DELETE"]',
                'profile_name' => 'Profile with OTHER only',
                'ap_id' => 4,
            ],
        ], $connection->executeQuery(<<<SQL
                SELECT a.id as a_id, a.name as auth_name, a.item as auth_item, a.rights as auth_rights, ap.name as profile_name, ap.id as ap_id
                    FROM authorization_profile_authorization
                JOIN authorization a ON a.id = authorization_id
                JOIN authorization_profile ap ON ap.id = authorization_profile_id
                WHERE authorization_profile_id
                    ORDER BY ap.id, a.id
            SQL
        )->fetchAllAssociative());
    }
}
