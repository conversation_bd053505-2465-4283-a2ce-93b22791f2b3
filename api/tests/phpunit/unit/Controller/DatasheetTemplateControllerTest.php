<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Controller;

use ApiPlatform\Metadata\HttpOperation;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Tests\U2\UnitTestCase;
use U2\Controller\DatasheetTemplateController;
use U2\Datasheets\TemplateStorage;
use U2\Entity\Datasheet;

class DatasheetTemplateControllerTest extends UnitTestCase
{
    private DatasheetTemplateController $layoutTemplateController;

    /**
     * @var MockObject&TemplateStorage
     */
    private MockObject $templateStorage;

    protected function setUp(): void
    {
        $this->templateStorage = $this->createMock(TemplateStorage::class);
        $this->layoutTemplateController = new DatasheetTemplateController($this->templateStorage);
    }

    public function test_deletes_a_layout_template(): void
    {
        $layout = $this->createMock(Datasheet::class);
        $this->templateStorage->expects($this->atLeastOnce())->method(HttpOperation::METHOD_DELETE)->with(self::equalTo($layout))->willReturn(true);
        $this->templateStorage->expects($this->atLeastOnce())->method(HttpOperation::METHOD_DELETE)->with($layout);

        $response = $this->layoutTemplateController->deleteTemplate($layout);
        self::assertSame('{"success":true}', $response->getContent());
    }

    public function test_its_response_informs_if_template_could_not_be_deleted(): void
    {
        $layout = $this->createMock(Datasheet::class);
        $this->templateStorage->expects($this->atLeastOnce())->method(HttpOperation::METHOD_DELETE)->with(self::equalTo($layout))->willReturn(false);
        $this->templateStorage->expects($this->atLeastOnce())->method(HttpOperation::METHOD_DELETE)->with($layout);

        $response = $this->layoutTemplateController->deleteTemplate($layout);
        self::assertSame('{"success":false}', $response->getContent());
    }

    public function test_downloads_a_layout_template(): void
    {
        $layout = $this->createMock(Datasheet::class);
        $layout->expects($this->atLeastOnce())->method('getId')->willReturn(1);
        $layout->expects($this->atLeastOnce())->method('getName')->willReturn('name');
        $this->templateStorage->expects($this->atLeastOnce())->method('getContent')->with(self::equalTo($layout))->willReturn('content');

        $response = $this->layoutTemplateController->downloadTemplate($layout);
        self::assertSame('content', $response->getContent());
        self::assertSame('text/html', $response->headers->get('Content-Type'));
        self::assertStringContainsString('name.html.twig', $response->headers->get('Content-Disposition') ?? 'invalid');
    }

    public function test_gives_a404_response_when_downloading_a_layout_template_that_does_not_exist(): void
    {
        $layout = $this->createMock(Datasheet::class);
        $this->expectException(NotFoundHttpException::class);
        $this->layoutTemplateController->downloadTemplate($layout);
    }
}
