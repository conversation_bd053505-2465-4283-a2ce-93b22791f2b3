<?php

declare(strict_types=1);
namespace Tests\Unit\U2\DataFixtures\Example;

use Faker\Generator;
use Tests\U2\UnitTestCase;
use U2\DataFixtures\Example\LocalFileExampleFactory;
use U2\DataFixtures\Example\PeriodFactory;
use U2\Document\DocumentDefaultPermissionPopulator;
use U2\Document\DocumentSectionPopulator;
use U2\Entity\DocumentTemplate;
use U2\Entity\UnitHierarchy;
use U2\Entity\User;
use U2\Entity\Workflow\Status;

class LocalFileExampleFactoryTest extends UnitTestCase
{
    public function test_create_populates_base_template_if_set(): void
    {
        $creator = new User();

        $documentSectionPopulator = $this->createMock(DocumentSectionPopulator::class);
        $documentDefaultPermissionPopulator = $this->createMock(DocumentDefaultPermissionPopulator::class);
        $factory = new LocalFileExampleFactory(
            $documentSectionPopulator,
            $documentDefaultPermissionPopulator,
            $this->createMock(Generator::class)
        );
        $documentSectionPopulator
            ->expects($this->once())
            ->method('fromBaseTemplate')
            ->with(self::anything());

        $documentDefaultPermissionPopulator
            ->expects($this->once())
            ->method('copyDefaultPermissions')
            ->with(self::anything());

        $documentTemplate = new DocumentTemplate();
        $documentTemplate->setName('Document Template');
        $factory->create(
            [
                'baseTemplate' => $documentTemplate,
                'createdBy' => $creator,
                'assignee' => $creator,
                'updatedBy' => $creator,
                'name' => 'LocalFile Name',
                'period' => PeriodFactory::getObject(),
                'status' => new Status(),
                'unitHierarchy' => new UnitHierarchy(),
            ]
        );
    }
}
