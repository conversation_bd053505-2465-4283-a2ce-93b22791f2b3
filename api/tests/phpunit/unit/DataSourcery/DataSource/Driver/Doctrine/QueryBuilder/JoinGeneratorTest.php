<?php

declare(strict_types=1);
namespace Tests\Unit\U2\DataSourcery\DataSource\Driver\Doctrine\QueryBuilder;

use Tests\U2\UnitTestCase;
use U2\DataSourcery\DataSource\Configuration\Field;
use U2\DataSourcery\DataSource\DataSource;
use U2\DataSourcery\DataSource\Driver\Doctrine\QueryBuilder\JoinFieldNamesFinder;
use U2\DataSourcery\DataSource\Driver\Doctrine\QueryBuilder\JoinGenerator;
use U2\DataSourcery\DataType\NumberDataType;
use U2\DataSourcery\Query\Query;

class JoinGeneratorTest extends UnitTestCase
{
    public function test_generate(): void
    {
        // Given
        $dataSource = $this->createMock(DataSource::class);
        $dataSource
            ->method('getFields')
            ->willReturn(
                [
                    new Field('FIELD_1', 'FIELD', '', new NumberDataType(), 'Field1'),
                    new Field('FIELD_2', 'FIELD', '', new NumberDataType(), 'Relation1.Field2'),
                    new Field('FIELD_3', 'FIELD', '', new NumberDataType(), 'Relation2.Field3'),
                    new Field('FIELD_4', 'FIELD', '', new NumberDataType(), 'Relation1.Relation3.Field4'),
                    new Field('FIELD_5', 'FIELD', '', new NumberDataType(), 'Relation2.Relation4.Relation5.Field5'),
                    new Field('FIELD_6', 'FIELD', '', new NumberDataType(), 'Field6'),
                    new Field('FIELD_7', 'FIELD', '', new NumberDataType(), 'Relation6.Field7'),
                ]
            );

        $requiredFieldsResolver = $this->createMock(JoinFieldNamesFinder::class);

        $requiredFieldsResolver
            ->method('find')
            ->with($query = new Query(), $dataSource)
            ->willReturn(
                [
                    'FIELD_1',
                    'FIELD_2',
                    'FIELD_3',
                    'FIELD_4',
                    'FIELD_5',
                ]
            );

        $generator = new JoinGenerator($requiredFieldsResolver);

        // When
        $joins = $generator->generate(
            'FROM_ALIAS',
            $query,
            $dataSource
        );

        self::assertCount(5, $joins, 'Incorrect number of joins returned by the generator.');

        // List of simple (ENTITY.RELATION) join strings, indexed by the automatically generated alias
        $joinStringsByAlias = [];
        foreach ($joins as $join) {
            $joinStringsByAlias[$join->getAlias()] = $join->getJoin();
        }

        // First-level joins expected
        self::assertContains('FROM_ALIAS.Relation1', $joinStringsByAlias, 'A join to Relation1 is expected');
        self::assertContains('FROM_ALIAS.Relation2', $joinStringsByAlias, 'A join to Relation2 is expected');

        $firstRelationAlias = array_search('FROM_ALIAS.Relation1', $joinStringsByAlias, true);
        $secondRelationAlias = array_search('FROM_ALIAS.Relation2', $joinStringsByAlias, true);

        // Second-level joins expected
        self::assertContains("$firstRelationAlias.Relation3", $joinStringsByAlias, 'A join to Relation3 through Relation1 is expected');
        self::assertContains("$secondRelationAlias.Relation4", $joinStringsByAlias, 'A join to Relation4 through Relation2 is expected');

        $fourthRelationAlias = array_search("$secondRelationAlias.Relation4", $joinStringsByAlias, true);

        // Third-level joins expected
        self::assertContains("$fourthRelationAlias.Relation5", $joinStringsByAlias, 'A join to Relation5 through Relation4 through Relation2 is expected');
    }
}
