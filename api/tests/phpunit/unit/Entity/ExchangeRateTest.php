<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Entity;

use PHPUnit\Framework\Attributes\DataProvider;
use Tests\U2\UnitTestCase;
use U2\Entity\ExchangeRate;
use U2\Entity\Interfaces\Periodable;

class ExchangeRateTest extends UnitTestCase
{
    private ExchangeRate $exchangeRate;

    public function test_it_should_implement_the_periodable_interface(): void
    {
        self::assertTrue($this->exchangeRate instanceof Periodable);
    }

    /**
     * @return array<string, array<string, string>>
     */
    public static function provideIndirectRates(): array
    {
        return [
            'Set indirect rate' => [
                'indirectRate' => '2',
                'expectedDirectRate' => '0.**********',
            ],
            'Set indirect rate to zero' => [
                'indirectRate' => '0',
                'expectedDirectRate' => '0',
            ],
        ];
    }

    #[DataProvider('provideIndirectRates')]
    public function test_set_indirect_rate(string $indirectRate, string $expectedDirectRate): void
    {
        // When
        $this->exchangeRate->setIndirectRate($indirectRate);

        // Then
        self::assertSame($expectedDirectRate, $this->exchangeRate->getDirectRate());
    }

    /**
     * @return array<string, array<string, mixed>>
     */
    public static function provideNonNumericStrings(): array
    {
        return [
            'Set indirect rate to text' => [
                'indirectRate' => 'Test',
            ],
            'Set indirect rate to empty string' => [
                'indirectRate' => '',
            ],
        ];
    }

    #[DataProvider('provideNonNumericStrings')]
    public function test_set_indirect_rate_only_accepts_numeric_values(string $indirectRate): void
    {
        // Then
        $this->expectException(\AssertionError::class);

        // When
        $this->exchangeRate->setIndirectRate($indirectRate);
    }

    protected function setUp(): void
    {
        $this->exchangeRate = new ExchangeRate();
    }
}
