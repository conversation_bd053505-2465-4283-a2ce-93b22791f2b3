<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Entity\Task\TaskType;

use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\EmailValidator;
use Symfony\Component\Validator\ConstraintValidatorFactory;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Tests\U2\UnitTestCase;
use U2\Entity\Contact;

/**
 * @covers \U2\Entity\Contact
 */
class ContactValidationTest extends UnitTestCase
{
    private ValidatorInterface $validator;

    public function test_validate_rfc_not_compliant_email(): void
    {
        // When
        $violations = $this->validator->validatePropertyValue(Contact::class, 'email', '<EMAIL>');

        // Then
        self::assertEquals(1, $violations->count());
    }

    public function test_mobile_can_be_null(): void
    {
        // Given
        $contact = new Contact();

        // Ensure Contact::setMobile() accepts null
        $contact->setMobile(null);

        // When
        $violations = $this->validator->validatePropertyValue($contact, 'mobile', $contact->getMobile());

        // Then
        self::assertCount(0, $violations);
    }

    protected function setUp(): void
    {
        $this->validator = Validation::createValidatorBuilder()
            ->enableAttributeMapping()
            /*
             * TODO: remove `setConstraintValidatorFactory` once we have migrated to Symfony 7.0
             * This is needed to make the EmailValidator use the strict mode.
             * The strict mode is not enabled by default.
             */
            ->setConstraintValidatorFactory(new ConstraintValidatorFactory([EmailValidator::class => new EmailValidator(Email::VALIDATION_MODE_STRICT)]))
            ->getValidator();
    }
}
