<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Entity;

use Tests\U2\UnitTestCase;
use U2\Entity\Interfaces\Periodable;
use U2\Entity\Task\TaskType;
use U2\Entity\Task\TaskType\UnitPeriod;
use U2\Entity\Workflow\Status;

class UnitPeriodTest extends UnitTestCase
{
    private UnitPeriod $unitPeriod;

    public function test_initializable(): void
    {
        self::assertInstanceOf(TaskType::class, $this->unitPeriod);
        self::assertInstanceOf(Periodable::class, $this->unitPeriod);
    }

    protected function setUp(): void
    {
        $this->unitPeriod = new UnitPeriod(new Status());
    }
}
