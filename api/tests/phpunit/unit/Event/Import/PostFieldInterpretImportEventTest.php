<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Event\Import;

use Symfony\Contracts\EventDispatcher\Event;
use Tests\U2\UnitTestCase;
use U2\Event\Import\PostFieldInterpretImportEvent;
use U2\Import\Configuration\Field\FieldConfigurationInterface;
use U2\Import\Configuration\ImportConfig;

class PostFieldInterpretImportEventTest extends UnitTestCase
{
    private PostFieldInterpretImportEvent $postFieldInterpretImportEvent;

    public function test_initializable(): void
    {
        self::assertInstanceOf(PostFieldInterpretImportEvent::class, $this->postFieldInterpretImportEvent);
        self::assertInstanceOf(Event::class, $this->postFieldInterpretImportEvent);
    }

    protected function setUp(): void
    {
        $fieldConfiguration = $this->createMock(FieldConfigurationInterface::class);
        $this->postFieldInterpretImportEvent = new PostFieldInterpretImportEvent(
            $this->createMock(ImportConfig::class),
            $fieldConfiguration,
            1
        );
    }
}
