<?php

declare(strict_types=1);
namespace Tests\Unit\U2\EventListener;

use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;
use Tests\U2\UnitTestCase;
use U2\Entity\Task\TaskType\TaxCredit;
use U2\EventListener\TaxCreditCalculatedFieldsListener;
use U2\TaxCompliance\TaxCredit\TaxCreditUpdater;

#[AsDoctrineListener(event: Events::prePersist)]
#[AsDoctrineListener(event: Events::preUpdate)]
class TaxCreditCalculatedFieldsListenerTest extends UnitTestCase
{
    /**
     * @covers \U2\EventListener\TaxCreditCalculatedFieldsListener::prePersist
     */
    public function test_pre_persist(): void
    {
        $taxCredits = $this->createMock(TaxCredit::class);
        $taxCreditsUpdater = $this->createMock(TaxCreditUpdater::class);
        $taxCreditsUpdater
            ->expects($this->once())
            ->method('update')
            ->with($taxCredits);

        $taxCreditsCalculatedFieldsListener = new TaxCreditCalculatedFieldsListener($taxCreditsUpdater);

        $lifeCycleEventArgs = new PrePersistEventArgs($taxCredits, $this->createMock(EntityManagerInterface::class));
        $taxCreditsCalculatedFieldsListener->prePersist($lifeCycleEventArgs);
    }

    /**
     * @covers \U2\EventListener\TaxCreditCalculatedFieldsListener::preUpdate
     */
    public function test_pre_update(): void
    {
        $taxCredits = $this->createMock(TaxCredit::class);
        $taxCreditsUpdater = $this->createMock(TaxCreditUpdater::class);
        $taxCreditsUpdater
            ->expects($this->once())
            ->method('update')
            ->with($taxCredits);

        $taxCreditsCalculatedFieldsListener = new TaxCreditCalculatedFieldsListener($taxCreditsUpdater);

        $preUpdateEventArgs = $this->createMock(PreUpdateEventArgs::class);
        $preUpdateEventArgs
            ->method('getObject')
            ->willReturn($taxCredits);
        $taxCreditsCalculatedFieldsListener->preUpdate($preUpdateEventArgs);
    }
}
