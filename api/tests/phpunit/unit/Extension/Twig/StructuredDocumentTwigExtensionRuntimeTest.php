<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Extension\Twig;

use Tests\U2\UnitTestCase;
use U2\Entity\DocumentSection;
use U2\Entity\StructuredDocumentInterface;
use U2\Extension\Twig\StructuredDocumentTwigExtensionRuntime;
use U2\Widget\Document\InlineRenderer;

class StructuredDocumentTwigExtensionRuntimeTest extends UnitTestCase
{
    public function test_renders_widgets_inline(): void
    {
        $content = 'Test rendering <widget>{"name":"widget-number-one"}</widget> and [[widget-number-two]] inline';
        $renderedContent = 'Test rendering widgetOne and [[widget-number-two]] inline';
        $widgetInlineRenderer = $this->createMock(InlineRenderer::class);
        $widgetInlineRenderer->expects($this->once())->method('render')->with($content)->willReturn($renderedContent);
        $section = $this->createMock(DocumentSection::class);
        $purifier = $this->createMock(\HTMLPurifier::class);
        $purifier->expects($this->once())->method('purify')->with($content)->willReturn($content);
        $section->expects($this->once())->method('getDocument')->willReturn($this->createMock(StructuredDocumentInterface::class));
        $section->expects($this->once())->method('getContent')->willReturn($content);

        $structuredDocumentTwigExtension = new StructuredDocumentTwigExtensionRuntime($widgetInlineRenderer, $purifier);

        self::assertSame($renderedContent, $structuredDocumentTwigExtension->renderDocumentSectionContent($section));
    }
}
