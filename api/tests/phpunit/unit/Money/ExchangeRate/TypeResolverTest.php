<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Money\ExchangeRate;

use Tests\U2\UnitTestCase;
use U2\Datasheets\Item\ExchangeMethods;
use U2\Money\ExchangeRate\ExchangeRateTypes;
use U2\Money\ExchangeRate\TypeResolver;

class TypeResolverTest extends UnitTestCase
{
    public function test_resolves_a_method_into_a_rate_type(): void
    {
        self::assertSame(ExchangeRateTypes::CURRENT, TypeResolver::resolve(ExchangeMethods::CURRENT));
        self::assertSame(ExchangeRateTypes::AVERAGE, TypeResolver::resolve(ExchangeMethods::AVERAGE));
        self::assertSame(ExchangeRateTypes::CURRENT, TypeResolver::resolve(ExchangeMethods::PREVIOUS_CURRENT));
        self::assertSame(ExchangeRateTypes::AVERAGE, TypeResolver::resolve(ExchangeMethods::PREVIOUS_AVERAGE));
    }

    public function test_throws_an_exception_for_unknown_method(): void
    {
        $this->expectException(\Exception::class);
        TypeResolver::resolve(0);
    }
}
