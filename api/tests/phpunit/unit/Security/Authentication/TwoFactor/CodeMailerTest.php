<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Security\Authentication\TwoFactor;

use Symfony\Contracts\Translation\TranslatorInterface;
use Tests\U2\TestUtils;
use Tests\U2\UnitTestCase;
use Twig\Environment;
use U2\Email\EmailHelper;
use U2\Entity\Contact;
use U2\Entity\User;
use U2\Entity\UserSetting;
use U2\Security\Authentication\TwoFactor\CodeMailer;
use U2\User\Settings\UserSettingTypes;

class CodeMailerTest extends UnitTestCase
{
    public function test_send_auth_code(): void
    {
        // Given
        $translator = $this->createMock(TranslatorInterface::class);
        $translator->method('trans')->willReturnCallback([TestUtils::class, 'mockTranslation']);

        $contact = new Contact();
        $contact->setEmail('<EMAIL>');
        $user = new User();
        $user->setEmailAuthCode('123456');
        $user->setContact($contact);
        $user->getSettings()->set(UserSettingTypes::LOCALE, new UserSetting(UserSettingTypes::LOCALE, 'DE', $user));

        $templateEngine = $this->createMock(Environment::class);
        $templateEngine
            ->method('render')
            ->with(
                'email/two_factor.email.html.twig',
                [
                    'authentication_code' => '123456',
                    'user' => $user,
                    'locale' => 'DE',
                ]
            )
            ->willReturn('email content');

        // Then
        $emailHelper = $this->createMock(EmailHelper::class);
        $emailHelper
            ->expects($this->once())
            ->method('sendEmail')
            ->with(
                '<EMAIL>',
                '<EMAIL>',
                'email content',
                'Translation: "u2.two_factor.email.subject" with {"%authentication_code%":"123456"}. Domain: "null". Locale: "DE"'
            );

        // When
        $codeMailer = new CodeMailer(
            $emailHelper,
            $translator,
            $templateEngine,
            'de',
            '<EMAIL>'
        );
        $codeMailer->sendAuthCode($user);
    }

    public function test_email_uses_default_locale_if_users_has_none_defined(): void
    {
        // Given
        $emailHelper = $this->createMock(EmailHelper::class);
        $templateEngine = $this->createMock(Environment::class);

        $contact = new Contact();
        $contact->setEmail('<EMAIL>');
        $user = new User();
        $user->setEmailAuthCode('123456');
        $user->setContact($contact);

        $templateEngine
            ->method('render')
            ->with(
                'email/two_factor.email.html.twig',
                [
                    'authentication_code' => '123456',
                    'user' => $user,
                    'locale' => 'de',
                ]
            )
            ->willReturn('email content');

        $translator = $this->createMock(TranslatorInterface::class);
        $translator->method('trans')->willReturnCallback([TestUtils::class, 'mockTranslation']);

        // Then
        $emailHelper
            ->expects($this->once())
            ->method('sendEmail')
            ->with(
                '<EMAIL>',
                '<EMAIL>',
                'email content',
                'Translation: "u2.two_factor.email.subject" with {"%authentication_code%":"123456"}. Domain: "null". Locale: "de"'
            );

        // When
        $codeMailer = new CodeMailer(
            $emailHelper,
            $translator,
            $templateEngine,
            'de',
            '<EMAIL>'
        );
        $codeMailer->sendAuthCode($user);
    }
}
