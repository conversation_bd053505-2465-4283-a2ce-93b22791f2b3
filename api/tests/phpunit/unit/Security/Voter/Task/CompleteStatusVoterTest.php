<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Security\Voter\Task;

use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Core\Authorization\Voter\VoterInterface;
use Tests\U2\UnitTestCase;
use Tests\Unit\U2\Security\Voter\VoterTestIssue;
use U2\Entity\User;
use U2\Security\Voter\DocumentVoterAttributes;
use U2\Security\Voter\Task\CompleteStatusVoter;
use U2\Security\Voter\VoterAttributes;

class CompleteStatusVoterTest extends UnitTestCase
{
    public function test_voter_denies_delete_access(): void
    {
        // Given
        $token = new UsernamePasswordToken(new User(), 'TEST_PROVIDER');
        $workflowableEntity = $this->createMock(VoterTestIssue::class);
        $workflowableEntity->method('isComplete')->willReturn(true);
        $workflowableEntity->method('getId')->willReturn(1);

        // When
        $voter = new CompleteStatusVoter();
        $actualVoterDecision = $voter->vote($token, $workflowableEntity, [VoterAttributes::delete]);

        // Then
        self::assertSame(VoterInterface::ACCESS_DENIED, $actualVoterDecision, 'The voter returned the wrong decision for a issue where the status is complete');
    }

    public function test_voter_denies_remove_review_access_when_wfc_is_enabled(): void
    {
        // Given
        $token = new UsernamePasswordToken(new User(), 'TEST_PROVIDER');
        $workflowableEntity = $this->createMock(VoterTestIssue::class);
        $workflowableEntity->method('isComplete')->willReturn(true);
        $workflowableEntity->method('getId')->willReturn(1);

        // When
        $voter = new CompleteStatusVoter();
        $actualVoterDecision = $voter->vote($token, $workflowableEntity, [VoterAttributes::removeReview]);

        // Then
        self::assertSame(VoterInterface::ACCESS_DENIED, $actualVoterDecision, 'The voter returned the wrong decision for a issue where the status is complete');
    }

    #[DataProvider('provideSupportedAttributes')]
    public function test_voter_grants_access_when_the_issue_status_is_not_complete(string $attribute): void
    {
        $token = new UsernamePasswordToken(new User(), 'TEST_PROVIDER');
        $workflowableEntity = $this->createMock(VoterTestIssue::class);
        $workflowableEntity->method('isComplete')->willReturn(false);

        $voter = new CompleteStatusVoter();
        $workflowableEntity->method('getId')->willReturn(1);

        $actualVoterDecision = $voter->vote($token, $workflowableEntity, [$attribute]);
        self::assertSame(VoterInterface::ACCESS_GRANTED, $actualVoterDecision, 'The voter returned the wrong decision for a issue where the status is not complete');
    }

    #[DataProvider('provideSupportedAttributes')]
    public function test_voter_grants_access_when_the_issue_is_new(string $attribute): void
    {
        $token = new UsernamePasswordToken(new User(), 'TEST_PROVIDER');
        $workflowableEntity = $this->createMock(VoterTestIssue::class);
        $workflowableEntity->method('getId')->willReturn(null);

        $voter = new CompleteStatusVoter();
        $actualVoterDecision = $voter->vote($token, $workflowableEntity, [$attribute]);

        self::assertSame(VoterInterface::ACCESS_GRANTED, $actualVoterDecision, 'The voter returned the wrong decision for a new issue');
    }

    #[DataProvider('provideSupportedAttributes')]
    public function test_voter_denies_access_when_the_issue_status_is_complete(string $attribute): void
    {
        $token = new UsernamePasswordToken(new User(), 'TEST_PROVIDER');
        $workflowableEntity = $this->createMock(VoterTestIssue::class);
        $workflowableEntity->method('isComplete')->willReturn(true);
        $workflowableEntity->method('getId')->willReturn(1);

        $voter = new CompleteStatusVoter();
        $actualVoterDecision = $voter->vote($token, $workflowableEntity, [$attribute]);

        self::assertSame(VoterInterface::ACCESS_DENIED, $actualVoterDecision, 'The voter returned the wrong decision for a issue where the status is complete');
    }

    public function test_suports_types(): void
    {
        $voter = new CompleteStatusVoter();
        self::assertTrue($voter->supportsType(VoterTestIssue::class));
        self::assertFalse($voter->supportsType(\stdClass::class));
    }

    public function test_voter_abstains_when_attribute_is_not_supported(): void
    {
        $voter = new CompleteStatusVoter();
        self::assertFalse($voter->supportsAttribute('UNSUPPORTED_ATTRIBUTE'));
    }

    /**
     * @return array<string,array<int,string>>
     */
    public static function provideSupportedAttributes(): array
    {
        return [
            'delete_record' => [VoterAttributes::delete],
            'remove_review' => [VoterAttributes::removeReview],
            'edit_content' => [DocumentVoterAttributes::editContent],
            'delete_document' => [DocumentVoterAttributes::delete],
        ];
    }
}
