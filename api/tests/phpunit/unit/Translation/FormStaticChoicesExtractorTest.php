<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Translation;

use Symfony\Component\Translation\MessageCatalogue;
use Tests\U2\UnitTestCase;
use U2\Translation\FormStaticChoicesExtractor;

class FormStaticChoicesExtractorTest extends UnitTestCase
{
    public function test_static_choices_extraction(): void
    {
        $prefix = 'A test prefix ';
        $formStaticChoicesExtractor = new FormStaticChoicesExtractor();
        $formStaticChoicesExtractor->setPrefix($prefix);
        $catalogue = new MessageCatalogue('de');
        $className = substr(self::class, strrpos(self::class, '\\') + 1);
        $formStaticChoicesExtractor->extract(__DIR__ . '/Fixtures/' . $className, $catalogue);

        $expectedCatalogue = [
            'messages' => [
                'Test First Choice' => $prefix . 'Test First Choice',
                'Test Second Choice' => $prefix . 'Test Second Choice',
                'Test Third Choice' => $prefix . 'Test Third Choice',
                'Test Fourth Choice' => $prefix . 'Test Fourth Choice',
                'Test Fifth Choice' => $prefix . 'Test Fifth Choice',
            ],
        ];
        $actualCatalogue = $catalogue->all();

        self::assertEquals($expectedCatalogue, $actualCatalogue);
    }
}
