<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Widget\Dashboard;

use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Tests\U2\UnitTestCase;
use U2\Entity\SavedFilter;
use U2\Exception\InsufficientPermissionsWidgetException;
use U2\Exception\InvalidArgumentValueWidgetException;
use U2\Exception\MissingArgumentWidgetException;
use U2\Repository\SavedFilterRepository;
use U2\Security\Voter\VoterAttributes;
use U2\Table\SavedFilter\RoutingHelper;
use U2\Table\Table;
use U2\Widget\Dashboard\FilterResults;
use U2\Widget\Dashboard\FilterResultsTableFactory;

class FilterResultsTest extends UnitTestCase
{
    public function test_get_data_fails_for_filter_id_referencing_a_non_existing_filter(): void
    {
        // Given
        $savedFilterRepository = $this->createMock(SavedFilterRepository::class);
        $savedFilterRepository->method('find')->with(1)->willReturn(null);
        $widget = new FilterResults(
            $this->createMock(RoutingHelper::class),
            $savedFilterRepository,
            $this->createMock(AuthorizationCheckerInterface::class),
            $this->createMock(FilterResultsTableFactory::class)
        );
        $widget->setParameters(['filter_id' => 1]);

        // Then
        $this->expectException(InvalidArgumentValueWidgetException::class);

        // When
        $widget->getData();
    }

    public function test_get_data_fails_for_missing_filter_id(): void
    {
        // Given
        $widget = new FilterResults(
            $this->createMock(RoutingHelper::class),
            $this->createMock(SavedFilterRepository::class),
            $this->createMock(AuthorizationCheckerInterface::class),
            $this->createMock(FilterResultsTableFactory::class)
        );
        // Then
        $this->expectException(MissingArgumentWidgetException::class);

        // When
        $widget->getData();
    }

    public function test_get_data_fails_if_user_has_insufficient_permissions(): void
    {
        // Given
        $table = $this->createMock(Table::class);
        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authorizationChecker->method('isGranted')->with(VoterAttributes::read, $table)->willReturn(false);
        $savedFilterRepository = $this->createMock(SavedFilterRepository::class);
        $savedFilterRepository->method('find')->with(1)->willReturn($savedFilter = new SavedFilter());
        $tableFactory = $this->createMock(FilterResultsTableFactory::class);
        $widget = new FilterResults(
            $this->createMock(RoutingHelper::class),
            $savedFilterRepository,
            $authorizationChecker,
            $tableFactory
        );
        $widget->setParameters(['filter_id' => 1]);
        $tableFactory->method('create')->with($widget, $savedFilter)->willReturn($table);

        // Then
        $this->expectException(InsufficientPermissionsWidgetException::class);

        // When
        $widget->getData();
    }

    public function test_it_cuts_the_amount_of_shown_columns_down_to_the_maximum(): void
    {
        // Given
        $widget = new FilterResults(
            $this->createMock(RoutingHelper::class),
            $this->createMock(SavedFilterRepository::class),
            $this->createMock(AuthorizationCheckerInterface::class),
            $this->createMock(FilterResultsTableFactory::class)
        );
        $widget->setParameters(['columns' => ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']]);

        // When
        $columns = $widget->getColumns();

        // Then
        self::assertSame(['Id', 'A', 'B', 'C', 'D', 'E'], $columns);
    }

    public function test_id_is_set_by_default(): void
    {
        // Given
        $widget = new FilterResults(
            $this->createMock(RoutingHelper::class),
            $this->createMock(SavedFilterRepository::class),
            $this->createMock(AuthorizationCheckerInterface::class),
            $this->createMock(FilterResultsTableFactory::class)
        );
        $widget->setParameters(['columns' => []]);

        // When
        $columns = $widget->getColumns();

        // Then
        self::assertSame(['Id'], $columns);
    }

    public function test_id_is_always_the_first_column(): void
    {
        // Given
        $widget = new FilterResults(
            $this->createMock(RoutingHelper::class),
            $this->createMock(SavedFilterRepository::class),
            $this->createMock(AuthorizationCheckerInterface::class),
            $this->createMock(FilterResultsTableFactory::class)
        );
        $widget->setParameters(['columns' => ['A', 'B', 'C', 'D', 'E', 'F', 'Id']]);

        // When
        $columns = $widget->getColumns();

        // Then
        self::assertSame('Id', reset($columns));
    }

    public function test_records_per_page_limit_cannot_be_bigger_than_ten(): void
    {
        // Given
        $widget = new FilterResults(
            $this->createMock(RoutingHelper::class),
            $this->createMock(SavedFilterRepository::class),
            $this->createMock(AuthorizationCheckerInterface::class),
            $this->createMock(FilterResultsTableFactory::class)
        );
        $widget->setParameters(['records_per_page' => 11]);

        // When
        $recordsPerPageLimit = $widget->getRecordsPerPageLimit();

        // Then
        self::assertSame(10, $recordsPerPageLimit);
    }

    public function test_records_per_page_limit_cannot_be_less_than_five(): void
    {
        // Given
        $widget = new FilterResults(
            $this->createMock(RoutingHelper::class),
            $this->createMock(SavedFilterRepository::class),
            $this->createMock(AuthorizationCheckerInterface::class),
            $this->createMock(FilterResultsTableFactory::class)
        );
        $widget->setParameters(['records_per_page' => 4]);

        // When
        $recordsPerPageLimit = $widget->getRecordsPerPageLimit();

        // Then
        self::assertSame(5, $recordsPerPageLimit);
    }
}
