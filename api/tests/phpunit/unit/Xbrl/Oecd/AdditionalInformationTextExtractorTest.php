<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Xbrl\Oecd;

use Doctrine\Common\Collections\ArrayCollection;
use Tests\U2\UnitTestCase;
use U2\Entity\CountryByCountryReportSection;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Xbrl\Oecd\AdditionalInformationTextExtractor;

class AdditionalInformationTextExtractorTest extends UnitTestCase
{
    public function test_extracts_additional_information_sections_content(): void
    {
        $section1 = $this->createMock(CountryByCountryReportSection::class);
        $section1->method('getName')->willReturn('Additional Information');
        $section1->method('getContent')->willReturn('<b>Test</b>');
        $section2 = $this->createMock(CountryByCountryReportSection::class);
        $section2->method('getName')->willReturn('Extra Information');
        $section2->expects($this->never())->method('getContent');
        $section3 = $this->createMock(CountryByCountryReportSection::class);
        $section3->method('getName')->willReturn('  additional  information  ');
        $section3->method('getContent')->willReturn('&lt;html&gt;Test &amp;&lt;/html&gt;');
        $section4 = $this->createMock(CountryByCountryReportSection::class);
        $section4->method('getName')->willReturn('Information');
        $section4->expects($this->never())->method('getContent');
        $section5 = $this->createMock(CountryByCountryReportSection::class);
        $section5->method('getName')->willReturn('Additional Info');
        $section5->method('getContent')->willReturn('&quot;Quotes&quot;');
        $section6 = $this->createMock(CountryByCountryReportSection::class);
        $section6->method('getName')->willReturn('An Additional Information Section');
        $section6->expects($this->never())->method('getContent');
        $countryByCountryReport = $this->createMock(CountryByCountryReport::class);
        $countryByCountryReport->method('getSections')->willReturn(new ArrayCollection([
            $section1,
            $section2,
            $section3,
            $section4,
            $section5,
            $section6,
        ]));

        $texts = AdditionalInformationTextExtractor::extract($countryByCountryReport);

        self::assertCount(3, $texts);
        self::assertSame($texts[0], 'TEST');
        self::assertSame($texts[1], '<html>Test &</html>');
        self::assertSame($texts[2], '"Quotes"');
    }

    public function test_splits_long_sections_into_smaller_chunks(): void
    {
        $expectedChunk1 = str_repeat('x', 4000);
        $expectedChunk2 = str_repeat('y', 4000);
        $expectedChunk3 = 'z';
        $section = $this->createMock(CountryByCountryReportSection::class);
        $section->method('getName')->willReturn('Additional Information');
        $section->method('getContent')->willReturn($expectedChunk1 . $expectedChunk2 . $expectedChunk3);
        $countryByCountryReport = $this->createMock(CountryByCountryReport::class);
        $countryByCountryReport->method('getSections')->willReturn(new ArrayCollection([$section]));

        $texts = AdditionalInformationTextExtractor::extract($countryByCountryReport);

        self::assertCount(3, $texts);
        self::assertSame($texts[0], $expectedChunk1);
        self::assertSame($texts[1], $expectedChunk2);
        self::assertSame($texts[2], $expectedChunk3);
    }

    public function test_extracts_multiline(): void
    {
        $countryByCountryReport = $this->createMock(CountryByCountryReport::class);
        $section = $this->createMock(CountryByCountryReportSection::class);
        $section->method('getName')->willReturn('Additional Information');
        $section->method('getContent')->willReturn(<<<HTML
            <p>PLEASE ENTER ADDITIONAL INFOS HERE!</p>\r\n
            <p>&nbsp;</p>\r\n
            <p>Another line (seperated by ENTER/RETURN)</p>
            HTML
        );
        $countryByCountryReport->method('getSections')->willReturn(new ArrayCollection([$section]));

        $texts = AdditionalInformationTextExtractor::extract($countryByCountryReport);

        self::assertCount(1, $texts);
        self::assertSame(
            $texts,
            [
                <<<EOF
                    PLEASE ENTER ADDITIONAL INFOS HERE!\n
                    \u{a0}\n
                    Another line (seperated by ENTER/RETURN)\n
                    EOF
                ,
            ]
        );
    }
}
