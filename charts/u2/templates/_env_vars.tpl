{{- define "u2.env_vars" -}}
env:
- name: APP_SECRET
  valueFrom:
    secretKeyRef:
      name: app-secret
      key: secret
- name: ENVIRONMENT
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: environment
- name: APP_HOSTNAME
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: tenant_base_hostname
- name: AWS_REGION
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: s3_bucket.region
{{- if .Values.appData.endpoint }}
- name: AWS_S3_ENDPOINT
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: s3_bucket.endpoint
{{- end }}
- name: AWS_S3_USE_PATH_STYLE_ENDPOINT
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: s3_bucket.use_path_style_endpoint
- name: AWS_S3_BUCKET
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: s3_bucket.name
- name: AWS_S3_BUCKET_PREFIX
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: s3_bucket.prefix
{{- if .Values.appData.key }}
- name: AWS_S3_KEY
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: s3_bucket.key
{{- end }}
{{- if .Values.appData.secret }}
- name: AWS_S3_SECRET
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: s3_bucket.secret
{{- end }}
- name: DATABASE_URL
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: database.url
- name: DATABASE_VERSION
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: database.version
- name: REDIS_URL
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: redis.url
- name: TENANTS_CONFIG
  valueFrom:
    secretKeyRef:
      name: {{ .Values.multiTenancyConfigSecret.name }}
      key: {{ .Values.multiTenancyConfigSecret.tenantsMapDataKey }}
- name: MAILER_URL
  valueFrom:
    secretKeyRef:
      name: mailer-config
      key: url
- name: MAILER_FROM
  valueFrom:
    secretKeyRef:
      name: mailer-config
      key: from
- name: JWT_SECRET_KEY
  valueFrom:
    secretKeyRef:
      name: jwt-config
      key: secret-key
- name: JWT_PUBLIC_KEY
  valueFrom:
    secretKeyRef:
      name: jwt-config
      key: public-key
- name: JWT_PASSPHRASE
  valueFrom:
    secretKeyRef:
      name: jwt-config
      key: passphrase
{{- end -}}
