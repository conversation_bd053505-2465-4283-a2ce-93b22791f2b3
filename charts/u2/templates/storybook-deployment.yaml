{{ if .Values.storybook.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: storybook
  labels:
    app.kubernetes.io/name: {{ include "u2.name" . }}-storybook
    helm.sh/chart: {{ include "u2.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  replicas: {{ .Values.storybook.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "u2.name" . }}-storybook
      app.kubernetes.io/instance: {{ .Release.Name }}
  strategy:
    rollingUpdate:
      # The value of 100% might not be optimal if the default number of replicas is high. See UU-5025
      maxSurge: 100%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "u2.name" . }}-storybook
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      containers:
      - name: storybook
        image: "{{ .Values.storybook.image.repository }}:{{ .Values.storybook.image.tag | default .Values.defaultU2ImageTag }}"
        imagePullPolicy: {{ .Values.storybook.image.pullPolicy }}
        ports:
        - name: web
          containerPort: 8080
          protocol: TCP
        resources:
          {{- toYaml .Values.storybook.resources | nindent 12 }}
        # Use --openssl-legacy-provider to solve a bug with ssl. see:
        # https://github.com/storybookjs/storybook/issues/20482
        # https://github.com/storybookjs/builder-vite/issues/414
        command: [ 'sh', '-c', 'storybook build -c .storybook -o .out && npx http-server -g -p 8080 .out' ]
      {{- if .Values.imageCredentials }}
      imagePullSecrets:
        - name: u2-docker-registry-credentials
      {{- end }}
{{ end }}
