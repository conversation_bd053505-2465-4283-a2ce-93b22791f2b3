<script setup lang="ts">
import { computed, ref, toRefs } from 'vue'
import AppDialog from '@js/components/AppDialog.vue'
import AssignForm from '@js/components/form/AssignMultiselect.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import Translator from '@js/translator'
import useLayoutsAllQuery from '@js/composable/useLayoutsAllQuery'
import type { ApiResourceId } from '@js/types'

const props = defineProps<{
  layouts: Array<ApiResourceId>
}>()

const { layouts } = toRefs(props)
const selectedLayouts = ref(layouts.value)

const emit = defineEmits<{
  (event: 'close'): void
  (event: 'layoutsSelected', payload: Array<ApiResourceId>): void
}>()

const { items, isLoading: areLayoutsLoading } = useLayoutsAllQuery()
const allLayoutOptions = computed(() =>
  items.value.map((layout) => ({
    id: layout['@id'],
    name: layout.name + ' (' + layout.group + ')',
  }))
)

function selectLayouts() {
  emit('layoutsSelected', selectedLayouts.value)
}
</script>

<template>
  <AppDialog
    :title="Translator.trans('u2.datasheets.datasheet_collection.select_datasheets')"
    :loading="areLayoutsLoading"
    @close="emit('close')"
  >
    <AssignForm v-model="selectedLayouts" :options="allLayoutOptions" />
    <template #buttons>
      <ButtonBasic :disabled="areLayoutsLoading" @click="emit('close')">
        {{ Translator.trans('u2.cancel') }}
      </ButtonBasic>

      <ButtonBasic :disabled="areLayoutsLoading" button-style="solid" @click="selectLayouts">
        {{ Translator.trans('u2.select') }}
      </ButtonBasic>
    </template>
  </AppDialog>
</template>
