<script setup lang="ts">
import { updateDocumentSection } from '@js/api/documentApi'
import FormErrors from '@js/components/form/FormErrors.vue'
import useForm from '@js/composable/useForm'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import { toTypedSchema } from '@vee-validate/zod'
import { isAxiosError } from 'axios'
import { StatusCodes } from 'http-status-codes'
import invariant from 'tiny-invariant'
import { computed, nextTick, onUnmounted, ref, toRefs, useTemplateRef, watch } from 'vue'
import DocumentSectionAddButton from '@js/components/document/DocumentSectionAddButton.vue'
import { newSectionTitleIdentifier } from '@js/model/document'
import BaseDocumentSection from '@js/components/document/BaseDocumentSection.vue'
import DocumentSectionExpander from '@js/components/document/DocumentSectionExpander.vue'
import DocumentSections from '@js/components/document/DocumentSections.vue'
import DocumentSectionTextarea from '@js/components/document/DocumentSectionTextarea.vue'
import DocumentSectionTitle from '@js/components/document/DocumentSectionTitle.vue'
import SectionMenu from '@js/components/document/SectionMenu.vue'
import TwigComponent from '@js/components/form/TwigComponent.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import useSavePrompt from '@js/composable/useSavePrompt'
import { useDetectReadyToEdit } from '@js/helper/document/useDetectReadyToEdit'
import { useDocumentStore } from '@js/stores/document'
import Translator from '@js/translator'
import type { HierarchicalSection } from '@js/helper/document/transformSectionsToHierarchy'
import { z } from 'zod'

const props = defineProps<{
  hierarchicalSection: HierarchicalSection
  userCanEditConfiguration: boolean
  userCanEditContent: boolean
  isParentExcluded?: boolean
}>()

const { hierarchicalSection, userCanEditContent, userCanEditConfiguration } = toRefs(props)

const emit = defineEmits<(event: 'saved') => void>()

const documentStore = useDocumentStore()

const isVisible = computed(
  () => !documentStore.isSectionCollapsed(props.hierarchicalSection.section)
)
const isExcluded = computed(
  () => !props.hierarchicalSection.section.include || props.isParentExcluded
)

// Section editing
const isNewSection = computed(
  () => hierarchicalSection.value.section.name === newSectionTitleIdentifier
)
const titlePlaceholder = computed(() =>
  isNewSection.value
    ? `*** ${Translator.trans('u2_structureddocument.new_section')} ***`
    : Translator.trans('u2.title')
)
const contentEditor = useTemplateRef('contentEditor')
const isDirty = ref(contentEditor.value?.isDirty)
watch(
  () => contentEditor.value?.isDirty,
  (newValue) => {
    console.log('Content editor dirty state changed:', newValue)
    setFieldValue('isDirty', newValue)
  }
)
const { handleSubmit, setResponseErrors, values, unmappedErrors, resetForm, setFieldValue } =
  useForm({
    validationSchema: toTypedSchema(
      z.object({
        name: z.string().min(1),
        content: z.string().optional().nullable(),
        isDirty: z.boolean().optional(),
      })
    ),
    initialValues: {
      name: isNewSection.value ? '' : hierarchicalSection.value.section.name,
      content: hierarchicalSection.value.section.content,
      isDirty: isDirty.value ?? false,
    },
    keepValuesOnUnmount: true,
  })
watch(
  () => hierarchicalSection.value.section,
  (newSectionData) => {
    if (documentStore.isSectionEdited(newSectionData)) {
      return
    }

    // Reset initial and current values of the form when section is updated
    resetForm({
      values: {
        name: isNewSection.value ? '' : newSectionData.name,
        content: newSectionData.content,
      },
    })
    savePrompt.set()
  }
)

const isInEditMode = computed(() => {
  return documentStore.isSectionEdited(hierarchicalSection.value.section)
})
const canUserAddSection = computed(() => {
  return userCanEditContent.value || userCanEditConfiguration.value
})
const canUserEdit = computed(() => {
  return hierarchicalSection.value.section.editable && canUserAddSection.value
})

const titleInput = useTemplateRef('titleInput')
const isReadyToEdit = useDetectReadyToEdit()
function enterEditMode(target: 'title' | 'content' = 'title') {
  if (isInEditMode.value) {
    return
  }

  if (!userCanEditContent.value || !hierarchicalSection.value.section.editable) {
    return
  }

  if (!isReadyToEdit()) {
    return
  }

  savePrompt.set()

  if (documentStore.isSectionCollapsed(hierarchicalSection.value.section)) {
    documentStore.expandSection(hierarchicalSection.value.section)
  }

  documentStore.editedSections.add(hierarchicalSection.value.section.id)

  if (target === 'content') {
    nextTick(() => {
      contentEditor.value?.focusEditor()
    })
    return
  }

  nextTick(() => titleInput.value?.focus())
}

const savePrompt = useSavePrompt(ref(values))

const isSaving = ref(false)
const { resolveNotification } = useHandleAxiosErrorResponse()
const save = handleSubmit(async () => {
  isSaving.value = true
  try {
    const response = await updateDocumentSection({
      id: hierarchicalSection.value.section.id,
      type: hierarchicalSection.value.section['@type'],
      name: values.name,
      content: values.content,
    })
    if (response.status === StatusCodes.OK) {
      emit('saved')
      documentStore.editedSections.delete(hierarchicalSection.value.section.id)
      savePrompt.set()
    }
  } catch (error) {
    await resolveNotification(error)
    invariant(isAxiosError(error) && error.response)
    setResponseErrors(error.response)
  }
  isSaving.value = false
})

function cancelEdit() {
  if (!savePrompt.changed.value || savePrompt.confirm()) {
    documentStore.editedSections.delete(hierarchicalSection.value.section.id)
    resetForm()
  }
}

onUnmounted(() => {
  documentStore.editedSections.delete(hierarchicalSection.value.section.id)
})
</script>

<template>
  <div>
    <div class="relative">
      <form :name="`document_section_${hierarchicalSection.section.id}`">
        <FormErrors :errors="unmappedErrors" />

        <BaseDocumentSection
          :id="'section-' + hierarchicalSection.section.id"
          :excluded="isExcluded"
          :level="hierarchicalSection.section.level > 6 ? 6 : hierarchicalSection.section.level"
          :class="{
            'hover:cursor-not-allowed': !isInEditMode && !canUserEdit,
            'hover:cursor-pointer': !isInEditMode && canUserEdit,
            'document-section-view-mode-padding': !isInEditMode,
          }"
        >
          <template #before>
            <DocumentSectionExpander :hierarchical-section />
          </template>

          <template #title>
            <button
              type="button"
              class="peer block appearance-none outline-hidden"
              :aria-label="`${Translator.trans('u2.edit')}: ${hierarchicalSection.section.name}`"
              :tabindex="documentStore.isSectionEdited(hierarchicalSection.section) ? '-1' : '0'"
              @click="enterEditMode('title')"
            />

            <DocumentSectionTitle
              ref="titleInput"
              name="name"
              class="before:absolute before:inset-0 before:left-[-10px] before:h-full before:w-1 before:cursor-auto before:rounded-t before:content-[''] peer-focus:before:rounded-b peer-focus:before:bg-linear-to-t peer-focus:before:from-blue-600 peer-focus:before:to-blue-500"
              :class="{
                'before:rounded-b': !isVisible,
                'before:bg-linear-to-t before:from-blue-200 before:to-blue-300 has-[[contenteditable=true]:focus]:before:from-blue-600 has-[[contenteditable=true]:focus]:before:to-blue-500':
                  isInEditMode,
              }"
              required
              :editable="isInEditMode"
              :placeholder="titlePlaceholder"
              :disabled="isInEditMode ? !userCanEditConfiguration : false"
              :aria-label="Translator.trans('u2_structureddocument.title')"
              :readonly="isSaving"
              @cancel="cancelEdit"
              @click="enterEditMode"
              @focusin="
                () => {
                  if (!isVisible && isInEditMode) {
                    documentStore.expandSection(hierarchicalSection.section)
                  }
                }
              "
            >
              <template #numbering>
                {{ hierarchicalSection.tocId }}
              </template>
              <template #icon>
                <SvgIcon
                  v-if="!hierarchicalSection.section.editable"
                  v-tooltip="Translator.trans('u2.not_editable')"
                  icon="lock-closed"
                  :class="['shrink-0 print:hidden', isExcluded ? 'text-gray-200' : 'text-gray-400']"
                />
              </template>
            </DocumentSectionTitle>
          </template>

          <template #controls>
            <SectionMenu
              v-if="hierarchicalSection.section.document"
              :section="hierarchicalSection.section"
              :user-can-edit-document-configuration="userCanEditConfiguration"
              :user-can-edit-document-content="userCanEditContent"
              @saved="emit('saved')"
              @edit="enterEditMode"
            />
          </template>

          <template #default>
            <div v-show="isVisible" class="relative -mt-1 pt-1">
              {{ contentEditor?.isDirty }}
              {{ values }}
              <DocumentSectionTextarea
                v-if="isInEditMode"
                ref="contentEditor"
                name="content"
                :section="hierarchicalSection.section"
                class="mt-1 p-0 before:absolute before:inset-0 before:left-[-10px] before:h-full before:w-1 before:cursor-auto before:rounded-b before:bg-linear-to-b before:from-blue-200 before:to-blue-300 before:content-[''] has-[.tox.tox-edit-focus]:before:bg-linear-to-b has-[.tox.tox-edit-focus]:before:from-blue-600 has-[.tox.tox-edit-focus]:before:to-blue-500"
                :aria-label="Translator.trans('u2_structureddocument.content')"
                :readonly="isSaving"
                @save="save"
                @cancel="cancelEdit"
              />
              <template v-else>
                <button
                  v-if="hierarchicalSection.renderedContent"
                  type="button"
                  class="peer block appearance-none outline-hidden"
                  :aria-label="`${Translator.trans('u2.edit_content')}: ${hierarchicalSection.section.name}`"
                  @click="enterEditMode('content')"
                />

                <TwigComponent
                  :html="hierarchicalSection.renderedContent ?? ''"
                  class="before:absolute before:inset-0 before:left-[-10px] before:h-full before:min-h-6 before:w-1 before:cursor-auto before:rounded-sm before:content-[''] peer-focus:before:bg-linear-to-t peer-focus:before:from-blue-600 peer-focus:before:to-blue-500"
                  @click="enterEditMode('content')"
                />
              </template>
            </div>
          </template>
        </BaseDocumentSection>
        <input v-model="isDirty" type="hidden" name="isDirty" />
      </form>
      <DocumentSectionAddButton
        :disabled="!canUserAddSection"
        class="absolute bottom-0 print:hidden"
        :hierarchical-section="hierarchicalSection"
        @saved="emit('saved')"
      />
    </div>

    <DocumentSections
      v-show="hierarchicalSection.subHierarchicalSections.length > 0 && isVisible"
      :sections="hierarchicalSection.subHierarchicalSections"
      :is-parent-excluded="isExcluded"
      :user-can-edit-content="userCanEditContent"
      :user-can-edit-configuration="userCanEditConfiguration"
      @saved="emit('saved')"
    />
  </div>
</template>
