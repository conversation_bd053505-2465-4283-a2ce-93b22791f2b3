<script setup lang="ts">
import { useBindAttrs } from '@js/composable/useBindAttrs'
import { useField } from 'vee-validate'
import { onMounted, onUnmounted, ref, toRefs, useTemplateRef } from 'vue'
import replaceWidgetStringsWithPlaceholders from '@js/helper/document/replaceWidgetStringsWithPlaceholders'
import FormRow from '@js/components/form/FormRow.vue'
import useSectionEditor from '@js/composable/useSectionEditor'
import type { DocumentSection } from '@js/model/document'
import type { FetchState } from '@js/types'

defineOptions({ inheritAttrs: false })
const { rootAttrs, bindAttrs } = useBindAttrs()

const props = withDefaults(
  defineProps<{
    id?: string
    disabled?: boolean
    errors?: Array<string>
    helpTooltip?: string
    label?: string | false
    name: string
    readonly?: boolean
    required?: boolean
    section?: DocumentSection
    state?: FetchState
    warningTooltip?: string
  }>(),
  {
    id: undefined,
    disabled: false,
    errors: () => [],
    helpTooltip: undefined,
    label: false,
    readonly: false,
    required: false,
    section: undefined,
    state: undefined,
    warningTooltip: undefined,
  }
)
const { value: contentValue, errors } = useField<string>(() => props.name, undefined, {
  bails: false,
  validateOnValueUpdate: false,
})

const emit = defineEmits<(event: 'save' | 'cancel') => void>()

const { section, disabled, readonly } = toRefs(props)
const { editor, initialiseTinyMce } = useSectionEditor(
  contentValue,
  disabled,
  readonly,
  useTemplateRef<HTMLTextAreaElement>('editorRef'),
  section,
  emit
)

const focusOnMount = ref(false)
function focus() {
  if (editor.value) {
    editor.value.focus()
    return
  }
  focusOnMount.value = true
}

const content = ref()

onMounted(async () => {
  content.value = await replaceWidgetStringsWithPlaceholders(contentValue.value, section.value)
  await initialiseTinyMce()
  if (focusOnMount.value) {
    editor.value?.focus()
    focusOnMount.value = false
  }
})

onUnmounted(() => {
  editor.value?.destroy()
})

defineExpose({ focusEditor: focus })
</script>

<template>
  <FormRow
    :id
    :required
    :help-tooltip
    :warning-tooltip
    :label
    class="tinymce-borderless"
    :errors
    v-bind="rootAttrs"
  >
    <template #default="{ fieldId }">
      <textarea
        v-show="content !== undefined"
        :id="fieldId"
        ref="editorRef"
        :value="content"
        rows="1"
        v-bind="bindAttrs"
        :class="{ 'has-errors': errors.length }"
      />

      <input v-model="contentValue" type="hidden" :name="name" />
    </template>
  </FormRow>
</template>

<style scoped>
@reference "@css/app.css";

.tinymce-borderless {
  &:deep(.tox-tinymce) {
    border: none;
  }

  &:deep(.tox.tox-edit-focus .tox-edit-area::before) {
    opacity: 0;
  }
}

:deep(button[data-mce-name='save']),
:deep(button[data-mce-name='cancel']) {
  align-self: end;
  color: theme('colors.white');
  display: inline-block; /* This is needed to prevent text inside buttons from inheriting text-decoration-line when section is excluded: https://drafts.csswg.org/css-text-decor-3/#line-decoration */
  margin-left: theme('spacing.1');
  margin-right: theme('spacing.1');
  padding: 5px theme('spacing.2');
  transition-duration: theme('transitionDuration.300');
  transition-property: color, background-color;
  transition-timing-function: theme('transitionTimingFunction.in-out');
}

:deep(button[data-mce-name='save']) {
  background-color: var(--color-good);
  color: theme('colors.white');

  &:hover {
    background-color: theme('colors.good-darker');
    color: theme('colors.white');
  }
}

:deep(button[data-mce-name='cancel']) {
  color: var(--color-action);

  &:hover {
    color: theme('colors.blue.600');
  }
}

:deep(.tox-toolbar__group) {
  button span {
    display: inline-block; /* This is needed to prevent text inside buttons from inheriting text-decoration-line when section is excluded: https://drafts.csswg.org/css-text-decor-3/#line-decoration */
  }
}

:deep(.tox:not(.tox-tinymce-inline).tox-tinymce--toolbar-sticky-on .tox-editor-header) {
  top: var(
    --sticky-header-height
  ) !important; /* we need to override tinymce's top value that they set in style attribute */
}
</style>
