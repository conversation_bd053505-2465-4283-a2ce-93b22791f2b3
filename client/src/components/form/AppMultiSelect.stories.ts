import AppMultiSelect from '@js/components/form/AppMultiSelect.vue'
import StatusBadge from '@js/components/workflow/StatusBadge.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import type { Meta, StoryObj } from '@storybook/vue3'

const meta: Meta<typeof AppMultiSelect> = {
  title: 'Form/MultiSelect/Field',
  argTypes: {
    'onUpdate:modelValue': { action: 'update:modelValue' },
    'onUpdate:searchQuery': { action: 'update:searchQuery' },
  },
  args: {
    allowCreate: false,
    clearable: true,
    disabled: false,
    hasErrors: false,
    hideSelected: false,
    loading: false,
    modelValue: ['2', '3'],
    searchQuery: '',
    maximumNumberOfOptions: 250,
    iconKey: 'icon',
    options: [
      { id: '1', name: 'Option 1', disabled: false },
      { id: '2', name: 'Option 2', disabled: false },
      { id: '3', name: 'Option 3', disabled: true },
      { id: '4', name: 'Option 4', disabled: false },
      { id: '5', name: 'Option 5 - Far away from the sun', disabled: true, icon: 'document' },
      {
        id: '6',
        name: 'Option 6: Copilot navigates me close to the sun',
        disabled: false,
        icon: 'edit',
      },
      {
        id: '7',
        name: 'Option 7: Very long option... believe me it is sooooooooooooooo long. You will be amazed... hoho, its even longer than you expected ALMOST END NOW',
        disabled: false,
      },
    ],
    placeholder: 'Placeholder',
  },
}
export default meta

export const MultiSelectDefault: StoryObj<typeof AppMultiSelect> = {
  render: (args) => ({
    components: {
      AppMultiSelect,
    },
    setup() {
      return { args }
    },
    template: `
      <AppMultiSelect v-bind="args"/>
    `,
  }),
}

export const MultiSelectWithGroupedOptions: StoryObj<typeof AppMultiSelect> = {
  ...MultiSelectDefault,
  args: {
    options: [
      { id: '1', name: 'Option 1', disabled: false },
      { id: '2', name: 'Option 2', disabled: false },
      { id: '3', name: 'Group 1 - Option 1', disabled: false },
      { id: '4', name: 'Group 1 - Option 2', disabled: false },
      { id: '5', name: 'Group 1 - Option 3', disabled: true },
      { id: '6', name: 'Group 1 - Option 4', disabled: false },
      { id: '7', name: 'Group 2 - Option 1', disabled: false },
      { id: '8', name: 'Group 2 - Option 2', disabled: false },
      { id: '9', name: 'Group 2 - Option 3', disabled: true },
      { id: '10', name: 'Group 2 - Option 4', disabled: false },
    ],
    modelValue: ['1', '3'],
    groupConfigurations: {
      'Group 1': (option) => {
        return option.name.startsWith('Group 1')
      },
      'Group 2': (option) => option.name.startsWith('Group 2'),
    },
  },
}

export const MultiSelectWithCreateNewOptions: StoryObj<typeof AppMultiSelect> = {
  ...MultiSelectDefault,
  args: {
    options: Array.from(Array(5).keys()).map((el) => ({
      id: el.toString(),
      name: el.toString(),
    })),
    modelValue: ['1', '450'],
    allowCreate: true,
  },
}

export const MultiSelectWithManyOptions: StoryObj<typeof AppMultiSelect> = {
  ...MultiSelectDefault,
  args: {
    options: Array.from(Array(999).keys()).map((el) => ({
      id: el.toString(),
      name: el.toString(),
    })),
    modelValue: ['1', '450'],
  },
}

export const MultiSelectWithSlotsStory: StoryObj<typeof AppMultiSelect> = {
  render: (args) => ({
    components: {
      AppMultiSelect,
      StatusBadge,
      SvgIcon,
    },
    setup() {
      return { args }
    },
    template: `
      <AppMultiSelect v-bind="args">
      <template #selectedValue="{ option, activeOption, clear }">
            <span class="inline-flex flex-nowrap items-center gap-x-0.5">
              <StatusBadge
                :status="option"
                :class="{ 'brightness-90': activeOption === option.id }"
              />
              <button
                type="button"
                v-if="!args.disabled"
                class="inline-flex items-center text-gray-500 hover:text-gray-800 hover:no-underline focus:text-gray-800 focus:no-underline active:text-gray-500"
                @click="clear(option.id)"
              >
                <SvgIcon icon="cross" size="small" />
              </button>
            </span>
      </template>
      <template #option="{ option }">
        <StatusBadge :status="option"/>
      </template>
      </AppMultiSelect>
    `,
  }),
  args: {
    options: [
      { id: '/api/statuses/1', name: 'Open', type: 'OPEN' },
      { id: '/api/statuses/2', name: 'In Progress', type: 'IN_PROGRESS' },
      { id: '/api/statuses/3', name: 'Pending', type: 'IN_PROGRESS' },
      { id: '/api/statuses/4', name: 'Done', type: 'COMPLETE' },
    ],
    modelValue: ['/api/statuses/1'],
  },
}
