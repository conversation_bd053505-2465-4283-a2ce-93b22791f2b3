<script setup lang="ts">
import type { Period } from '@js/api/periodApi'
import AppSelect from '@js/components/form/AppSelect.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'

const {
  periods = [],
  disabled = false,
  hasErrors = false,
  required = false,
  valueKey = '@id',
  enableFilter = true,
} = defineProps<{
  periods?: Array<Period>
  disabled?: boolean
  hasErrors?: boolean
  required?: boolean
  valueKey?: 'id' | '@id'
  enableFilter?: boolean
}>()

const modelValue = defineModel<Period['@id'] | Period['id'] | null>('modelValue', {
  required: false,
  default: null,
})
const searchQuery = defineModel<string | undefined>('searchQuery', { required: false })
</script>

<template>
  <AppSelect
    v-model="modelValue"
    v-model:search-query="searchQuery"
    :enable-filter="enableFilter"
    :options="periods"
    :placeholder="Translator.trans('u2.select_a_period')"
    :disabled="disabled"
    :required="required"
    :has-errors="hasErrors"
    :group-configurations="{
      [Translator.trans('u2.open')]: (option) => !option.closed,
      [Translator.trans('u2.closed')]: (option) => option.closed,
    }"
    sort-key="name"
    :value-key="valueKey"
  >
    <template #optionIcon="{ option }">
      <SvgIcon v-if="option?.closed" :icon="'lock-closed'" class="text-bad" />
    </template>
  </AppSelect>
</template>
