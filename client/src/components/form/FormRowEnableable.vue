<script lang="ts" setup>
import $ from 'jquery'
import { onMounted, ref } from 'vue'
import AppCheckbox from '@js/components/form/AppCheckbox.vue'

const element = ref()

withDefaults(defineProps<{ disabled?: boolean }>(), { disabled: false })

onMounted(() => {
  const settings = {
    checkboxClass: '.js-enableable-form-checkbox',
    fieldShouldBeKeptDisabledClass: '.js-enableable-form-keep-disabled',
  }

  const togglerCheckboxes = $(element.value).find(settings.checkboxClass)

  $(togglerCheckboxes).each((index, checkbox) => {
    const $checkbox = $(checkbox)
    if (!$checkbox.is(':checked')) {
      const uncheckedValueField = $checkbox
        .parent()
        .find('input, textarea, select')
        .not(settings.checkboxClass)
      const $uncheckedValueField = $(uncheckedValueField)
      const $enableableContainer = $uncheckedValueField.closest('.js-enableable-container')
      $uncheckedValueField.prop('disabled', true)
      $enableableContainer.addClass('bg-skin-disabled').removeClass('bg-white')
      $enableableContainer
        .find('.js-enableable-button')
        .removeClass('cursor-pointer')
        .addClass('cursor-not-allowed')
        .prop('disabled', true)
    }
    $checkbox.on('change.enableableForm', function () {
      const $thisCheckbox = $(this)
      const valueFields = $thisCheckbox
        .parent()
        .find('input, textarea, select')
        .not(settings.checkboxClass)

      $(valueFields).each((index, field) => {
        const checked = $thisCheckbox.is(':checked')
        const $field = $(field)
        const fieldShouldBeKeptDisabled =
          $field.is(settings.fieldShouldBeKeptDisabledClass) ||
          $field.parent().is(settings.fieldShouldBeKeptDisabledClass)
        const $enableableContainer = $field.closest('.js-enableable-container')

        if (fieldShouldBeKeptDisabled) {
          return
        }
        $field.prop('disabled', !checked)
        $enableableContainer.toggleClass('bg-skin-disabled', !checked)
        $enableableContainer.toggleClass('bg-white', checked)
        $enableableContainer
          .find('.js-enableable-button')
          .removeClass('cursor-not-allowed')
          .addClass('cursor-pointer')
          .prop('disabled', !checked)

        if ($field.val()) {
          $enableableContainer.find('.js-enableable-cross').toggleClass('hidden', !checked)
        }
      })
    })
  })
})
</script>

<template>
  <div ref="element" class="flex w-full items-center gap-x-2 p-1">
    <AppCheckbox :disabled="disabled" :model-value="false" class="js-enableable-form-checkbox" />
    <slot />
  </div>
</template>
