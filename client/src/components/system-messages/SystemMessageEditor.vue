<script setup lang="ts">
import { useQueryClient } from '@tanstack/vue-query'
import { toTypedSchema } from '@vee-validate/zod'
import { toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { z } from 'zod'
import invariant from 'tiny-invariant'
import { isAxiosError } from 'axios'
import { queries } from '@js/query'
import { systemMessageApi } from '@js/api/systemMessageApi'
import FieldDateTimePicker from '@js/components/form/FieldDateTimePicker.vue'
import FieldSelect from '@js/components/form/FieldSelect.vue'
import FieldTextarea from '@js/components/form/FieldTextarea.vue'
import FormErrors from '@js/components/form/FormErrors.vue'
import useForm from '@js/composable/useForm'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import { useNotificationsStore } from '@js/stores/notifications'
import { usePageStore } from '@js/stores/page'
import Translator from '@js/translator'
import type { SystemMessage } from '@js/model/system_message'

const props = defineProps<{
  message?: Partial<SystemMessage>
}>()

const router = useRouter()

const types = [
  {
    id: 'INFO',
    name: 'Info',
  },
  {
    id: 'WARNING',
    name: 'Warning',
  },
]

const { message } = toRefs(props)

const { handleSubmit, setResponseErrors, unmappedErrors, setValues } = useForm({
  validationSchema: toTypedSchema(
    z.object({
      type: z.string(),
      content: z.string(),
      displayFrom: z.string().datetime({ offset: true }).optional().nullable(),
      displayTo: z.string().datetime({ offset: true }).optional().nullable(),
    })
  ),
  initialValues: getValuesFromMessage(message.value),
})

function getValuesFromMessage(message: Partial<SystemMessage> | undefined) {
  return {
    type: message ? message.type : 'INFO',
    content: message ? message.content : undefined,
    displayFrom: message?.displayFrom,
    displayTo: message?.displayTo,
  }
}

const pageStore = usePageStore()

const notificationsStore = useNotificationsStore()
const { resolveNotification } = useHandleAxiosErrorResponse()

const queryClient = useQueryClient()

const save = handleSubmit(async (values) => {
  pageStore.loading = true

  try {
    const { data: message } = await systemMessageApi.saveSystemMessage({
      ...props.message,
      ...values,
      displayFrom: values.displayFrom ?? null,
      displayTo: values.displayTo ?? null,
    })
    await router.push({
      name: 'SystemMessageEdit',
      params: { id: message.id },
    })
    queryClient.invalidateQueries({
      queryKey: queries.systemMessages._def,
    })
    setValues(getValuesFromMessage(message))
    notificationsStore.addSuccess(Translator.trans('u2_core.success'))
  } catch (error) {
    await resolveNotification(error)
    invariant(isAxiosError(error) && error.response)
    setResponseErrors(error.response)
  } finally {
    pageStore.loading = false
  }
})
</script>

<template>
  <form id="system_message_form" name="system_message" @submit.prevent="save">
    <FormErrors :errors="unmappedErrors" />

    <!-- Type -->
    <FieldSelect
      :label="Translator.trans('u2_core.type')"
      name="type"
      :options="types"
      :required="true"
    />

    <!-- Display from -->
    <FieldDateTimePicker
      id="displayFrom"
      name="displayFrom"
      :label="Translator.trans('u2_core.display_from')"
    />

    <!-- Display to -->
    <FieldDateTimePicker
      id="displayTo"
      name="displayTo"
      :label="Translator.trans('u2_core.display_to')"
    />

    <!-- Content -->
    <FieldTextarea
      id="system_message_content"
      :label="Translator.trans('u2.content')"
      name="content"
      :required="true"
      class="max-w-3xl"
    />
  </form>
</template>
