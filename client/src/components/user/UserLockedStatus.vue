<script setup lang="ts">
import { unlockUser } from '@js/api/userApi'
import { computed, ref, toRef } from 'vue'
import { skipToken, useQuery } from '@tanstack/vue-query'
import invariant from 'tiny-invariant'
import { useAuthStore } from '@js/stores/auth'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import YesNo from '@js/components/YesNo.vue'
import { isUserWithAllProperties } from '@js/model/user'
import { queries } from '@js/query'
import type { UserWithAllProperties } from '@js/model/user'

const props = defineProps<{
  user: UserWithAllProperties
}>()

const user = toRef(props, 'user')
const { data: resolvedUser, refetch } = useQuery({
  ...queries.users.single(user.value.id),
  /*
  TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
    See: https://github.com/lukemorales/query-key-factory/issues/100
 */
  queryFn: computed(() => (user.value ? queries.users.single(user.value.id).queryFn : skipToken)),
  initialData:
    user.value && typeof user.value === 'object' && isUserWithAllProperties(user.value)
      ? user.value
      : undefined,
})

const isDialogOpen = ref(false)
const authStore = useAuthStore()
const canUnlock = computed(() => authStore.hasRole('ROLE_USER_GROUP_ADMIN'))
const isLocked = computed(() => {
  invariant(resolvedUser.value && isUserWithAllProperties(resolvedUser.value))
  return resolvedUser.value?.locked
})
const showUnlock = computed(() => {
  invariant(resolvedUser.value && isUserWithAllProperties(resolvedUser.value))
  return isLocked.value && canUnlock.value
})
async function unlock() {
  const notificationsStore = useNotificationsStore()
  try {
    const response = await unlockUser(user.value.id)
    notificationsStore.addByType(response.data.messages)
    await refetch()
  } catch {
    notificationsStore.addError(Translator.trans('u2.unlock_user_unsuccessful'))
  } finally {
    isDialogOpen.value = false
  }
}
</script>

<template>
  <span v-if="user">
    <YesNo :value="isLocked" with-text />
    <a v-if="showUnlock" class="ml-1" @click.prevent="isDialogOpen = true">
      {{ Translator.trans('u2.unlock').toLowerCase() }}
    </a>

    <ConfirmationDialog
      v-if="isDialogOpen"
      :accept-text="Translator.trans('u2.unlock_user')"
      :title="Translator.trans('u2.confirm_user_unlock')"
      @confirm="unlock"
      @close="isDialogOpen = false"
    >
      {{ Translator.trans('u2.unlock_user_confirmation') }}
    </ConfirmationDialog>
  </span>
</template>
