import { computed, toRef, unref } from 'vue'
import { useQuery } from '@tanstack/vue-query'
import { queries } from '@js/query'
import type { ConditionType } from '@js/api/transitionConditionTypeApi'
import type { MaybeRefOrGetter } from 'vue'

export default function useConditionTypeQuery(
  id: MaybeRefOrGetter<ConditionType['id'] | undefined | null>
) {
  const idRef = toRef(id as ConditionType['id'])
  return useQuery({
    ...queries.conditionTypes.single(idRef),
    enabled: computed(() => !!unref(idRef)),
  })
}
