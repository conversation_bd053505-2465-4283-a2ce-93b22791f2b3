import { computed, toRef, toValue, unref } from 'vue'
import { useQuery } from '@tanstack/vue-query'
import { queries } from '@js/query'
import type { MaybeRefOrGetter } from 'vue'
import type { TaskParams } from '@js/model/task'

export default function useDocumentEditRenderedSectionsDataQuery(
  taskParams: MaybeRefOrGetter<TaskParams>,
  queryOptions?: {
    enabled?: MaybeRefOrGetter<boolean>
  }
) {
  const taskParamsRef = toRef(taskParams)

  return useQuery({
    ...queries.document.editDocumentData(taskParamsRef)._ctx.sections,
    ...queryOptions,
    enabled: computed(() =>
      toValue(queryOptions?.enabled) === false ? false : !!unref(taskParamsRef)
    ),
  })
}
