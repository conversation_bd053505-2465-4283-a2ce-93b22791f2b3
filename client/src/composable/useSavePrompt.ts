import cloneDeep from 'lodash/cloneDeep'
import { computed, onUnmounted, ref, watch } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'
import Translator from '@js/translator'
import type { Ref } from 'vue'

const isPageLeaveConfirmed = ref(false)
export default function useSavePrompt(data: Ref<object>) {
  const initial = ref<object>()

  const hasChanges = computed(() => {
    console.log('initial VALUES', JSON.stringify(initial.value))
    console.log('VALUES', JSON.stringify(data.value))
    return JSON.stringify(initial.value) !== JSON.stringify(data.value)
  })
  function triggerConfirmationPrompt(event: BeforeUnloadEvent) {
    event.preventDefault() // Cancel the event and show the standard dialog
  }

  function addEventListener() {
    window.addEventListener('beforeunload', triggerConfirmationPrompt, false)
  }

  function removeEventListener() {
    window.removeEventListener('beforeunload', triggerConfirmationPrompt, false)
  }

  watch(hasChanges, (newValue) => {
    if (newValue) {
      addEventListener()
      return
    }
    removeEventListener()
  })

  function confirm() {
    return window.confirm(Translator.trans('u2.page_has_unsaved_changes'))
  }

  onBeforeRouteLeave(() => {
    if (!hasChanges.value || isPageLeaveConfirmed.value) {
      return true
    }

    if (confirm()) {
      isPageLeaveConfirmed.value = true
      return true
    }

    return false
  })

  onUnmounted(() => {
    initial.value = undefined
    removeEventListener()
  })

  function set() {
    isPageLeaveConfirmed.value = false
    initial.value = cloneDeep(data.value)
    console.log(initial.value)
  }

  function revert() {
    data.value = cloneDeep(initial.value || {})
  }

  set()

  return {
    set,
    revert,
    changed: hasChanges,
    confirm,
  }
}
