import { updateSavedFilterAssignedUsers } from '@js/api/savedFilterApi'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { queries } from '@js/query'
import type { SavedFilter } from '@js/model/saved-filter'
import type { User } from '@js/model/user'

/**
 * Returns a TansStack mutation to update the users assigned to a saved filter.
 */
export default function useSavedFilterAssignedUsersUpdateMutation() {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (variables: {
      savedFilter: SavedFilter
      userIds: Array<NonNullable<User['@id']>>
    }) => {
      return updateSavedFilterAssignedUsers(variables.savedFilter.id, variables.userIds)
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({
        queryKey: queries.savedFilters.single(variables.savedFilter.id).queryKey,
      })
    },
  })
}
