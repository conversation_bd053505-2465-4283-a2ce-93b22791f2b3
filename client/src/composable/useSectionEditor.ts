import { fetchDocumentWidgetEditorConfiguration } from '@js/api/documentApi'
import replacePlaceholdersWithWidgetStrings from '@js/helper/document/replacePlaceholdersWithWidgetStrings'
import $ from 'jquery'
import invariant from 'tiny-invariant'
import { ref, watch } from 'vue'
import { decodeDocumentWidget } from '@js/helper/document/decodeDocumentWidget'
import resolveWidgetPlaceholder from '@js/helper/document/resolveWidgetPlaceholder'
import sanitizeWidget from '@js/helper/document/sanitizeWidget'
import { configurableWidgets, documentWidgets } from '@js/model/document'
import tinymce from '@js/tinymce'
import Translator from '@js/translator'
import * as StringFormatter from '@js/utilities/string-formatter'
import type { Editor, Events, Ui } from 'tinymce'
import type { AnyDocumentWidget, DocumentImageWidget, DocumentSection } from '@js/model/document'
import type { Ref, ShallowRef } from 'vue'

// The type for dialog config should be exported from tinymce, but it's not, so we use this type instead which is based on tinymce's interface DialogSpec<T extends DialogData> { ...}
export type DialogConfig = {
  title: string
  size?: string
  body: {
    type: Ui.Dialog.PanelSpec['type']
    items: Array<Record<string, unknown>>
  }
  buttons: Array<Record<string, unknown>>
  initialData?: Record<string, unknown>
  onAction?: (api: Record<string, unknown>) => void
  onChange?: (api: Record<string, unknown>) => void
  onSubmit?: (api: Ui.Dialog.DialogInstanceApi<Ui.Dialog.DialogData>) => void
  onClose?: () => void
  onCancel?: (api: Record<string, unknown>) => void
  onTabChange?: (api: Record<string, unknown>) => void
}

export default function useSectionEditor(
  modelValue: Ref<string>,
  content: Ref<string>,
  disabled: Ref<boolean>,
  readonly: Ref<boolean>,
  editorRef: Readonly<ShallowRef<HTMLTextAreaElement | null>>,
  section: Ref<DocumentSection | undefined>,
  emit: (event: 'save' | 'cancel') => void
) {
  const editorInstance = ref<Editor>()

  function updateImageWidgetWidthParameter(widget: AnyDocumentWidget, width: string) {
    $.extend(true, widget, {
      parameters: {
        width: width === '' ? 'auto' : width,
      },
    })
    return widget
  }

  async function openImageWidgetDialog(widget: DocumentImageWidget) {
    invariant(editorInstance.value)
    const dialogConfig = await getDialogConfig(widget)

    const attachedImagesIds = $.map(
      dialogConfig.body.items[0].items,
      (value: { text: string; value: string }) => value.value
    )
    if (attachedImagesIds.length < 1) {
      editorInstance.value.windowManager.alert(Translator.trans('u2.no_images_in_section'))
      return
    }

    // @ts-expect-error:tinymce expects interface DialogSpec<T extends DialogData> {...} for dialogConfig but doesn't export this interface
    editorInstance.value.windowManager.open({
      ...dialogConfig,
      initialData: {
        ...dialogConfig.initialData,
        id: widget.parameters.id,
      },
    })
    fixTooltips(dialogConfig)
  }

  function getToolbar2() {
    return section.value
      ? 'imagedocumentwidget filedocumentwidget documentwidget table charmap | save cancel'
      : 'table charmap | save cancel'
  }

  /**
   * @param items Type of items argument is not exported from tinymce, should be something like tinymce's BodyComponentSpec[]
   */
  function getInputsWithTooltips(
    items: Array<Record<string, unknown>>
  ): Array<Record<string, string>> {
    return items.reduce(function (tooltips: Array<Record<string, string>>, item) {
      if (item.type === 'input' && item.tooltip) {
        tooltips.push({
          [item.label as unknown as string]: item.tooltip as unknown as string,
        })
      }
      if (item.items) {
        tooltips.push(...getInputsWithTooltips(item.items as Array<Record<string, unknown>>))
      }
      return tooltips
    }, [])
  }

  function getDialogConfig(widget: AnyDocumentWidget) {
    invariant(section.value)
    return fetchDocumentWidgetEditorConfiguration(section.value, widget).then((response) => {
      const dialogConfig = response.data

      dialogConfig.onSubmit = (api) => {
        $.extend(widget, { parameters: api.getData() })
        // @ts-expect-error: Tinymce v5 expects boolean instead of widget which is incorrect, maybe they'll fix their types in future
        editorInstance.value.execCommand('insertWidgetPlaceholder', widget)
        api.close()
      }
      return dialogConfig
    })
  }

  /**
   * TODO: remove when TinyMCE authors add configuration options for tooltips
   * Ticket: https://jira.universalunits.net/browse/UU-5037
   * Issue reported to TinyMCE: https://github.com/tinymce/tinymce/issues/5018
   */
  const fixTooltips = (dialog: DialogConfig) => {
    const inputsWithTooltips = getInputsWithTooltips(dialog.body.items)
    $('input:text[id*="form-field_"]').each(function (i, el) {
      const inputLabel = (el as HTMLInputElement).labels?.[0].textContent
      invariant(inputLabel, 'Input label is null or undefined')
      const tooltip = inputsWithTooltips.find((input) => input[inputLabel])?.[inputLabel]
      if (tooltip) {
        el.setAttribute('title', tooltip)
      }
    })
  }

  async function handleImageWidgetClick($targetImageWidget: JQuery<HTMLSpanElement>) {
    invariant(editorInstance.value)
    const $placeholder = $targetImageWidget.parent('[data-image-document-widget]')

    const widget = decodeDocumentWidget(
      $placeholder.children('[data-document-widget-settings]').text()
    )
    updateImageWidgetWidthParameter(
      widget,
      $placeholder.children('img')?.prop('style')?.width ?? ''
    )
    editorInstance.value.selection.select($placeholder[0])
    invariant(widget.name === 'image')
    await openImageWidgetDialog(widget)
  }

  async function handleWidgetClick($targetWidget: JQuery<HTMLElement>) {
    invariant(editorInstance.value)
    const $placeholder = $targetWidget.parent(
      '[data-document-widget], [data-inline-document-widget]'
    )
    editorInstance.value.selection.select($placeholder[0])

    const dialogConfig = await getDialogConfig(
      decodeDocumentWidget($placeholder.children('[data-document-widget-settings]').text())
    )

    // @ts-expect-error:tinymce expects interface DialogSpec<T extends DialogData> {...} for dialogConfig but doesn't export this interface
    editorInstance.value.windowManager.open(dialogConfig)
  }

  const isDirty = ref(false)
  async function initialiseTinyMce() {
    invariant(editorRef.value)
    const [editor] = await tinymce.initialize({
      selector: undefined,
      target: editorRef.value,
      readonly: disabled.value || readonly.value,
      body_class: 'document-section-content document-section-content-editor',
      toolbar_sticky: true,
      max_height: undefined,
      image_button: !section.value,
      media_button: !section.value,
      toolbar2: getToolbar2(),
      setup: (editor) => {
        editor.on('input', () => {
          content.value = editor.getContent()
          isDirty.value = true
        })

        editor.on('change', () => {
          content.value = editor.getContent()
          isDirty.value = true
        })

        editor.on('keyup', (event) => {
          if (['Backspace', 'Delete'].includes(event.key)) {
            /* TODO: Find a better solution for removing image widgets
               It should be possible to remove the image widget by pressing backspace when the image widget is selected without extra checks.
               Without the "if" statement below if the resize controls of the image widget are visible, the image widget is not removed by pressing backspace.
               If the focus is on the outer span the widget can be removed by pressing backspace.
             */
            const $selectedNode = $(editor.selection.getNode())
            if ($selectedNode.is('[data-image-document-widget] > img')) {
              $selectedNode.parent('[data-image-document-widget]').remove()
              content.value = editor.getContent()
            }
          }
        })

        editor.on('keydown', (event: KeyboardEvent) => {
          if (event.code === 'Escape') {
            emit('cancel')
          }
        })

        editor.on('click', (event) => {
          const $targetImageWidget: JQuery<HTMLSpanElement> = $(event.target).closest(
            '[data-image-document-widget-edit]'
          )
          if ($targetImageWidget.length === 1) {
            handleImageWidgetClick($targetImageWidget)
          }

          const $targetWidget = $(event.target).closest('[data-document-widget-edit]')

          if ($targetWidget.length === 1) {
            handleWidgetClick($targetWidget)
          }
        })

        editor.on('dblclick', (event) => {
          if ($(event.target).is('[data-image-document-widget] img')) {
            handleImageWidgetClick($(event.target))
          }
        })

        editor.addCommand('insertWidgetPlaceholder', (widget) => {
          invariant(section.value)
          resolveWidgetPlaceholder(widget as unknown as AnyDocumentWidget, section.value).then(
            (response) => {
              editor.insertContent(response)
            }
          )
        })

        editor.ui.registry.addButton('save', {
          text: Translator.trans('u2.save'),
          onAction: () => {
            modelValue.value = replacePlaceholdersWithWidgetStrings(content.value)
            emit('save')
          },
        })

        editor.ui.registry.addButton('cancel', {
          text: Translator.trans('u2.cancel'),
          onAction: () => {
            modelValue.value = replacePlaceholdersWithWidgetStrings(content.value)
            emit('cancel')
          },
        })

        editor.ui.registry.addButton('imagedocumentwidget', {
          icon: 'image',
          onAction: async () => {
            const selectedNode = editor.selection.getNode()

            const targetNode = selectedNode.hasAttribute('data-image-document-widget')
              ? $(selectedNode).children('img')[0]
              : selectedNode

            if (!(targetNode.nodeName === 'IMG' && 'width' in targetNode)) {
              await openImageWidgetDialog(
                sanitizeWidget({ name: 'image', parameters: { width: 'auto', id: '' } })
              )
              return
            }

            const widgetSettings = $(targetNode)
              .parent()
              .find('[data-document-widget-settings]')
              .text()

            /*
              Ensures that selecting a new image file in the dialog replaces the current image in the editor.
              Without this step an additional image is added instead of replacing the existing one.
            */
            editorInstance.value?.selection.select(targetNode.parentNode ?? targetNode)

            const widget = sanitizeWidget(
              updateImageWidgetWidthParameter(
                decodeDocumentWidget(widgetSettings),
                targetNode.style.width
              )
            )
            invariant(widget.name === 'image')
            await openImageWidgetDialog(widget)
          },
          onSetup: (api) => {
            const editorEventCallback = (eventApi: Events.NodeChangeEvent) => {
              api.setEnabled(
                $(eventApi.element).parent().is('[data-image-document-widget]') ||
                  eventApi.element.nodeName !== 'IMG'
              )
            }
            editor.on('NodeChange', editorEventCallback)

            return () => editor.off('NodeChange', editorEventCallback)
          },
          tooltip: Translator.trans('u2.insert_image'),
        })

        editor.ui.registry.addMenuButton('documentwidget', {
          fetch: function (callback) {
            // @ts-expect-error: we need to type items as Array<MenuItemSpec> but tinymce doesn't export this type
            const items = []
            documentWidgets.forEach((widgetName) => {
              if (widgetName !== 'file' && widgetName !== 'image') {
                items.push({
                  onAction: function () {
                    if (configurableWidgets.includes(widgetName)) {
                      getDialogConfig(
                        sanitizeWidget({
                          name: widgetName,
                        })
                      ).then((dialogConfig) => {
                        // @ts-expect-error:tinymce expects interface DialogSpec<T extends DialogData> {...} for dialogConfig but doesn't export this interface
                        editor.windowManager.open(dialogConfig)
                      })
                      return
                    }

                    editor.execCommand(
                      'insertWidgetPlaceholder',
                      // @ts-expect-error: Tinymce v5 expects boolean instead of widget which is incorrect, maybe they'll fix their types in future
                      sanitizeWidget({
                        name: widgetName,
                      })
                    )
                  },
                  text: StringFormatter.dashedToReadable(widgetName),
                  type: 'menuitem',
                })
              }
            })

            // @ts-expect-error: we need to type items as Array<MenuItemSpec> but tinymce doesn't export this type
            callback(items)
          },
          icon: 'plus',
          tooltip: Translator.trans('u2.insert_widget'),
        })

        editor.ui.registry.addButton('filedocumentwidget', {
          icon: 'document-properties',
          onAction: () => {
            getDialogConfig(
              sanitizeWidget({
                name: 'file',
              })
            ).then((dialogConfig) => {
              const attachedFilesIds = $.map(
                dialogConfig.body.items[0].items,
                (value: { text: string; value: string }) => value.value
              )
              if (attachedFilesIds.length < 1) {
                editor.windowManager.alert(Translator.trans('u2.no_files_in_section'))
                return
              }
              // @ts-expect-error:tinymce expects interface DialogSpec<T extends DialogData> {...} for dialogConfig but doesn't export this interface
              editor.windowManager.open(dialogConfig)
              fixTooltips(dialogConfig)
            })
          },
          tooltip: Translator.trans('u2.insert_file_reference'),
        })
      },
    })
    editorInstance.value = editor
  }

  watch([disabled, readonly], () => {
    if (editorInstance.value) {
      editorInstance.value.mode.set(disabled.value || readonly.value ? 'readonly' : 'design')
    }
  })

  return {
    editor: editorInstance,
    initialiseTinyMce,
    isDirty,
  }
}
