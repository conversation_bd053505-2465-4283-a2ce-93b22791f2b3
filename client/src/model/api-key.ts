import { computed } from 'vue'
import Translator from '@js/translator'
import type { ApiResource } from '@js/types'

export const ttlOptionToReadableName: Record<number, string> = {
  '-1': Translator.trans('u2.infinite'),
  7: Translator.trans('u2.7_days'),
  30: Translator.trans('u2.30_days'),
  60: Translator.trans('u2.60_days'),
  90: Translator.trans('u2.90_days'),
} as const

export type ApiKey = ApiResource & {
  id: string
  ttl: keyof typeof ttlOptionToReadableName
  value?: string
  name: string
  description?: string
  enabled: boolean
  createdAt?: string
  lastUsedAt?: string
  expiresAt?: string
  readableApiKey?: string
}

export const ttlSelectOptions = computed(() => {
  return Object.entries(ttlOptionToReadableName).map(([key, value]) => ({
    id: key,
    name: value,
  }))
})
