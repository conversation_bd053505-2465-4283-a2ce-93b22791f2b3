import type { ApiResource, ApiResourceId } from '@js/types'

/**
 * This value should be the same as defined in the php.ini files for the `upload_max_filesize`.
 */
export const maxUploadSize = 52428800 // 50 MB

export type SystemSettings = ApiResource & {
  '@id': '/api/system-settings'
  '@type': 'SystemSettings'
  securityPasswordRulesResetHoursValid: number
  u2Locale: string | null
  loginColor: string | null
  securityMaxLoginAttempts: number
  securityPasswordRulesUniqueHistoryCount: number
  securityPasswordRulesMinLength: number
  securityPasswordRulesMaxAgeInDays: number
  securityFileUploadWhitelist: Array<string>
  securityUnitEditFieldWhitelist: Array<string>
  securityTwoFactorIsEnforced: boolean
  securityPasswordRulesRequireNumber: boolean
  securityPasswordRulesRequireNonAlphanumericCharacter: boolean
  securityPasswordRulesRequireUppercaseLetter: boolean
  securityPasswordRulesRequireLowercaseLetter: boolean
  taxAssessmentOpenBoy: boolean
  maxUploadSize: number
  applicationCurrency: ApiResourceId
}
