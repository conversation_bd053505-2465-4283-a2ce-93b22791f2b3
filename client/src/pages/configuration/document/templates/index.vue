<script setup lang="ts">
import { fetchDocumentTemplatesByQuery } from '@js/api/documentTemplateApi'
import { computed, ref } from 'vue'
import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router'
import { isAxiosError } from 'axios'
import { useHead } from '@vueuse/head'
import { watchDebounced } from '@vueuse/core'
import AppPage from '@js/components/page-structure/AppPage.vue'
import AppSearch from '@js/components/form/AppSearch.vue'
import AppTable from '@js/components/table/AppTable.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import { dashedToReadable } from '@js/utilities/string-formatter'
import { flattenObject } from '@js/utilities/flattenObject'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import useTable from '@js/composable/useTable'
import { getIdFromIri } from '@js/utilities/api-resource'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { ApiQuery } from '@js/composable/useTable'
import type { DocumentTemplate } from '@js/model/document-template'

const totalItems = ref(0)

const itemsRaw = ref<Array<DocumentTemplate>>([])

useHead({ title: Translator.trans('u2_structureddocument.document_templates') })

const fetchListData = async (query: ApiQuery) => {
  const { data } = await fetchDocumentTemplatesByQuery(query)
  itemsRaw.value = data['hydra:member']
  totalItems.value = data['hydra:totalItems']
}

const items = computed(() => {
  return itemsRaw.value.map((documentTemplate) => {
    return flattenObject({
      ...documentTemplate,
      updatedBy: documentTemplate.updatedBy ? getIdFromIri(documentTemplate.updatedBy) : undefined,
      createdBy: documentTemplate.createdBy ? getIdFromIri(documentTemplate.createdBy) : undefined,
    })
  })
})

const router = useRouter()
const route = useRoute()

const {
  columns,
  query,
  changePage,
  changePageSize,
  fromLocationQuery,
  setSelectedColumns,
  apiQuery,
  toLocationQuery,
  sort,
} = useTable(
  [
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2.name'),
      id: 'name',
      required: true,
    },
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.type'),
      id: 'type',
      required: true,
    },
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2.description'),
      id: 'description',
      selectedByDefault: false,
    },
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.created_by'),
      id: 'createdBy',
      type: 'user',
      selectedByDefault: true,
    },
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.created_at'),
      id: 'createdAt',
      type: 'date',
      selectedByDefault: true,
    },
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.updated_by'),
      id: 'updatedBy',
      type: 'user',
      selectedByDefault: true,
    },
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.updated_at'),
      id: 'updatedAt',
      type: 'date',
      selectedByDefault: true,
    },
    {
      align: 'right',
      name: '',
      id: 'actions',
      required: true,
    },
  ],
  { sort: { createdAt: 'DESC' }, filter: { search: '' } },
  { cacheKey: 'document-template' }
)
watchDebounced(
  query,
  (newValue) => {
    router.push({ query: toLocationQuery(newValue) })
  },
  { deep: true, debounce: 400 }
)

const newQuery = fromLocationQuery(route.query)
if (JSON.stringify(newQuery) !== JSON.stringify(query.value)) {
  query.value = newQuery
}

const { handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()
try {
  await fetchListData(apiQuery.value)
} catch (error) {
  if (
    !isAxiosError(error) ||
    (error.response && !(await handleAxiosErrorResponse(error.response)))
  ) {
    throw error
  }
}

onBeforeRouteUpdate(async (to) => {
  const newQuery = fromLocationQuery(to.query)
  if (JSON.stringify(newQuery) !== JSON.stringify(query.value)) {
    query.value = newQuery
  }

  const { handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()
  try {
    await fetchListData(apiQuery.value)
  } catch (error) {
    if (
      !isAxiosError(error) ||
      (error.response && !(await handleAxiosErrorResponse(error.response)))
    ) {
      throw error
    }
  }
})
</script>

<template>
  <AppPage>
    <template #header
      ><PageHeader :title="Translator.trans('u2_structureddocument.document_templates')">
        <ButtonBasic
          button-style="solid"
          icon="upload"
          :to="{ name: 'TemplateImport' }"
          :tooltip="Translator.trans('u2_structureddocument.import_new_document_template')"
        >
          {{ Translator.trans('u2.import.import') }}
        </ButtonBasic>
      </PageHeader>
    </template>
    <AppSearch v-model="query.filter.search" class="w-96 max-w-full" />
    <AppTable
      :query="query"
      :headers="columns"
      :items="items ?? []"
      :total-items="totalItems"
      :selected="query.selectedColumns"
      horizontal-scroll
      @sort="sort"
      @page-change="changePage"
      @new-column-selection="setSelectedColumns"
      @page-size-change="changePageSize"
    >
      <template #item-name="{ item }">
        <strong>{{ item.name }}</strong>
      </template>
      <template #item-type="{ item }">
        {{ dashedToReadable(item.type) }}
      </template>
      <template #item-actions="{ item }">
        <ButtonBasic
          icon="config"
          :to="{ name: 'TemplateConfigure', params: { id: item.id } }"
          :tooltip="
            Translator.trans('u2_structureddocument.configure_template_with_given_template_type', {
              document_template_type: item.type,
            })
          "
        >
          {{ Translator.trans('u2.configuration') }}
        </ButtonBasic>
        <ButtonBasic icon="view" :to="{ name: 'TemplatePreview', params: { id: item.id } }">
          {{ Translator.trans('u2_structureddocument.preview') }}
        </ButtonBasic>
      </template>
    </AppTable>
  </AppPage>
</template>
