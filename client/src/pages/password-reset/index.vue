<script setup lang="ts">
import { requestPasswordReset } from '@js/api/passwordApi'
import { toTypedSchema } from '@vee-validate/zod'
import { isAxiosError } from 'axios'
import invariant from 'tiny-invariant'
import { ref } from 'vue'
import { useHead } from '@vueuse/head'
import { useLocalStorage } from '@vueuse/core'
import { z } from 'zod'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import FieldInputText from '@js/components/form/FieldInputText.vue'
import useForm from '@js/composable/useForm'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import FormRow from '@js/components/form/FormRow.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'

const usernameLocaleStorage = useLocalStorage('username', '')

const loading = ref(false)
const notificationStore = useNotificationsStore()
useHead({ title: Translator.trans('u2_core.request_a_password_reset') })

const { handleSubmit, setResponseErrors } = useForm({
  validationSchema: toTypedSchema(
    z.object({
      usernameOrEmail: z.string().min(1),
    })
  ),
  initialValues: { usernameOrEmail: usernameLocaleStorage.value },
})

const { resolveNotification } = useHandleAxiosErrorResponse()

const save = handleSubmit(async (values) => {
  loading.value = true
  notificationStore.clear()
  try {
    await requestPasswordReset(values.usernameOrEmail)
    notificationStore.addSuccess(
      Translator.trans('u2.authentication.password_reset.success.email_has_been_sent')
    )
  } catch (error) {
    await resolveNotification(error)
    invariant(isAxiosError(error) && error.response)
    setResponseErrors(error.response)
  } finally {
    loading.value = false
  }
})
</script>

<template>
  <form
    id="password_reset_request_form"
    name="password_reset_request_form"
    class="no-required-stars"
    @submit.prevent="save"
  >
    <FieldInputText
      id="password_reset_request_form_usernameOrEmail"
      :label="Translator.trans('u2.authentication.password_reset.enter_username_or_email')"
      name="usernameOrEmail"
      required
    />
    <FormRow>
      <div class="flex items-center space-x-2">
        <ButtonBasic :disabled="loading" button-style="solid" type="submit">{{
          Translator.trans('u2_core.send')
        }}</ButtonBasic>
        <router-link class="text-gray-800 hover:text-gray-800" :to="{ name: 'AppLogin' }">{{
          Translator.trans('u2.return_to_the_login_page')
        }}</router-link>
      </div>
    </FormRow>
  </form>
</template>
