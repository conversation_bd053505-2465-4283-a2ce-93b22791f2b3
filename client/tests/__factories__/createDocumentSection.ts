import { faker } from '@faker-js/faker/locale/en'
import { userApiBasePath } from '@js/api/userApi'
import type { DocumentSection } from '@js/model/document'

export function createDocumentSection(overrides: Partial<DocumentSection> = {}): DocumentSection {
  const id = overrides.id ?? faker.number.int()
  const sectionTypes: Array<DocumentSection['@type']> = [
    'MasterFileSection',
    'LocalFileSection',
    'CountryByCountryReportSection',
    'DocumentTemplateSection',
  ]
  const sectionType =
    overrides['@type'] ?? sectionTypes[faker.number.int({ min: 0, max: sectionTypes.length - 1 })]

  function getIri() {
    if (overrides['@id']) {
      return overrides['@id']
    }
    switch (sectionType) {
      case 'MasterFileSection':
        return `/api/master-file-sections/${id}`
      case 'LocalFileSection':
        return `/api/local-file-sections/${id}`
      case 'CountryByCountryReportSection':
        return `/api/country-by-country-report-sections/${id}`
      case 'DocumentTemplateSection':
        return `/api/document-template-sections/${id}`
    }
  }

  function getDocument() {
    if (overrides.document) {
      return overrides.document
    }
    switch (sectionType) {
      case 'MasterFileSection':
        return `/api/master-files/${faker.number.int()}`
      case 'LocalFileSection':
        return `/api/local-files/${faker.number.int()}`
      case 'CountryByCountryReportSection':
        return `/api/country-by-country-reports/${faker.number.int()}`
      case 'DocumentTemplateSection':
        return `/api/document-templates/${faker.number.int()}`
    }
  }

  return {
    document: getDocument(),
    displayName: faker.lorem.words(3),
    id,
    name: faker.lorem.words(2),
    content: `<p>${faker.lorem.paragraph(1)}</p>`,
    level: faker.number.int({ min: 1, max: 6 }),
    createdBy: userApiBasePath + `/${faker.number.int()}`,
    updatedBy: userApiBasePath + `/${faker.number.int()}`,
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.past().toISOString(),
    include: true,
    required: false,
    editable: true,
    ...overrides,
    '@type': sectionType,
    '@id': getIri(),
    '@context': `/api/contexts/${sectionType}`,
  }
}
