import { HttpMethods, HttpResponse, http } from 'msw'
import { StatusCodes } from 'http-status-codes'
import { datasheetFieldApi } from '@js/api/datasheetFieldApi'
import { server } from '@tests/mocks/server'

const layoutId = 1
const field = {
  '@id': datasheetFieldApi.buildBasePath(layoutId) + '/1',
  id: 1,
  item: '/api/items/1',
  name: 'name',
  helpText: 'helpText',
  disabled: false,
}

const fieldUpdated = {
  ...field,
  name: 'name',
  item: '/api/items/2',
  helpText: 'helpText',
  disabled: true,
}

const fieldCreated = {
  '@id': datasheetFieldApi.buildBasePath(layoutId) + '/1337',
  id: '1337',
  name: 'name',
  item: '/api/items/2',
  helpText: 'helpText',
  disabled: false,
}

describe('field api', () => {
  it('fetches all fields', async function () {
    server.use(
      http.get(datasheetFieldApi.buildBasePath(layoutId), async () => {
        return HttpResponse.json({ 'hydra:member': [field] }, { status: StatusCodes.OK })
      })
    )
    const response = await datasheetFieldApi.fetchAllDatasheetFields(layoutId)
    expect(response.data).toEqual({ 'hydra:member': [field] })
    expect(response.config.url).toBe(datasheetFieldApi.buildBasePath(layoutId))
    expect(response.config.params).toStrictEqual({ pagination: false })
    expect(response.config.method).toBe(HttpMethods.GET.toLowerCase())
  })

  it('fetches a field by IRI', async function () {
    server.use(
      http.get(field['@id'], async () => {
        return HttpResponse.json(field, { status: StatusCodes.OK })
      })
    )
    const response = await datasheetFieldApi.fetchDatasheetFieldByIri(field['@id'])
    expect(response.data).toEqual(field)
    expect(response.config.url).toEqual(field['@id'])
  })

  it('fetches a field by refId', async function () {
    server.use(
      http.get(datasheetFieldApi.buildBasePath(layoutId) + '/' + field.id, async () => {
        return HttpResponse.json(field, { status: StatusCodes.OK })
      })
    )
    const response = await datasheetFieldApi.fetchDatasheetFieldById(field.id, layoutId)
    expect(response.data).toEqual(field)
    expect(response.config.url).toEqual(datasheetFieldApi.buildBasePath(layoutId) + '/' + field.id)
  })

  it('creates an api', async function () {
    server.use(
      http.post(datasheetFieldApi.buildBasePath(layoutId), async () => {
        return HttpResponse.json(fieldCreated, { status: StatusCodes.OK })
      })
    )
    const response = await datasheetFieldApi.saveDatasheetField(
      {
        name: 'name',
        item: '/api/items/1',
        helpText: 'helpText',
        disabled: false,
      },
      layoutId
    )
    expect(response.data).toEqual(fieldCreated)
    expect(response.config.url).toBe(datasheetFieldApi.buildBasePath(1))
    expect(response.config.method).toBe(HttpMethods.POST.toLowerCase())
  })

  it('updates an api', async function () {
    server.use(
      http.patch(field['@id'], async () => {
        return HttpResponse.json(fieldUpdated, { status: StatusCodes.OK })
      })
    )
    const response = await datasheetFieldApi.saveDatasheetField(fieldUpdated, layoutId)
    expect(response.data).toEqual(fieldUpdated)
    expect(response.config.url).toBe(field['@id'])
    expect(response.config.method).toBe(HttpMethods.PATCH.toLowerCase())
  })
})
