import { createFieldState } from '@tests/__factories__/createFieldState'
import { HttpMethods, HttpResponse, http } from 'msw'
import { StatusCodes } from 'http-status-codes'
import { fieldStateApi } from '@js/api/fieldStateApi'
import { server } from '@tests/mocks/server'

describe('field configuration api', () => {
  it('deletes an existing record', async function () {
    const fieldState = createFieldState()
    server.use(
      http.delete(`${fieldState['@id']}`, async () => {
        return HttpResponse.json(undefined, { status: StatusCodes.NO_CONTENT })
      })
    )
    const response = await fieldStateApi.deleteFieldState(fieldState)
    expect(response.config.url).toBe(`${fieldState['@id']}`)
    expect(response.config.method).toBe(HttpMethods.DELETE.toLowerCase())
    expect(response.data).toBe('')
  })

  it('updates an existing record', async function () {
    const fieldState = createFieldState()
    server.use(
      http.patch(fieldState['@id'], async () => {
        return HttpResponse.json(fieldState, { status: StatusCodes.OK })
      })
    )
    const response = await fieldStateApi.updateFieldState(fieldState)
    expect(response.config.url).toBe(fieldState['@id'])
    expect(response.config.method).toBe(HttpMethods.PATCH.toLowerCase())
    expect(response.data).toEqual(fieldState)
  })

  it('creates a new record', async function () {
    const fieldState = createFieldState()
    server.use(
      http.post('/api/field-states', async () => {
        return HttpResponse.json(fieldState, { status: StatusCodes.CREATED })
      })
    )
    const response = await fieldStateApi.createFieldState(fieldState)
    expect(response.config.url).toBe('/api/field-states')
    expect(response.config.method).toBe(HttpMethods.POST.toLowerCase())
    expect(response.data).toEqual(fieldState)
  })
})
