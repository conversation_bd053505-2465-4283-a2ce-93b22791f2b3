import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { HttpResponse, http } from 'msw'
import { StatusCodes } from 'http-status-codes'
import { fromPartial } from '@total-typescript/shoehorn'
import { render } from '@testing-library/vue'
import { expect } from 'vitest'
import ItemEditor from '@js/components/ItemEditor.vue'
import { setServerHandlers } from '@tests/utils'
import type { LayoutItem } from '@js/model/datasheet'

const item = fromPartial<LayoutItem>({
  '@id': '/api/items/1',
  id: 1,
  refId: 'Name',
  name: 'Name',
  description: 'Description',
  exchangeMethod: 1,
  editable: true,
  formulaReadable: null,
})

setServerHandlers(
  http.get('/api/items/1', async () => {
    return HttpResponse.json(item, { status: StatusCodes.OK })
  })
)

describe('Item editor', () => {
  it('renders', async () => {
    // Given
    const ui = render(ItemEditor, {
      global: {
        plugins: [createTestingPinia()],
      },
    })

    expect(ui.getByRole('combobox', { name: /type/ })).toBeInTheDocument()
    expect(ui.getByRole('combobox', { name: /exchangeMethod/ })).toBeInTheDocument()
    expect(ui.getByRole('textbox', { name: 'u2.name' })).toBeInTheDocument()
    expect(ui.getByRole('textbox', { name: 'u2_core.ref_id' })).toBeInTheDocument()
    expect(ui.getByRole('textbox', { name: 'u2.description' })).toBeInTheDocument()
    expect(ui.getByRole('textbox', { name: /u2.datasheets.formula/ })).toBeInTheDocument()
    expect(ui.getByLabelText('u2.editable').parentElement?.role).toBe('switch')
  })
})
