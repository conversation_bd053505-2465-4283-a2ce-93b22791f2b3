import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { render } from '@testing-library/vue'
import flushPromises from 'flush-promises'
import { fromPartial } from '@total-typescript/shoehorn'
import { mount } from '@vue/test-utils'
import { HttpResponse, http } from 'msw'
import { server } from '@tests/mocks/server'
import { StatusCodes } from 'http-status-codes'
import UserLabel from '@js/components/UserLabel.vue'
import DashboardAssignedUsersAside from '@js/components/dashboard/DashboardAssignedUsersAside.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import { setServerHandlers } from '@tests/utils'
import type { Dashboard } from '@js/model/dashboard'
import type { User } from '@js/model/user'

const dashboard = fromPartial<Dashboard>({
  '@id': '/api/dashboards/1',
  id: '1',
  title: 'dashboard 1',
})

const user1 = fromPartial<User>({
  '@id': '/api/users/1',
  id: 1,
  username: 'user1',
})

const user2 = fromPartial<User>({
  '@id': '/api/users/2',
  id: 2,
  username: 'user2',
})

setServerHandlers(
  http.get('/api/users', async () => {
    return HttpResponse.json({ 'hydra:member': [user1] }, { status: StatusCodes.OK })
  }),
  http.get(user1['@id'], async () => {
    return HttpResponse.json({ ...user1 }, { status: StatusCodes.OK })
  }),
  http.get(user2['@id'], async () => {
    return HttpResponse.json({ ...user2 }, { status: StatusCodes.OK })
  })
)

describe('DashboardAssignedUsersAside', () => {
  it('Renders with non inherited users', async () => {
    // Given
    server.use(
      http.get('/api/dashboards/1/direct-users', async () => {
        return HttpResponse.json({ 'hydra:member': [user1] }, { status: StatusCodes.OK })
      }),
      http.get('/api/dashboards/1/inherited-users', async () => {
        return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
      })
    )
    const wrapper = mount(DashboardAssignedUsersAside, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: { dashboard },
    })

    await flushPromises()

    // Then
    const userLabel = wrapper.findComponent(UserLabel)
    expect(userLabel.exists()).toBe(true)
    expect(userLabel.html()).toContain(user1.username)
    expect(userLabel.props('color')).toContain('white')

    const editButton = wrapper.findComponent(ButtonEdit)
    expect(editButton.exists()).toBe(true)
  })

  it('Renders with edit button disabled', async () => {
    // Given
    server.use(
      http.get('/api/dashboards/1/direct-users', async () => {
        return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
      }),
      http.get('/api/dashboards/1/inherited-users', async () => {
        return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
      })
    )
    const ui = render(DashboardAssignedUsersAside, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: { dashboard, disabled: true },
    })

    await flushPromises()

    // Then
    expect(ui.getByRole('button', { name: 'u2.edit' })).toBeDisabled()
    expect(ui.getByText('u2.no_users')).toBeInTheDocument()
    expect(ui.getByText('u2.dashboard.no_users_assigned_description')).toBeInTheDocument()
    expect(ui.getByRole('button', { name: 'u2.assign_users' })).toBeDisabled()
  })

  it('Renders with inherited users', async () => {
    // Given
    server.use(
      http.get('/api/dashboards/1/direct-users', async () => {
        return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
      }),
      http.get('/api/dashboards/1/inherited-users', async () => {
        return HttpResponse.json({ 'hydra:member': [user2] }, { status: StatusCodes.OK })
      })
    )
    const wrapper = mount(DashboardAssignedUsersAside, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: { dashboard },
    })

    await flushPromises()

    // Then
    const userLabel = wrapper.findComponent(UserLabel)
    expect(userLabel.exists()).toBe(true)
    expect(userLabel.html()).toContain(user2.username)
    expect(userLabel.props('color')).toContain('gray')
  })

  it('Renders without users', async () => {
    server.use(
      http.get('/api/dashboards/1/direct-users', async () => {
        return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
      }),
      http.get('/api/dashboards/1/inherited-users', async () => {
        return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
      })
    )

    // Given
    const ui = render(DashboardAssignedUsersAside, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: { dashboard },
    })

    await flushPromises()

    // Then
    expect(ui.getByText('u2.no_users')).toBeInTheDocument()
    expect(
      ui.getByText('u2.dashboard.no_users_assigned_description', { exact: false })
    ).toBeInTheDocument()
    expect(ui.getByRole('button', { name: 'u2.assign_users' })).toBeInTheDocument()
  })

  it('Renders with public visibility info', async () => {
    // Given
    server.use(
      http.get('/api/dashboards/1/direct-users', async () => {
        return HttpResponse.json({ 'hydra:member': [user1] }, { status: StatusCodes.OK })
      }),
      http.get('/api/dashboards/1/inherited-users', async () => {
        return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
      })
    )
    const ui = render(DashboardAssignedUsersAside, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: { dashboard, showPublicVisibilityInfo: true },
    })

    await flushPromises()

    // Then
    expect(ui.getByText('u2.visibility_user_permissions_warning')).toBeInTheDocument()
    expect(ui.queryByText(user1.username)).toBeInTheDocument()
  })
})
