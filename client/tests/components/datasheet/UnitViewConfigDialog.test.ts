import { periodApiBasePath } from '@js/api/periodApi'
import { unitApiBasePath } from '@js/api/unitApi'
import { createTestingPinia } from '@pinia/testing'
import { render, waitFor } from '@testing-library/vue'
import { createPeriod } from '@tests/__factories__/createPeriod'
import { createUnit } from '@tests/__factories__/createUnit'
import flushPromises from 'flush-promises'
import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import { createRouter, createWebHistory } from 'vue-router'
import { chooseOption, findResourceById, setServerHandlers, wrapInSuspense } from '@tests/utils'
import UnitViewConfigDialog from '@js/components/datasheet/UnitViewConfigDialog.vue'

vi.unmock('vue-router')
const period = createPeriod({ id: 2 })
const unit = createUnit({ id: 1 })

setServerHandlers(
  http.get('/api/periods', async () => {
    return HttpResponse.json(
      { 'hydra:member': [period], 'hydra:totalItems': 1 },
      { status: StatusCodes.OK }
    )
  }),
  http.get(periodApiBasePath + '/:id', ({ params }) =>
    HttpResponse.json(findResourceById(params.id, [period]), {
      status: StatusCodes.OK,
    })
  ),

  http.get(unitApiBasePath, async () => {
    return HttpResponse.json(
      { 'hydra:member': [unit], 'hydra:totalItems': 1 },
      { status: StatusCodes.OK }
    )
  }),

  http.get(unitApiBasePath + '/:id', ({ params }) =>
    HttpResponse.json(findResourceById(params.id, [unit]), {
      status: StatusCodes.OK,
    })
  )
)

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      component: { template: '<div></div>' },
    },
  ],
})
const routerPushSpy = vi.spyOn(router, 'push')

describe('UnitViewConfigDialog', () => {
  const AppDialog = {
    name: 'AppDialog',
    template: '<div><slot name="default" /><slot name="buttons" /></div>',
  }

  beforeEach(() => {
    window.HTMLElement.prototype.scrollIntoView = vi.fn()
  })

  it('submits a form', async () => {
    // Given
    const pinia = createTestingPinia({
      initialState: {
        'user-settings': {
          locale: 'en',
        },
        'datasheet-parameters': {
          parameters: {
            unit: 1,
            period: 2,
          },
        },
      },
    })

    const ui = render(wrapInSuspense(UnitViewConfigDialog), {
      global: {
        plugins: [pinia, router],
        stubs: {
          AppDialog,
        },
      },
    })
    await flushPromises()

    // Then
    await chooseOption(ui, 'period', period.name)
    await chooseOption(ui, 'unit', unit.refId + ' - ' + unit.name)

    // When
    ui.getByRole('button', { name: /u2.change_parameters/ }).click()
    await flushPromises()

    // Then
    await waitFor(() => {
      expect(routerPushSpy).toHaveBeenCalledWith({
        query: {
          unit: 1,
          period: 2,
        },
      })
    })
  })
})
