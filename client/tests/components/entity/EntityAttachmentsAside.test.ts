import { fireEvent, render, within } from '@testing-library/vue'
import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import flushPromises from 'flush-promises'
import { HttpResponse, http } from 'msw'
import { setServerHandlers } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import EntityAttachmentsAside from '@js/components/entity/EntityAttachmentsAside.vue'

const user1 = {
  '@id': '/api/users/1',
  id: 1,
  locked: false,
  lastLogin: null,
  username: 'admin',
  userRoles: [],
  roles: [],
  groups: [],
  contact: {
    id: 1,
    nameLast: 'Biscuits',
    email: '<EMAIL>',
  },
}

const user2 = {
  '@id': '/api/users/2',
  id: 2,
  locked: false,
  lastLogin: null,
  username: 'bubbles',
  userRoles: [],
  roles: [],
  groups: [],
  contact: {
    id: 1,
    nameLast: 'Bubbles',
    email: '<EMAIL>',
  },
}

const attachment1 = {
  '@id': '/tasks/1/attachments/1',
  links: {
    downloadPath: 'download-path',
    fileEditPath: 'file-edit-path',
    unattachPath: 'unattach-path',
  },
  file: '/api/file/1',
  userPermissions: [],
  groupPermissions: [],
  accessType: 'public',
  types: [],
  name: 'File 1.pdf',
  createdAt: '2020-12-10T09:34:25+00:00',
  createdBy: user1['@id'],
}
const attachment2 = {
  ...attachment1,
  '@id': '/tasks/1/attachments/2',
  name: 'File 2.txt',
  file: '/api/file/2',
  createdBy: user2['@id'],
}

setServerHandlers(
  http.get('resource-iri/attachments', async () => {
    return HttpResponse.json(
      {
        'hydra:member': [attachment1, attachment2],
        'hydra:totalItems': 0,
      },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/users', async () => {
    return HttpResponse.json(
      {
        'hydra:member': [user1, user2],
        'hydra:totalItems': 0,
      },
      { status: StatusCodes.OK }
    )
  }),
  http.get(user1['@id'], async () => {
    return HttpResponse.json({ ...user1 }, { status: StatusCodes.OK })
  }),
  http.get(user2['@id'], async () => {
    return HttpResponse.json({ ...user2 }, { status: StatusCodes.OK })
  }),
  http.get('/api/configuration/file-types', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  })
)

describe('EntityAttachmentsAside', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('toggles attachment list info', async () => {
    const ui = render(EntityAttachmentsAside, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: {
        resource: {
          '@id': 'resource-iri',
        },
        disabled: false,
      },
    })

    const list = await ui.findByRole('list')
    expect(within(list).getByRole('button', { name: attachment1.name })).toBeInTheDocument()
    expect(within(list).getByRole('button', { name: attachment2.name })).toBeInTheDocument()
    expect(within(list).getAllByRole('button', { name: 'u2.open_menu' })).toHaveLength(2)
    expect(ui.queryByRole('region')).not.toBeInTheDocument()

    // When
    const expandButton = ui.getByRole('button', { name: 'u2.expand' })
    await fireEvent.click(expandButton)

    await flushPromises()

    // Then
    const attachmentInfos = ui.getAllByRole('region')
    expect(attachmentInfos).toHaveLength(2)
    const attachmentInfo1 = attachmentInfos[0]
    const attachmentInfo2 = attachmentInfos[1]
    expect(within(attachmentInfo1).getByText('10/12/2020, 09:34')).toBeInTheDocument()
    expect(within(attachmentInfo2).getByText('10/12/2020, 09:34')).toBeInTheDocument()
    expect(within(attachmentInfo1).getByText(user1.username)).toBeInTheDocument()
    expect(within(attachmentInfo2).getByText(user2.username)).toBeInTheDocument()
    expect(within(attachmentInfo1).getByText('u2_core.my_permissions:')).toBeInTheDocument()
    expect(within(attachmentInfo2).getByText('u2_core.my_permissions:')).toBeInTheDocument()

    // When
    const collapseButton = ui.getByRole('button', { name: 'u2.collapse' })
    await fireEvent.click(collapseButton)

    // Then
    expect(ui.queryByRole('region')).toBeNull()
  })

  it('collapses all files info when one is open', async () => {
    const ui = render(EntityAttachmentsAside, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: {
        resource: {
          '@id': 'resource-iri',
        },
        disabled: false,
      },
    })
    await flushPromises()

    // When
    await fireEvent.click(ui.getAllByRole('button', { name: 'u2.toggle_attachment_info' })[0])

    // Then
    expect(ui.getByRole('region')).toBeInTheDocument()

    // When
    await fireEvent.click(ui.getByRole('button', { name: 'u2.collapse' }))

    // Then
    expect(ui.queryByRole('region')).toBeNull()
  })
})
