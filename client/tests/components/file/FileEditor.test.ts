import { render, within } from '@testing-library/vue'
import { createFile } from '@tests/__factories__/createFile'
import { createUser } from '@tests/__factories__/createUser'
import { createUserPermission } from '@tests/__factories__/createUserPermission'
import { flushPromises } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import { HttpResponse, http } from 'msw'
import { setServerHandlers } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import { CompositePermissionMasks } from '@js/model/permission'
import FileEditor from '@js/components/file/FileEditor.vue'
import { useAuthStore } from '@js/stores/auth'

const file = createFile()
const user = createUser()

setServerHandlers(
  http.get('/api/users', async () => {
    return HttpResponse.json(
      { 'hydra:member': [user], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/configuration/file-types', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/system-settings', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/user-groups', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  })
)

describe('FileEditor', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders for new', async () => {
    const ui = render(FileEditor, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              auth: {
                user,
              },
            },
          }),
        ],
      },
    })
    useAuthStore().user = user
    await flushPromises()

    expect(ui.getByRole('button', { name: 'u2.select_a_file' })).toBeInTheDocument()
    expect(
      within(ui.getByRole('radiogroup')).getByRole('radio', { name: 'u2.access_type.public' })
    ).not.toBeDisabled()
    expect(
      within(ui.getByRole('radiogroup')).getByRole('radio', {
        name: 'u2_core.file.access_type_smart',
      })
    ).not.toBeDisabled()
    expect(
      within(ui.getByRole('radiogroup')).getByRole('radio', {
        name: 'u2_core.file.access_type_protected',
      })
    ).not.toBeDisabled()

    const comboboxes = ui.getAllByRole('combobox')
    expect(comboboxes[0]).toHaveAttribute('aria-multiselectable', 'true')
    expect(comboboxes[1]).toHaveValue(user.username)
    expect(comboboxes[2]).toHaveValue('Read, Edit, Delete, Manage')

    expect(ui.getByText('u2_core.assign_extra_permissions')).toBeInTheDocument()
    expect(ui.getByText('u2_core.user')).toBeInTheDocument()
    expect(ui.getByText('u2_core.group')).toBeInTheDocument()
    expect(ui.getAllByText('u2_core.permissions')).toHaveLength(2)
    expect(ui.getByLabelText('u2.description')).not.toBeDisabled()
  })

  it('renders for edit', async () => {
    const ui = render(FileEditor, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              auth: {
                user,
              },
            },
          }),
        ],
      },
      props: {
        file: {
          ...file,
          accessType: 'smart',
          userPermissions: [
            createUserPermission({ user: user['@id'], mask: CompositePermissionMasks.MANAGE }),
          ],
        },
      },
    })
    useAuthStore().user = user
    await flushPromises()

    expect(ui.queryByRole('button', { name: 'u2.select_a_file' })).not.toBeInTheDocument()
    expect(
      within(ui.getByRole('radiogroup')).getByRole('radio', { name: 'u2.access_type.public' })
    ).not.toBeDisabled()
    expect(
      within(ui.getByRole('radiogroup')).getByRole('radio', {
        name: 'u2_core.file.access_type_smart',
      })
    ).not.toBeDisabled()
    expect(
      within(ui.getByRole('radiogroup')).getByRole('radio', {
        name: 'u2_core.file.access_type_protected',
      })
    ).not.toBeDisabled()

    const comboboxes = ui.getAllByRole('combobox')
    expect(comboboxes[0]).toHaveAttribute('aria-multiselectable', 'true')
    expect(comboboxes[1]).toHaveValue(user.username)
    expect(comboboxes[2]).toHaveValue('Read, Edit, Delete, Manage')

    expect(ui.getByText('u2_core.assign_extra_permissions')).toBeInTheDocument()
    expect(ui.getByText('u2_core.user')).toBeInTheDocument()
    expect(ui.getByText('u2_core.group')).toBeInTheDocument()
    expect(ui.getAllByText('u2_core.permissions')).toHaveLength(2)
    expect(ui.getByLabelText('u2.description')).not.toBeDisabled()
  })

  it('disables permissions fields and buttons if user does not have enough rights', async () => {
    const ui = render(FileEditor, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              auth: {
                user,
              },
            },
          }),
        ],
      },
      props: {
        file: { ...file, accessType: 'public' },
      },
    })
    await flushPromises()

    expect(
      within(ui.getByRole('radiogroup')).getByRole('radio', { name: 'u2.access_type.public' })
    ).toBeDisabled()
    expect(
      within(ui.getByRole('radiogroup')).getByRole('radio', {
        name: 'u2_core.file.access_type_smart',
      })
    ).toBeDisabled()
    expect(
      within(ui.getByRole('radiogroup')).getByRole('radio', {
        name: 'u2_core.file.access_type_protected',
      })
    ).toBeDisabled()

    expect(ui.getByRole('combobox')).toBeDisabled()

    const buttons = ui.queryAllByRole('button', { name: 'u2_core.add_new_entry' })

    buttons.forEach((button) => {
      expect(button).toBeDisabled()
      expect(button).not.toBeVisible()
    })
  })
})
