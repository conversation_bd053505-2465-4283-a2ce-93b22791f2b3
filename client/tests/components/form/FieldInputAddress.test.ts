import { userEvent } from '@testing-library/user-event'
import { render } from '@testing-library/vue'
import { createAddress } from '@tests/__factories__/createAddress'
import { createCountry } from '@tests/__factories__/createCountry'
import { createHydraCollection, setServerHandlers } from '@tests/utils'
import { flushPromises } from '@vue/test-utils'
import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import { defineComponent } from 'vue'
import FieldInputAddress from '@js/components/form/FieldInputAddress.vue'
import useForm from '@js/composable/useForm'
import type { RenderResult } from '@testing-library/vue'

export default defineComponent({
  components: { FieldInputAddress },
})
const country = createCountry()
setServerHandlers(
  http.get('/api/countries', async () => {
    return HttpResponse.json(createHydraCollection([country]), {
      status: StatusCodes.OK,
    })
  })
)

async function expectEmptyAddressFields(ui: RenderResult) {
  expect(await ui.findByLabelText('u2.address.line_1')).toHaveValue('')
  expect(ui.getByLabelText('u2.address.line_2')).toHaveValue('')
  expect(ui.getByLabelText('u2.address.line_3')).toHaveValue('')
  expect(ui.getByLabelText('u2.address.post_code')).toHaveValue('')
  expect(ui.getByLabelText('u2.address.city')).toHaveValue('')
  expect(ui.getByLabelText('u2.address.state')).toHaveValue('')
  expect(ui.getByLabelText('address.country-combobox')).toHaveValue('')
}

describe('FieldInputAddress', () => {
  it('renders required address', async () => {
    const ui = render(FieldInputAddress, {
      props: {
        label: 'Address label',
        required: true,
        name: 'address',
      },
    })

    expect(ui.getByRole('group', { name: 'Address label' })).toBeInTheDocument()
    await expectEmptyAddressFields(ui)
    expect(ui.queryByRole('button', { name: 'u2.remove_address' })).not.toBeInTheDocument()
  })

  it('renders optional address', async () => {
    const user = userEvent.setup()
    const ui = render(FieldInputAddress, {
      props: {
        label: 'Address label',
        name: 'address',
      },
    })

    await user.click(ui.getByRole('button', { name: 'u2.enter_address' }))
    await expectEmptyAddressFields(ui)
    expect(ui.getByRole('button', { name: 'u2.remove_address' })).toBeInTheDocument()
  })

  it('preserves initial values after removing address', async () => {
    // Given
    const user = userEvent.setup()
    const address = createAddress({ country: country['@id'] })
    const ui = render(
      defineComponent({
        components: { FieldInputAddress },
        setup() {
          useForm({
            initialValues: { address },
          })
        },
        template: '<FieldInputAddress name="address" :label="false"/>',
      })
    )

    await flushPromises()

    // When
    await user.click(ui.getByRole('button', { name: 'u2.remove_address' }))

    // Then
    expect(ui.queryByLabelText('u2.address.line_1')).not.toBeInTheDocument()
    expect(ui.queryByLabelText('u2.address.line_2')).not.toBeInTheDocument()
    expect(ui.queryByLabelText('u2.address.line_3')).not.toBeInTheDocument()
    expect(ui.queryByLabelText('u2.address.post_code')).not.toBeInTheDocument()
    expect(ui.queryByLabelText('u2.address.city')).not.toBeInTheDocument()
    expect(ui.queryByLabelText('u2.address.state')).not.toBeInTheDocument()
    expect(ui.queryByLabelText('address.country-combobox')).not.toBeInTheDocument()

    // When
    await user.click(ui.getByRole('button', { name: 'u2.enter_address' }))

    // Then
    expect(ui.getByLabelText('u2.address.line_1')).toHaveValue(address.line1)
    expect(ui.getByLabelText('u2.address.line_2')).toHaveValue(address.line2)
    expect(ui.getByLabelText('u2.address.line_3')).toHaveValue(address.line3)
    expect(ui.getByLabelText('u2.address.post_code')).toHaveValue(address.postcode)
    expect(ui.getByLabelText('u2.address.city')).toHaveValue(address.city)
    expect(ui.getByLabelText('u2.address.state')).toHaveValue(address.state)
    expect(ui.getByLabelText('address.country-combobox')).toHaveValue(country.nameShort)
  })
})
