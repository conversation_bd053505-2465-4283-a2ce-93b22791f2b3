import { render, waitFor } from '@testing-library/vue'
import { userEvent } from '@testing-library/user-event'
import { createHydraCollection, findResourceById } from '@tests/utils'
import { server } from '@tests/mocks/server'
import { HttpResponse, http } from 'msw'
import { StatusCodes } from 'http-status-codes'
import { unitApiBasePath } from '@js/api/unitApi'
import { createUnit } from '@tests/__factories__/createUnit'
import { computed, defineComponent } from 'vue'
import useForm from '@js/composable/useForm'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'
import FieldUnitSelect from '@js/components/form/FieldUnitSelect.vue'
import { expect } from 'vitest'
import flushPromises from 'flush-promises'

describe('FieldUnitSelect', () => {
  beforeEach(() => {
    window.HTMLElement.prototype.scrollIntoView = vi.fn()
  })

  it('ensures that initial value is not merged twice into the options', async () => {
    const initialSelection = createUnit()
    const unit2 = createUnit()
    const unit3 = createUnit()
    server.use(
      http.get(unitApiBasePath, async () =>
        HttpResponse.json(createHydraCollection([initialSelection, unit2, unit3]), {
          status: StatusCodes.OK,
        })
      ),
      http.get(unitApiBasePath + '/:id', ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [initialSelection]), {
          status: StatusCodes.OK,
        })
      )
    )

    // Given
    const user = userEvent.setup()

    const WrapperComponent = defineComponent({
      components: { FieldUnitSelect },
      setup() {
        useForm({
          validationSchema: toTypedSchema(z.object({ unit: z.string() })),
          initialValues: { unit: initialSelection['@id'] },
        })
        return {}
      },
      template: `<form><FieldUnitSelect required name="unit"/></form>`,
    })

    const ui = render(WrapperComponent)

    await flushPromises()

    const combobox = ui.getByRole(`combobox`) as HTMLInputElement
    await waitFor(() => {
      expect(combobox.value).toBe(`${initialSelection.refId} - ${initialSelection.name}`)
    })

    // When
    await user.click(combobox)

    // Then
    expect(await ui.findByRole('listbox')).toBeInTheDocument()

    const options = ui.getAllByRole('option')
    expect(options).toHaveLength(3)
    expect(ui.getByText(`${initialSelection.refId} - ${initialSelection.name}`)).toBeInTheDocument()
    expect(ui.getByText(`${unit2.refId} - ${unit2.name}`)).toBeInTheDocument()
    expect(ui.getByText(`${unit3.refId} - ${unit3.name}`)).toBeInTheDocument()

    // When
    await user.keyboard('{backspace}') // Empty the selected value

    // Then
    expect(ui.getAllByRole('option')).toHaveLength(3)
  })

  it('ensures that initial value is put into options if found', async () => {
    const initialSelection = createUnit()
    const unit2 = createUnit()
    const unit3 = createUnit()

    server.use(
      http.get(unitApiBasePath, async () => {
        return HttpResponse.json(createHydraCollection([unit2, unit3]), {
          status: StatusCodes.OK,
        }) // Initial selection is not in the list
      }),
      http.get(unitApiBasePath + '/:id', ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [initialSelection]), {
          status: StatusCodes.OK,
        })
      )
    )

    // Given
    const user = userEvent.setup()

    const WrapperComponent = defineComponent({
      components: { FieldUnitSelect },
      setup() {
        useForm({
          validationSchema: toTypedSchema(z.object({ unit: z.string() })),
          initialValues: { unit: initialSelection['@id'] },
        })
        return {}
      },
      template: `<form><FieldUnitSelect required name="unit"/></form>`,
    })

    const ui = render(WrapperComponent)

    await flushPromises()

    const combobox = ui.getByRole(`combobox`) as HTMLInputElement
    await waitFor(() => {
      expect(combobox.value).toBe(`${initialSelection.refId} - ${initialSelection.name}`)
    })

    // When
    await user.click(combobox)

    // Then
    expect(await ui.findByRole('listbox')).toBeInTheDocument()

    const options = ui.getAllByRole('option')
    expect(options).toHaveLength(3)
    expect(ui.getByText(`${initialSelection.refId} - ${initialSelection.name}`)).toBeInTheDocument()
    expect(ui.getByText(`${unit2.refId} - ${unit2.name}`)).toBeInTheDocument()
    expect(ui.getByText(`${unit3.refId} - ${unit3.name}`)).toBeInTheDocument()
  })

  it('ensures that first option is highlighted when options arrived delayed', async () => {
    server.use(
      http.get(unitApiBasePath, async () =>
        HttpResponse.json(createHydraCollection([createUnit({ refId: 'NO', name: 'NO' })]), {
          status: StatusCodes.OK,
        })
      )
    )

    // Given
    const user = userEvent.setup()

    const ui = render(FieldUnitSelect, {
      props: { name: 'unit' },
    })

    await flushPromises()

    // When
    const combobox = ui.getByRole(`combobox`) as HTMLInputElement
    await user.click(combobox)

    // Then
    expect(await ui.findByRole('listbox')).toBeInTheDocument()
    expect(ui.queryAllByRole('option')).toHaveLength(1)

    // Given
    const unit1 = createUnit({ refId: '1', name: 'Option' })
    const unit2 = createUnit({ refId: '2', name: 'Option' })
    server.use(
      http.get(unitApiBasePath, async () =>
        HttpResponse.json(createHydraCollection([unit1, unit2]), {
          status: StatusCodes.OK,
        })
      )
    )

    // When
    await user.type(combobox, 'Option')

    // Then
    await waitFor(() => {
      expect(ui.getAllByRole('option')).toHaveLength(2)
      expect(ui.getByRole('option', { name: /1 - Option/ })).toBeActiveDescendant()
    })

    const optionUnit2 = ui.getByRole('option', { name: /1 - Option/ })
    expect(optionUnit2.attributes.getNamedItem('data-highlighted')).toBeDefined()
  })

  it('ensures that selected option is kept while searching', async () => {
    const initialSelection = createUnit()
    server.use(
      http.get(unitApiBasePath, async () => {
        return HttpResponse.json(createHydraCollection([]), {
          status: StatusCodes.OK,
        })
      }),
      http.get(unitApiBasePath + '/:id', ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [initialSelection]), {
          status: StatusCodes.OK,
        })
      )
    )

    // Given
    const user = userEvent.setup()

    const WrapperComponent = defineComponent({
      components: { FieldUnitSelect },
      setup() {
        const { values } = useForm({
          validationSchema: toTypedSchema(z.object({ unit: z.string() })),
          initialValues: { unit: initialSelection['@id'] },
        })
        return {
          unit: computed(() => values.unit),
        }
      },
      template: `<form><FieldUnitSelect required name="unit"/></form><div id="selectedValue">Unit: {{ unit }}</div>`,
    })

    const ui = render(WrapperComponent)

    await flushPromises()

    const combobox = ui.getByRole(`combobox`) as HTMLInputElement

    expect(ui.getByText('Unit: ' + initialSelection['@id'])).toBeInTheDocument()

    // When
    await user.click(combobox)

    // Then
    expect(await ui.findByRole('listbox')).toBeInTheDocument()

    // Given
    const unit1 = createUnit({ refId: '1', name: 'Option' })
    const unit2 = createUnit({ refId: '2', name: 'Option' })
    server.use(
      http.get(unitApiBasePath, async () =>
        HttpResponse.json(createHydraCollection([unit1, unit2]), {
          status: StatusCodes.OK,
        })
      )
    )

    // When
    await user.type(combobox, 'Option')
    await waitFor(() => {
      expect(ui.getAllByRole('option')).toHaveLength(2)
    })

    // Then
    expect(ui.getByText('Unit: ' + initialSelection['@id'])).toBeInTheDocument()
  })
})
