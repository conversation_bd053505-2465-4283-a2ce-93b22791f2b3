import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import flushPromises from 'flush-promises'
import { HttpResponse, http } from 'msw'
import { createHydraCollection, findResourceById, setServerHandlers } from '@tests/utils'
import { createUser } from '@tests/__factories__/createUser'
import { createTask } from '@tests/__factories__/createTask'
import { fireEvent, render } from '@testing-library/vue'
import { expect } from 'vitest'
import { StatusCodes } from 'http-status-codes'
import { userEvent } from '@testing-library/user-event'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import { useAuthStore } from '@js/stores/auth'
import AssignUserDialog from '@js/components/task/AssignUserDialog.vue'

const currentUser = createUser()
const userOption = createUser()
const userWithoutPermission = createUser()
const task = createTask({ assignee: null })
setServerHandlers(
  http.get('/api/users/:id', ({ params }) => {
    HttpResponse.json(
      findResourceById(params.id, [currentUser, userOption, userWithoutPermission]),
      {
        status: StatusCodes.OK,
      }
    )
  }),
  http.get('/api/tasks/' + task.id + '/permitted-users', () =>
    HttpResponse.json(createHydraCollection([currentUser, userOption]), {
      status: StatusCodes.OK,
    })
  ),
  http.get('/api/users/:id/groups', () =>
    HttpResponse.json(createHydraCollection([]), {
      status: StatusCodes.OK,
    })
  )
)

describe('UserAssignmentDialog', () => {
  beforeEach(() => {
    mockIntersectionObserver()
  })

  it('Renders', async () => {
    // Given
    const ui = render(AssignUserDialog, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: {
        task: task,
      },
    })

    useAuthStore().$patch({ user: currentUser })

    // Given
    expect(await ui.findByLabelText(`assign_to_user_form[assignee]-combobox`)).toBeInTheDocument()

    expect(ui.getByRole('button', { name: 'u2.cancel' })).toBeInTheDocument()
    expect(ui.getByRole('button', { name: /u2.assign/ })).toBeInTheDocument()
    expect(ui.getByRole('button', { name: /u2_core.assign_to_me/ })).toBeInTheDocument()
    expect(ui.getByText('u2.comment.add_comment_field_placeholder')).toBeInTheDocument()
  })

  it('opens a new comment form if the placeholder is focused', async () => {
    // Given
    const ui = render(AssignUserDialog, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: {
        task: task,
      },
    })

    useAuthStore().$patch({ user: currentUser })

    // Given
    await flushPromises()

    // When
    const addCommentPlaceholder = ui.getByTestId('new-comment-trigger')
    await fireEvent.focus(addCommentPlaceholder)

    // Then
    expect(ui.queryByTestId('new-comment-trigger')).not.toBeInTheDocument()

    expect(ui.getByLabelText(`assign_to_user_form[userGroup]-combobox`)).toBeInTheDocument()

    expect(ui.getByRole('textbox', { name: 'assign_to_user_form[comment]' })).toBeInTheDocument()
  })

  it('assigns current user if button is clicked', async () => {
    const user = userEvent.setup()
    // Given
    const ui = render(AssignUserDialog, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: {
        task: task,
      },
    })

    useAuthStore().$patch({ user: currentUser })

    // Given
    await flushPromises()

    // When
    const addCommentPlaceholder = ui.getByTestId('new-comment-trigger')
    await user.click(addCommentPlaceholder)

    // Then
    expect(ui.queryByTestId('new-comment-trigger')).not.toBeInTheDocument()

    expect(ui.getByLabelText(`assign_to_user_form[userGroup]-combobox`)).toBeInTheDocument()

    expect(ui.getByRole('textbox', { name: 'assign_to_user_form[comment]' })).toBeInTheDocument()
  })
})
