import { commentApiBasePath } from '@js/api/commentApi'
import { userApiBasePath } from '@js/api/userApi'
import { userGroupBasePath } from '@js/api/userGroupApi'
import { createTestingPinia } from '@pinia/testing'
import { render, waitFor, within } from '@testing-library/vue'
import { createComment } from '@tests/__factories__/createComment'
import { createUserGroup } from '@tests/__factories__/createUserGroup'
import { findResourceById, setServerHandlers } from '@tests/utils'
import flushPromises from 'flush-promises'
import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import { createUser } from '@tests/__factories__/createUser'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import { fetchStates } from '@js/types'
import TaskComments from '@js/components/task/TaskComments.vue'
import { server } from '@tests/mocks/server'

describe('TaskComments', () => {
  const task = {
    '@id': 'task-id-iri',
    id: 3,
  }

  const user = createUser()
  const userGroup = createUserGroup()

  const comment = createComment({
    id: 1,
    content: 'Comment 1',
    deleted: false,
    author: user['@id'],
    group: userGroup['@id'],
    quote: undefined,
    task: task['@id'],
    'u2:extra': {
      canWrite: true,
      canDelete: true,
    },
  })

  const comment2 = createComment({
    id: 2,
    content: 'Comment 2',
    deleted: false,
    author: user['@id'],
    group: userGroup['@id'],
    quote: undefined,
    task: task['@id'],
  })

  setServerHandlers(
    http.get('/api/tasks/3/comments', async () => {
      return Response.json(
        {
          'hydra:member': [comment, comment2],
        },
        { status: StatusCodes.NO_CONTENT }
      )
    }),
    http.get(userApiBasePath + '/:id', ({ params }) =>
      HttpResponse.json(findResourceById(params.id, [user]), { status: StatusCodes.OK })
    ),
    http.get(userGroupBasePath + '/:id', ({ params }) =>
      HttpResponse.json(findResourceById(params.id, [userGroup]), { status: StatusCodes.OK })
    ),
    http.delete(commentApiBasePath + '/:id', async ({ params }) => {
      if (!findResourceById(params.id, [comment])) throw new Error('Comment not found')

      return new Response(null, { status: StatusCodes.NO_CONTENT })
    })
  )

  beforeEach(() => {
    mockIntersectionObserver()
  })

  it('renders', async () => {
    const ui = render(TaskComments, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              task: { task },
              comments: {
                commentsFetchState: fetchStates.resolved,
                comments: [comment, comment2],
              },
            },
            stubActions: false,
          }),
        ],
      },
    })

    await flushPromises()

    expect(ui.getByText('Comment 1')).toBeInTheDocument()
    expect(ui.getByText('Comment 2')).toBeInTheDocument()
    expect(ui.getByRole('button', { name: 'u2_comment.new_comment' })).toBeInTheDocument()
    expect(ui.getByRole('heading', { name: /u2_core.comments \(2\)/ })).toBeInTheDocument()
    expect(ui.getByText('u2.comment.add_comment_field_placeholder')).toBeInTheDocument()
  })

  it('set the edit state of a comment when an edit button is clicked', async () => {
    // Given
    const ui = render(TaskComments, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              task: { task },
              comments: {
                commentsFetchState: fetchStates.resolved,
                comments: [comment],
              },
            },
            stubActions: false,
          }),
        ],
      },
    })

    await flushPromises()

    // When
    ui.getByRole('button', { name: 'u2_comment.edit_comment' }).click()

    // Then
    expect(await ui.findByLabelText('u2_comment.comment')).toHaveValue('Comment 1')
  })

  it('cancels the edit state of a comment when a cancel button is clicked on an edited comment', async () => {
    // Given
    const ui = render(TaskComments, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              task: { task },
              comments: {
                commentsFetchState: fetchStates.resolved,
                comments: [comment],
                editedComments: [1],
              },
            },
            stubActions: false,
          }),
        ],
      },
    })

    // Then
    expect(ui.getByLabelText('u2_comment.comment')).toHaveValue('Comment 1')

    // When
    await ui.getByRole('button', { name: 'u2.cancel' }).click()

    // Then
    expect(ui.queryByLabelText('u2_comment.comment')).not.toBeInTheDocument()
  })

  it('deletes a comment when delete is clicked in the confirmation dialog', async () => {
    const ui = render(TaskComments, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              task: { task },
              comments: {
                commentsFetchState: fetchStates.resolved,
                comments: [comment, comment2],
                currentTaskId: 3,
              },
            },
            stubActions: false,
          }),
        ],
      },
    })

    await flushPromises()

    const comment1 = ui.getByText('Comment 1')
    expect(comment1).toBeInTheDocument()
    expect(ui.getByText('Comment 2')).toBeInTheDocument()
    expect(ui.getByRole('heading', { name: /u2_core.comments \(2\)/ })).toBeInTheDocument()

    server.use(
      http.get('/api/tasks/3/comments', async () => {
        return Response.json({ 'hydra:member': [comment2] }, { status: StatusCodes.OK })
      })
    )

    // When
    within(comment1.closest('article') as HTMLElement)
      .getByRole('button', { name: 'u2_comment.delete_comment' })
      .click()

    // Then
    expect(await ui.findByText('u2_comment.delete_comment.confirmation')).toBeInTheDocument()
    ui.getByRole('button', { name: 'u2.delete' }).click()

    await waitFor(() => {
      expect(ui.queryByText('Comment 1')).toBeNull()
    })

    expect(await ui.findByText(/u2_core.comments \(1\)/)).toBeInTheDocument()
  })

  it('saves a comment', async () => {
    // Given
    const editedComment = createComment({
      id: 2,
      content: 'Comment 2',
      deleted: false,
      group: userGroup['@id'],
      quote: undefined,
      author: user['@id'],
      'u2:extra': {
        canWrite: true,
        canDelete: true,
      },
    })
    const ui = render(TaskComments, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              task: { task },
              comments: {
                commentsFetchState: fetchStates.resolved,
                comments: [comment, editedComment],
                editedComments: [2],
                currentTaskId: task.id,
              },
            },
            stubActions: false,
          }),
        ],
      },
    })

    await flushPromises()

    const updatedComment = { ...editedComment, content: 'Comment edited' }
    server.use(
      http.patch('/api/comments/2', async () => {
        return HttpResponse.json(updatedComment, { status: StatusCodes.OK })
      })
    )

    // When
    ui.getByRole('button', { name: /u2.save/ }).click()

    // Then
    expect(await ui.findByText('Comment edited')).toBeInTheDocument()
  })
})
