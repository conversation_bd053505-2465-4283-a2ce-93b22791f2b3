import { createP<PERSON>, setActive<PERSON>inia } from 'pinia'
import flushPromises from 'flush-promises'
import { mount } from '@vue/test-utils'
import { HttpResponse, http } from 'msw'
import { StatusCodes } from 'http-status-codes'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import BulkTransitionDialog from '@js/components/task/list/BulkTransitionDialog.vue'
import AppDialog from '@js/components/AppDialog.vue'
import TwigComponent from '@js/components/form/TwigComponent.vue'
import { server } from '@tests/mocks/server'

describe('Bulk Transition Dialog', () => {
  beforeEach(() => {
    mockIntersectionObserver()
    setActivePinia(createPinia())
  })

  it('renders', async () => {
    // When
    server.use(
      http.get('/legacy/bulk-transition/tam-tax-audit-risk', async () => {
        return HttpResponse.json(
          '<form action="/legacy/bulk-transition/tam-tax-audit-risk"><input type="radio" checked></form>',
          { status: StatusCodes.OK }
        )
      })
    )
    const component = mount(BulkTransitionDialog, {
      props: {
        shortName: 'tam-tax-audit-risk',
      },
    })

    // Then
    await flushPromises()

    expect(component.findComponent(TwigComponent).exists()).toBe(true)

    const basicButtons = component.findAllComponents(ButtonBasic)

    expect(basicButtons.filter((button) => button.text() === 'u2.cancel')).toHaveLength(1)

    expect(basicButtons.filter((button) => button.text() === 'u2.transition')).toHaveLength(1)
  })

  it('emits "close" event', async () => {
    // Given
    server.use(
      http.get('/legacy/bulk-transition/tam-tax-audit-risk', async () => {
        return HttpResponse.json(
          '<form action="/legacy/bulk-transition/tam-tax-audit-risk"><input type="radio" checked></form>',
          { status: StatusCodes.OK }
        )
      })
    )

    const component = mount(BulkTransitionDialog, {
      props: {
        shortName: 'tam-tax-audit-risk',
      },
    })
    const dialog = component.findComponent(AppDialog)

    // When
    dialog.vm.$emit('close')

    // Then
    expect(component.emitted()).toHaveProperty('close')
  })

  it('emits "close" event when cancel is clicked', async () => {
    // Given
    server.use(
      http.get('/legacy/bulk-transition/tam-tax-audit-risk', async () => {
        return HttpResponse.json(
          '<form action="/legacy/bulk-transition/tam-tax-audit-risk"><input type="radio" checked></form>',
          { status: StatusCodes.OK }
        )
      })
    )

    const component = mount(BulkTransitionDialog, {
      props: {
        shortName: 'tam-tax-audit-risk',
      },
    })

    await flushPromises()

    // When
    const cancelButton = component.findAllComponents(ButtonBasic)[0]
    await cancelButton.trigger('click')

    // Then
    expect(component.emitted()).toHaveProperty('close')
  })

  it('saves the bulk transition form', async () => {
    // Given
    server.use(
      http.get('/legacy/bulk-transition/tam-tax-audit-risk', async () => {
        return HttpResponse.json(
          '<form action="/legacy/bulk-transition/tam-tax-audit-risk"><input type="radio" checked></form>',
          { status: StatusCodes.OK }
        )
      }),

      http.post('/legacy/bulk-transition/tam-tax-audit-risk', async () => {
        return HttpResponse.json({}, { status: StatusCodes.OK })
      })
    )

    const component = mount(BulkTransitionDialog, {
      attachTo: document.body,
      props: {
        shortName: 'tam-tax-audit-risk',
      },
    })

    // When
    await flushPromises()

    const saveButton = component.findAllComponents(ButtonBasic)[1]
    await saveButton.trigger('click')

    await flushPromises()

    // Then
    expect(component.emitted()).toHaveProperty('close')
    expect(component.emitted()).toHaveProperty('saved')
  })
})
