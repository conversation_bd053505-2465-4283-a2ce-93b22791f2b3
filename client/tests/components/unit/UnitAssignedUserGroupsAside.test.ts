import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { render } from '@testing-library/vue'
import flushPromises from 'flush-promises'
import { fromPartial } from '@total-typescript/shoehorn'
import { mount } from '@vue/test-utils'
import { HttpResponse, http } from 'msw'
import { setServerHandlers } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import UnitAssignedUserGroupsAside from '@js/components/unit/UnitAssignedUserGroupsAside.vue'
import LabelBasic from '@js/components/LabelBasic.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import { server } from '@tests/mocks/server'
import type { UserGroup } from '@js/model/userGroup'
import type { Unit } from '@js/model/unit'

const unit = fromPartial<Unit>({
  '@id': '/api/units/1',
  id: 1,
  name: 'unit 1',
})

const userGroup1 = fromPartial<UserGroup>({
  '@id': '/api/groups/1',
  id: 1,
  name: 'userGroup 1',
})

setServerHandlers(
  http.get('/api/user-groups', async () => {
    return HttpResponse.json({ 'hydra:member': [userGroup1] }, { status: StatusCodes.OK })
  })
)

describe('UnitAssignedUserGroupsAside', () => {
  it('Renders with user groups', async () => {
    // Given
    server.use(
      http.get('/api/units/1/groups', async () => {
        return HttpResponse.json({ 'hydra:member': [userGroup1] }, { status: StatusCodes.OK })
      })
    )
    const wrapper = mount(UnitAssignedUserGroupsAside, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: { unit },
    })

    await flushPromises()

    // Then
    const labelBasic = wrapper.findComponent(LabelBasic)
    expect(labelBasic.exists()).toBe(true)
    expect(labelBasic.html()).toContain(userGroup1.name)
    const editButton = wrapper.findComponent(ButtonEdit)
    expect(editButton.exists()).toBe(true)
  })

  it('Renders with edit button disabled', async () => {
    // Given
    server.use(
      http.get('/api/units/1/groups', async () => {
        return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
      })
    )
    const ui = render(UnitAssignedUserGroupsAside, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: { unit, disabled: true },
    })

    await flushPromises()

    // Then
    expect(ui.getByRole('button', { name: 'u2.edit' })).toBeDisabled()
    expect(ui.getByText('u2.no_user_groups')).toBeInTheDocument()
    expect(
      ui.getByText('u2.unit.no_user_groups_assigned_description', {
        exact: false,
      })
    ).toBeInTheDocument()
    expect(ui.getByText('u2.contact_admin', { exact: false })).toBeInTheDocument()
    expect(ui.queryByRole('button', { name: 'u2.assign_user_groups' })).not.toBeInTheDocument()
  })

  it('Renders without users', async () => {
    server.use(
      http.get('/api/units/1/groups', async () => {
        return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
      })
    )

    // Given
    const ui = render(UnitAssignedUserGroupsAside, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: { unit },
    })

    await flushPromises()

    // Then
    expect(ui.getByText('u2.no_user_groups')).toBeInTheDocument()
    expect(
      ui.getByText('u2.unit.no_user_groups_assigned_description', {
        exact: false,
      })
    ).toBeInTheDocument()
    expect(
      ui.getByText('u2.unit.no_user_groups_assigned_admin', { exact: false })
    ).toBeInTheDocument()
    expect(ui.getByRole('button', { name: 'u2.assign_user_groups' })).toBeInTheDocument()
  })
})
