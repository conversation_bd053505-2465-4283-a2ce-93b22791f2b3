import axios from 'axios'
import { createTestingPinia } from '@pinia/testing'
import flushPromises from 'flush-promises'
import { fromPartial } from '@total-typescript/shoehorn'
import { mount } from '@vue/test-utils'
import UserLabel from '@js/components/UserLabel.vue'
import AppDateTime from '@js/components/AppDateTime.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import UnitInformationAside from '@js/components/unit/UnitInformationAside.vue'
import type { LegalUnit } from '@js/model/unit'

describe('UnitInformationAside', () => {
  it('renders', async () => {
    axios.get = vi.fn()
    const component = mount(UnitInformationAside, {
      props: {
        unit: fromPartial<LegalUnit>({
          '@type': 'LegalUnit',
          createdAt: '2021-10-22T16:14:48+00:00',
          createdBy: '/api/users/1',
          updatedAt: '2021-10-22T18:14:48+00:00',
          updatedBy: '/api/users/2',
        }),
      },
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          AsideSection: { template: '<div><slot /></div>' },
          InformationAsideSection: { template: '<div><slot /></div>' },
          InformationGridRow: { template: '<div><slot /></div>' },
        },
      },
    })

    expect(component.findComponent(SvgIcon).props('icon')).toBe('legal-unit')

    const dateTimeComponents = component.findAllComponents(AppDateTime)

    expect(dateTimeComponents[0].props('date')).toBe('2021-10-22T16:14:48+00:00')
    expect(dateTimeComponents[1].props('date')).toBe('2021-10-22T18:14:48+00:00')

    await flushPromises()

    const userLabels = component.findAllComponents(UserLabel)
    expect(userLabels[0].props('user')).toBe(1)
    expect(userLabels[1].props('user')).toBe(2)
  })
})
