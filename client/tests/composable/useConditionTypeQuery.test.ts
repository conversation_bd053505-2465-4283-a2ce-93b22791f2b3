import { defineComponent, ref } from 'vue'
import flushPromises from 'flush-promises'
import { mount } from '@vue/test-utils'
import { HttpResponse, http } from 'msw'
import { StatusCodes } from 'http-status-codes'
import useConditionTypeQuery from '@js/composable/useConditionTypeQuery'
import { setServerHandlers } from '@tests/utils'

describe('useConditionTypeQuery', () => {
  const conditionType = {
    '@id': '/api/workflow/transition/condition-types/id',
    '@type': 'ConditionType',
    id: 'id',
    description: 'description',
    help: 'help',
    name: 'name',
  }

  function createTestComponent() {
    return defineComponent({
      setup() {
        const { data, isLoading } = useConditionTypeQuery(ref('id'))
        return {
          data,
          isLoading,
        }
      },
      template: '<div/>',
    })
  }

  setServerHandlers(
    http.get(conditionType['@id'], async () => {
      return HttpResponse.json({ ...conditionType }, { status: StatusCodes.OK })
    })
  )

  it('gets a user by iri', async () => {
    // Given
    const component = mount(createTestComponent(), {})

    // Then
    expect(component.vm.data).toBeUndefined()
    expect(component.vm.isLoading).toBe(true)

    // When
    await flushPromises()

    // Then
    expect(component.vm.isLoading).toBe(false)
    expect(component.vm.data).toEqual(conditionType)
  })
})
