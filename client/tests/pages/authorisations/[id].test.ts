import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import flushPromises from 'flush-promises'
import { mount } from '@vue/test-utils'
import { HttpResponse, http } from 'msw'
import { setServerHandlers, wrapInSuspense } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import { userEvent } from '@testing-library/user-event'
import { render, within } from '@testing-library/vue'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import { useNotificationsStore } from '@js/stores/notifications'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import AuthorizationEditor from '@js/components/user/AuthorizationEditor.vue'
import AuthorizationEdit from '@js/pages/authorisations/[id].vue'

setServerHandlers(
  http.get('/api/users', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/user-groups', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.delete('/api/authorizations/1', async () => {
    return HttpResponse.json({}, { status: StatusCodes.OK })
  }),
  http.get('/api/authorizations/1', async () => {
    return HttpResponse.json(
      {
        id: 1,
        '@id': 'resource-iri',
        'hydra:member': [],
        'hydra:totalItems': 0,
      },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/authorization-items', async () => {
    return HttpResponse.json({}, { status: StatusCodes.OK })
  }),
  http.get('/api/authorizations/1/direct-users', async () => {
    return HttpResponse.json(
      {
        'hydra:member': [{ username: 'Username' }],
        'hydra:totalItems': 1,
      },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/authorizations/1/indirect-users', async () => {
    return HttpResponse.json(
      {
        'hydra:member': [{ username: 'Another user' }],
        'hydra:totalItems': 1,
      },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/authorizations/1/inherited-users', async () => {
    return HttpResponse.json(
      {
        'hydra:member': [{ username: 'Another user' }],
        'hydra:totalItems': 1,
      },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/authorizations/1/groups', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  })
)

describe('AuthorizationEdit Page', () => {
  beforeEach(() => {
    mockIntersectionObserver()
  })

  it('renders', async function () {
    const page = mount(wrapInSuspense(AuthorizationEdit, { id: 1 }), {
      global: {
        plugins: [createTestingPinia()],
      },
    })

    await flushPromises()

    expect(page.findComponent(AuthorizationEditor).exists()).toBe(true)
    expect(page.findComponent(PageHeader).exists()).toBe(true)
  })

  it('deletes an authorization profile', async () => {
    const user = userEvent.setup()
    const ui = render(wrapInSuspense(AuthorizationEdit, { id: 1 }), {
      global: {
        plugins: [createTestingPinia()],
      },
    })

    await flushPromises()

    const meatball = await ui.findByLabelText('u2.open_menu')
    await user.click(meatball)

    await user.click(ui.getByText(/u2.delete/))

    await user.click(within(ui.getByRole('dialog')).getByText(/u2.delete/))

    await flushPromises()
    // Then
    const notificationsStore = useNotificationsStore()
    expect(notificationsStore.addSuccess).toHaveBeenCalledWith('u2.success_removed')
  })
})
