import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { mount } from '@vue/test-utils'
import { HttpResponse, http } from 'msw'
import { setServerHandlers } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import AuditorNew from '@js/pages/configuration/auditors/new.vue'

setServerHandlers(
  http.get('/api/countries', async () => {
    return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
  })
)

describe('AuditorNew Page', () => {
  it('renders', function () {
    const page = mount(AuditorNew, {
      global: {
        plugins: [createTestingPinia()],
      },
    })

    expect(page.findComponent(PageHeader).exists()).toBe(true)
  })
})
