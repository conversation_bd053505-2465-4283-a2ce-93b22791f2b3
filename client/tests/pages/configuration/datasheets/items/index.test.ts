import { useRoute } from 'vue-router'
import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { fromPartial } from '@total-typescript/shoehorn'
import { HttpResponse, http } from 'msw'
import { findResourceById, setServerHandlers } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import flushPromises from 'flush-promises'
import { render, within } from '@testing-library/vue'
import { createItem } from '@tests/__factories__/createItem'
import { expect } from 'vitest'
import { userEvent } from '@testing-library/user-event'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import TaxAccountingItemList from '@js/pages/configuration/datasheets/items/index.vue'
import { itemApi } from '@js/api/itemApi'
import { useNotificationsStore } from '@js/stores/notifications'
import { server } from '@tests/mocks/server'
import type { RouteLocationNormalizedLoaded } from 'vue-router'

const item = createItem({ type: 'percent', exchangeMethod: 2 })

setServerHandlers(
  http.get('/api/items', async () => {
    return HttpResponse.json(
      { 'hydra:member': [item] },
      {
        status: StatusCodes.OK,
      }
    )
  })
)

describe('TaxAccountingItemList Page', () => {
  beforeEach(() => {
    mockIntersectionObserver()
  })
  beforeAll(() => {
    vi.mocked(useRoute).mockImplementation(() =>
      fromPartial<RouteLocationNormalizedLoaded>({ query: {} })
    )
  })

  it('renders', async function () {
    const ui = render(TaxAccountingItemList, {
      global: {
        plugins: [createTestingPinia()],
      },
    })
    await flushPromises()

    const pageHeader = ui.getByRole('heading', { name: 'u2.datasheets.item.plural' })
    expect(pageHeader).toBeInTheDocument()

    // Check all headers are there
    expect(ui.getByRole('columnheader', { name: 'u2_core.ref_id' })).toBeInTheDocument()
    expect(ui.getByRole('columnheader', { name: 'u2.name' })).toBeInTheDocument()
    expect(
      ui.getByRole('columnheader', { name: 'u2.datasheets.exchange_method' })
    ).toBeInTheDocument()
    expect(ui.getByRole('columnheader', { name: 'u2.editable' })).toBeInTheDocument()
    expect(ui.getByRole('columnheader', { name: 'u2.datasheets.type' })).toBeInTheDocument()
    expect(ui.getByRole('columnheader', { name: 'u2.datasheets.formula' })).toBeInTheDocument()

    // Check the item values in the cells
    expect(ui.getByRole('cell', { name: item.refId })).toBeInTheDocument()
    expect(ui.getByRole('cell', { name: item.name ?? '' })).toBeInTheDocument()
    expect(
      ui.getByRole('cell', { name: 'u2.datasheets.exchange_method.current' })
    ).toBeInTheDocument()
    expect(ui.getByRole('cell', { name: item.editable ? 'yes-ok' : 'no' })).toBeInTheDocument()
    expect(ui.getByRole('cell', { name: 'u2.datasheets.item.types.percent' })).toBeInTheDocument()
    expect(ui.getByRole('cell', { name: item.formulaReadable ?? '' })).toBeInTheDocument()
  })

  it('allows deleting of an item', async function () {
    const user = userEvent.setup()
    server.use(
      http.delete(itemApi.basePath + '/:id', async ({ params }) => {
        HttpResponse.json(findResourceById(params.id, [item]), { status: StatusCodes.OK })

        return HttpResponse.json(undefined, { status: StatusCodes.NO_CONTENT })
      })
    )

    const ui = render(TaxAccountingItemList, {
      global: {
        plugins: [createTestingPinia()],
      },
    })
    await flushPromises()

    await user.click(ui.getByText(/u2.delete/))

    await user.click(within(ui.getByRole('dialog')).getByText(/u2.delete/))

    await flushPromises()
    // Then
    const notificationsStore = useNotificationsStore()
    expect(notificationsStore.addSuccess).toHaveBeenCalledWith('u2.success_removed')
  })
})
