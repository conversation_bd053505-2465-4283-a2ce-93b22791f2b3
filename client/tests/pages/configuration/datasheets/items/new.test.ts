import flushPromises from 'flush-promises'
import { mount } from '@vue/test-utils'
import { createTestingP<PERSON> } from '@pinia/testing'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import ItemNew from '@js/pages/configuration/datasheets/items/new.vue'
import ItemEditor from '@js/components/ItemEditor.vue'

describe('ItemNew Page', () => {
  it('renders', async function () {
    const page = mount(ItemNew, {
      global: {
        plugins: [createTestingPinia()],
      },
    })

    await flushPromises()

    expect(page.findComponent(PageHeader).exists()).toBe(true)
    expect(page.findComponent(ItemEditor).exists()).toBe(true)
  })
})
