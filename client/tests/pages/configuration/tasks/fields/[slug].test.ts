import { useRoute } from 'vue-router'
import { createTestingP<PERSON> } from '@pinia/testing'
import { fromPartial } from '@total-typescript/shoehorn'
import { mount } from '@vue/test-utils'
import { HttpResponse, http } from 'msw'
import { setServerHandlers, wrapInSuspense } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import flushPromises from 'flush-promises'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import ChoiceList from '@js/pages/configuration/tasks/fields/[slug].vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import AppTable from '@js/components/table/AppTable.vue'
import AppSearch from '@js/components/form/AppSearch.vue'
import type { RouteLocationNormalizedLoaded } from 'vue-router'

vi.mock('@js/router/choice-list', () => {
  return {
    default: [],
    getCurrentChoice: vi.fn(() => ({
      title: () => 'page title',
    })),
  }
})

setServerHandlers(
  http.get('/api/choices', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  })
)

describe('ChoiceList Page', () => {
  beforeAll(() => {
    vi.mocked(useRoute).mockImplementation(() =>
      fromPartial<RouteLocationNormalizedLoaded>({ query: {} })
    )
  })

  it('renders', async function () {
    const page = await mount(
      wrapInSuspense(ChoiceList, {
        resourceCollectionEndpoint: '/api/choices',
        fieldId: 'fieldId',
        readableName: 'readableName',
      }),
      {
        global: {
          plugins: [createTestingPinia()],
        },
      }
    )
    await flushPromises()

    expect(page.findComponent(PageHeader).exists()).toBe(true)
    // @ts-expect-error: See https://github.com/vuejs/test-utils/pull/2242.
    expect(page.findComponent(AppTable).exists()).toBe(true)
    expect(page.findComponent(AppSearch).exists()).toBe(true)
    expect(page.findComponent(ButtonNew).exists()).toBe(true)
  })
})
