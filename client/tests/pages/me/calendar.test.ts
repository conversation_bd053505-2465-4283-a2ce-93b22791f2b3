import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { useRoute } from 'vue-router'
import axios from 'axios'
import flushPromises from 'flush-promises'
import { fromPartial } from '@total-typescript/shoehorn'
import { mount } from '@vue/test-utils'
import { wrapInSuspense } from '@tests/utils'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import CalendarOverview from '@js/pages/me/calendar.vue'
import NewCalendarEntryDialog from '@js/components/calendar/NewCalendarEntryDialog.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import type { RouteLocationNormalizedLoaded } from 'vue-router'

vi.mock('axios')

describe('Calender overview Page', () => {
  beforeEach(() => {
    mockIntersectionObserver()
  })

  beforeAll(() => {
    vi.mocked(useRoute).mockImplementation(() =>
      fromPartial<RouteLocationNormalizedLoaded>({ query: {} })
    )
    vi.mocked(axios.get).mockResolvedValue({
      data: {
        entries: [],
        title: 'Von - B<PERSON>',
        previousCalendarWeekDate: '2020-02-02',
        currentCalendarWeekDate: '2020-02-02',
        nextCalendarWeekDate: '2020-02-02',
        canCreateNewCalendarEntry: true,
      },
    })
  })

  it('renders', async function () {
    const page = await mount(wrapInSuspense(CalendarOverview), {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          AppSidebar: true,
        },
      },
      props: {
        calendars: [],
        entries: {},
        title: 'title',
        extra: {
          shortName: 'short-name',
          isFormDisabled: true,
        },
      },
    })
    await flushPromises()

    expect(page.findComponent(PageHeader).exists()).toBe(true)
  })

  it('opens a new entry dialog', async function () {
    const page = await mount(wrapInSuspense(CalendarOverview), {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          AppSidebar: true,
        },
      },
    })

    await flushPromises()

    expect(page.findComponent(NewCalendarEntryDialog).exists()).toBe(false)
    await page.find('#button-calendar-entry-new').trigger('click')

    const dialog = page.findComponent(NewCalendarEntryDialog)
    expect(dialog.exists()).toBe(true)

    await dialog.vm.$emit('close')
    expect(page.findComponent(NewCalendarEntryDialog).exists()).toBe(false)
  })
})
