import { fireEvent, render, within } from '@testing-library/vue'
import { useRoute } from 'vue-router'
import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import flushPromises from 'flush-promises'
import { fromPartial } from '@total-typescript/shoehorn'
import { expect } from 'vitest'
import { setServerHandlers, wrapInSuspense } from '@tests/utils'
import { HttpResponse, http } from 'msw'
import { StatusCodes } from 'http-status-codes'
import invariant from 'tiny-invariant'
import { userEvent } from '@testing-library/user-event'
import { createUser } from '@tests/__factories__/createUser'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import TaskList from '@js/pages/tasks/TaskList.vue'
import { useAuthStore } from '@js/stores/auth'
import { useNotificationsStore } from '@js/stores/notifications'
import { useTaskListStore } from '@js/stores/task-list'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
import type { TaskListInfo } from '@js/model/task'

setServerHandlers(
  http.delete('/api/bulk/tam-transfer-pricing/delete', async () => {
    return HttpResponse.json({}, { status: StatusCodes.OK })
  }),
  http.get('/legacy/bulk-transition/tam-transfer-pricing', async () => {
    return HttpResponse.json('', { status: StatusCodes.OK })
  }),
  http.get('/api/tasktype/tam-transfer-pricing/table', async () => {
    return HttpResponse.json(
      {
        data: {
          data: {
            name: 'u2_transfer_pricing_transaction_table',
            state: {
              sor: [['SubType', 'ASC']],
              pag: [2, 10],
              sel: ['41'],
              uql: '',
              clmns: [
                'Id',
                'Status',
                'Period',
                'Name',
                'Type',
                'SubType',
                'TransactionAmount',
                'Currency',
                'UnitRefId',
                'UnitName',
                'TransactionUnitRequiresDocumentation',
                'PartnerUnitRefId',
                'PartnerUnitName',
                'TransactionPartnerUnitRequiresDocumentation',
                'DueDate',
                'StatusType',
                'ReviewCount',
                'Files',
                'SourceId',
              ],
              filter: null,
            },
            fields: [
              {
                uniqueName: 'Id',
                readableName: 'Id',
                description: '',
                type: {
                  default: 'NUMERIC_EQ',
                  available: [
                    'NUMERIC_GT',
                    'NUMERIC_GTE',
                    'NUMERIC_EQ',
                    'NUMERIC_LTE',
                    'NUMERIC_LT',
                    'NUMERIC_NEQ',
                    'IN',
                    'NIN',
                  ],
                  name: 'number',
                },
                metadata: {
                  filterable: true,
                  required: true,
                  selectedByDefault: true,
                  sortable: true,
                  hidden: false,
                  name: 'u2_core.id',
                },
                choices: [],
              },
              {
                uniqueName: 'Status',
                readableName: 'Status',
                description: '',
                type: {
                  default: 'STRING_EQ',
                  available: ['STRING_EQ', 'STRING_NEQ', 'STRING_LIKE', 'IN', 'NIN'],
                  name: 'string',
                },
                metadata: {
                  filterable: true,
                  required: false,
                  selectedByDefault: true,
                  sortable: true,
                  hidden: false,
                  name: 'u2_core.status',
                },
                choices: {
                  open: 'open',
                  'in progress': 'in progress',
                  'in review': 'in review',
                  'done this is done this is done this is done this is done this is done this is done this is done this':
                    'done this is done this is done this is done this is done this is done this is done this is done this',
                },
              },
              {
                uniqueName: 'Name',
                readableName: 'Name',
                description: '',
                type: {
                  default: 'STRING_EQ',
                  available: ['STRING_EQ', 'STRING_NEQ', 'STRING_LIKE', 'IN', 'NIN'],
                  name: 'string',
                },
                metadata: {
                  filterable: true,
                  required: false,
                  selectedByDefault: true,
                  sortable: true,
                  hidden: false,
                  name: 'u2_tpm.name',
                },
                choices: [],
              },
            ],
            columns: [
              {
                id: 'Id',
                label: 'u2_core.id',
                name: 'u2_core.id',
                required: true,
                description: null,
                filterable: true,
                sortable: true,
                type: 'id',
                hidden: false,
                selectedByDefault: true,
                vars: [],
              },
              {
                id: 'Status',
                label: 'u2_core.status',
                name: 'u2_core.status',
                required: false,
                description: null,
                filterable: true,
                sortable: true,
                type: 'workflow_status',
                hidden: false,
                selectedByDefault: true,
                vars: [],
              },
              {
                id: 'Name',
                label: 'u2_tpm.name',
                name: 'u2_tpm.name',
                required: false,
                description: null,
                filterable: true,
                sortable: true,
                type: 'name',
                hidden: false,
                selectedByDefault: true,
                vars: [],
              },
            ],
            config: {
              hasData: true,
              pagination: {
                previous: 1,
                current: 2,
                next: 3,
                first: 0,
                last: 4,
                first_item: 21,
                last_item: 30,
                total_items: 41,
                pages: 5,
                pages_in_range: [0, 1, 2, 3, 4],
                page_range: 5,
                count: 10,
              },
              paginationEnabled: true,
              selectionKey: 'Id',
              tableName: 'u2_transfer_pricing_transaction_table',
            },
            records: [
              {
                Id: 1,
                Status: { name: 'open', type: 'OPEN' },
                Name: 'Personalgestellung: Johnston and Sons',
              },
            ],
          },
        },
      },
      { status: StatusCodes.OK }
    )
  })
)

const userResource = createUser({
  '@context': '/api/contexts/User',
  '@id': '/api/users/1',
  '@type': 'User',
  id: 1,
  locked: false,
  username: 'user1',
  lastLogin: '2021-10-22T16:14:48+00:00',
  lastActivity: null,
  userRoles: ['ROLE_USER'],
  roles: ['ROLE_USER'],
  groups: [],
})

describe('TaskList', () => {
  beforeEach(() => {
    mockIntersectionObserver()
  })

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRoute).mockImplementation(() =>
      fromPartial<RouteLocationNormalizedLoaded>({
        fullPath: '/tam/transfer-pricing',
        name: 'MyName',
        query: {},
      })
    )
  })

  const taskListInfo = {
    '@id': '/api/.well-known/genid/xxxxxxxx',
    canDelete: true,
    canCreate: true,
    canWrite: true,
    hasDocument: true,
    hasMultipleOptionsForNew: false,
    tableHasUql: true,
    itemPluralName: 'Important Task Type',
    newPath: 'newPath',
    optionsForNew: {},
    savedFilterInformation: {
      changed: true,
      savedFilter: {
        '@id': '/api/saved-filters/1',
        '@type': 'SavedFilter',
        id: 1,
        name: 'Saved Filter',
        owner: '/api/users/1',
        taskShortName: 'cm-contract',
        favourite: true,
        description: 'My Filter',
        uql: 'My Search',
        createdAt: '2020-01-01',
        updatedAt: '2020-01-01',
        updatedBy: undefined,
        createdBy: '/api/users/1',
        canDelete: true,
        canEdit: true,
        public: true,
      },
    },
    shortName: 'tam-transfer-pricing',
  } satisfies TaskListInfo

  it('renders TaskList page for documents', async () => {
    const user = userEvent.setup()
    const ui = render(wrapInSuspense(TaskList), {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'task-list-info': {
                taskListInfo: {
                  ...taskListInfo,
                },
              },
            },
          }),
        ],
      },
    })
    const authStore = useAuthStore()
    authStore.$patch({
      user: userResource,
    })

    await flushPromises()

    const meatball = await ui.findByLabelText('u2.open_menu')
    await user.click(meatball)

    expect(ui.getByRole('menuitem', { name: /u2_tpm.show_template_list/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /u2.edit_selected_records/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /u2.transition_selected_records/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /u2.delete_selected_records/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /u2.import.import/ })).toHaveAttribute(
      'aria-disabled',
      'true'
    )
    expect(ui.getByRole('menuitem', { name: /CSV/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /JSON/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /Excel/ })).not.toBeDisabled()

    expect(ui.getByRole('link', { name: /u2.saved_filters/ })).toBeInTheDocument()
    expect(ui.getByRole('link', { name: /u2.new/ })).toBeInTheDocument()

    expect(
      ui.getByRole('button', { name: 'u2.share_link_to_the_currently_filtered_table' })
    ).toBeInTheDocument()
    expect(await ui.findByRole('table')).toBeInTheDocument()
  })

  it('renders TaskList page for non documents without multiple options for new', async () => {
    const ui = render(wrapInSuspense(TaskList), {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'task-list-info': { taskListInfo },
            },
          }),
        ],
      },
    })

    await flushPromises()

    const meatball = await ui.queryByLabelText('u2.open_menu')
    invariant(meatball, 'meatball is null')
    await fireEvent.click(meatball)

    expect(ui.getByRole('menuitem', { name: /u2.edit/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /u2.transition/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /u2.delete/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /u2.import.import/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /CSV/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /JSON/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /Excel/ })).not.toBeDisabled()

    expect(ui.getByRole('link', { name: /u2.new/ })).toBeInTheDocument()
    expect(
      ui.getByRole('button', { name: 'u2.share_link_to_the_currently_filtered_table' })
    ).toBeInTheDocument()
    expect(await ui.findByRole('table')).toBeInTheDocument()
  })

  it('renders TaskList page for non documents with multiple options for new', async () => {
    const ui = render(wrapInSuspense(TaskList), {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'task-list-info': {
                taskListInfo: {
                  ...taskListInfo,
                  hasDocument: false,
                  documentTemplateListPath: undefined,
                  hasMultipleOptionsForNew: true,
                },
              },
            },
          }),
        ],
      },
    })

    await flushPromises()

    const meatball = await ui.findByLabelText('u2.open_menu')
    await fireEvent.click(meatball)

    expect(ui.getByRole('menuitem', { name: /u2.edit/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /u2.transition/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /u2.delete/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /u2.import.import/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /CSV/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /JSON/ })).not.toBeDisabled()
    expect(ui.getByRole('menuitem', { name: /Excel/ })).not.toBeDisabled()

    expect(ui.getByRole('link', { name: /u2.saved_filters/ })).toBeInTheDocument()
    expect(ui.getByRole('button', { name: /u2.new/ })).toBeInTheDocument()

    expect(
      ui.getByRole('button', { name: 'u2.share_link_to_the_currently_filtered_table' })
    ).toBeInTheDocument()
    expect(await ui.findByRole('table')).toBeInTheDocument()
  })

  it('shows and hides bulk delete dialog', async () => {
    const user = userEvent.setup()
    const ui = render(wrapInSuspense(TaskList), {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'task-list-info': { taskListInfo },
            },
          }),
        ],
        stubs: {
          PageHeader: { template: '<div><slot name="title"/><slot/></div>' },
          SavedFilterControl: true,
        },
      },
    })
    const taskListStore = useTaskListStore()
    taskListStore.$patch({
      selectedRecordIds: [1, 2],
    })
    await flushPromises()

    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()

    const meatball = await ui.findByLabelText('u2.open_menu')
    await user.click(meatball)

    await user.click(ui.getByText(/u2.delete/))

    await user.click(within(ui.getByRole('dialog')).getByText('u2.delete'))

    // Then
    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()
  })

  it('shows a warning when no records are selected for bulk deletion', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(wrapInSuspense(TaskList), {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'task-list-info': { taskListInfo },
            },
          }),
        ],
        stubs: {
          PageHeader: { template: '<div><slot name="title"/><slot/></div>' },
          SavedFilterControl: true,
        },
      },
    })

    await flushPromises()

    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()
    // When
    const meatball = await ui.findByLabelText('u2.open_menu')
    await user.click(meatball)

    await user.click(ui.getByRole('menuitem', { name: /u2.delete/ }))

    // Then
    expect(useNotificationsStore().addWarning).toHaveBeenCalledWith('u2.no_records_selected_delete')
    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()
  })

  it('shows and hides bulk transition dialog', async () => {
    const user = userEvent.setup()
    const ui = render(wrapInSuspense(TaskList), {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'task-list-info': { taskListInfo },
            },
          }),
        ],
        stubs: {
          PageHeader: { template: '<div><slot name="title"/><slot/></div>' },
          SavedFilterControl: true,
        },
      },
    })
    const taskListStore = useTaskListStore()
    taskListStore.$patch({
      selectedRecordIds: [1, 2],
    })
    await flushPromises()

    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()

    const meatball = await ui.findByLabelText('u2.open_menu')
    await user.click(meatball)

    await user.click(ui.getByText(/u2.transition/))

    await user.click(within(ui.getByRole('dialog')).getByText('u2.transition'))

    // When
    await user.click(ui.getByRole('button', { name: /cross/ }))

    // Then
    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()
  })

  it('shows a warning when no records are selected for bulk transition', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(wrapInSuspense(TaskList), {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'task-list-info': { taskListInfo },
            },
          }),
        ],
        stubs: {
          PageHeader: { template: '<div><slot name="title"/><slot/></div>' },
          SavedFilterControl: true,
        },
      },
    })

    await flushPromises()

    // When
    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()

    const meatball = await ui.findByLabelText('u2.open_menu')
    await user.click(meatball)

    await user.click(ui.getByRole('menuitem', { name: /u2.transition/ }))
    // Then
    expect(useNotificationsStore().addWarning).toHaveBeenCalledWith(
      'u2.no_records_selected_transition'
    )
    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()
  })

  it('shows a warning when no records are selected for bulk edit', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(wrapInSuspense(TaskList), {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'task-list-info': {
                taskListInfo: { ...taskListInfo, canWrite: true },
              },
            },
          }),
        ],
        stubs: {
          PageHeader: { template: '<div><slot name="title"/><slot/></div>' },
          SavedFilterControl: true,
        },
      },
    })

    await flushPromises()

    // When
    const meatball = await ui.findByLabelText('u2.open_menu')
    await user.click(meatball)

    await user.click(ui.getByRole('menuitem', { name: /u2.edit/ }))

    // Then
    expect(useNotificationsStore().addWarning).toHaveBeenCalledWith('u2.no_records_selected_edit')
  })
})
