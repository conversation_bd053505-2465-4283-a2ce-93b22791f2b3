import axios from 'axios'
import Cookie from '@js/utilities/cookie'
import { createJwt } from '@tests/__factories__/createJwt'
import { createPinia, setActivePinia } from 'pinia'
import { createUser } from '@tests/__factories__/createUser'
import { createUserSettings } from '@tests/__factories__/createUserSettings'
import { flushPromises } from '@vue/test-utils'
import { HttpResponse, http } from 'msw'
import { setServerHandlers } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import { useAuthStore } from '@js/stores/auth'

const user1 = createUser()

const newToken = createJwt({ id: user1.id })
setServerHandlers(
  http.get('/api/logout', async () => {
    return HttpResponse.json({}, { status: StatusCodes.OK })
  }),
  http.get('/legacy/refresh-jwt', async () => {
    return HttpResponse.json({ token: newToken }, { status: StatusCodes.OK })
  }),
  http.get(user1['@id'], async () => {
    return HttpResponse.json(user1, { status: StatusCodes.OK })
  }),
  http.get(`${user1['@id']}/settings`, async () => {
    return HttpResponse.json(createUserSettings(), { status: StatusCodes.OK })
  })
)

describe('Auth Store Module', () => {
  beforeAll(() => {
    vi.clearAllMocks()
  })

  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('clears the token and the user', async () => {
    // Given
    const authStore = useAuthStore()
    Cookie.write('U2SESSION', 'something', 1)
    Cookie.write('REMEMBERME', 'something', 1)
    authStore.$patch({
      token: createJwt({ id: user1.id }),
      user: user1,
    })

    // When
    await authStore.logout()

    // Then
    expect(Cookie.read('U2SESSION')).toBeNull()
    expect(Cookie.read('REMEMBERME')).toBeNull()
    expect(authStore.$state).toStrictEqual({
      requiresTwoFactor: false,
      user: undefined,
      token: undefined,
    })
  })

  it('refreshes the jwt and fetches the current user', async () => {
    // Given
    const authStore = useAuthStore()
    const oldJwt = createJwt({ id: user1.id, exp: 0 })
    authStore.$patch({
      token: oldJwt,
      user: user1,
    })

    expect(authStore.token).toBe(oldJwt)

    // When
    await authStore.ensureToken()

    // Then
    expect(authStore.token).toBe(newToken)
    expect(authStore.user).toStrictEqual(user1)
  })

  it('handles concurrent jwt refreshes', async () => {
    // Given
    vi.useFakeTimers()

    const token = createJwt({
      id: user1.id,
      exp: new Date(Date.now() - 50000).getTime() / 1000, // expired
      roles: [],
    })

    const authStore = useAuthStore()
    authStore.$patch({ token: token })

    const axiosSpy = vi.spyOn(axios, 'get')
    const spyRequest = vi.fn()
    const makeRequest = async () => {
      await authStore.ensureToken()
      spyRequest()
    }
    // When
    await Promise.all([makeRequest(), makeRequest(), makeRequest()])

    await flushPromises()
    // Then
    expect(spyRequest).toHaveBeenCalledTimes(3)
    expect(axiosSpy).toHaveBeenNthCalledWith(1, '/legacy/refresh-jwt')
    expect(axiosSpy).not.toHaveBeenNthCalledWith(2, '/legacy/refresh-jwt')
  })

  it('knows if user has a role', async () => {
    // Given
    const authStore = useAuthStore()
    authStore.$patch({
      user: { ...user1, roles: ['ROLE_ADMIN'] },
    })

    // Then
    expect(authStore.hasRole('ROLE_ADMIN')).toBe(true)
    expect(authStore.hasRole('ROLE_USER_GROUP_ADMIN')).toBe(false)
  })

  it('knows if user has a authorization', async () => {
    // Given
    const authStore = useAuthStore()
    authStore.$patch({
      user: { id: user1.id, roles: ['ROLE_UNIT_MANAGER'] },
    })

    // Then
    expect(authStore.hasRole('ROLE_UNIT_MANAGER')).toBe(true)
    expect(authStore.hasRole('ROLE_PERIOD_MANAGER')).toBe(false)
  })

  it('reveals if the token is valid', async () => {
    // Given
    vi.useFakeTimers()

    const token = createJwt({
      id: user1.id,
      exp: new Date(Date.now() + 5000).getTime() / 1000,
      roles: [],
    })

    const authStore = useAuthStore()
    authStore.$patch({ token: token })

    // Then
    expect(authStore.isTokenValid()).toBe(true)

    // When
    vi.advanceTimersByTime(4000)
    // Then
    expect(authStore.isTokenValid()).toBe(true)

    // When
    vi.advanceTimersByTime(1000)
    // Then
    expect(authStore.isTokenValid()).toBe(false)

    vi.clearAllTimers()
  })

  it('reveals if the token is needs refreshing valid', async () => {
    // Given
    vi.useFakeTimers()

    const token = createJwt({
      id: user1.id,
      exp: new Date(Date.now() + 20000).getTime() / 1000,
      roles: [],
    })

    const authStore = useAuthStore()
    authStore.$patch({ token: token })

    // Then
    expect(authStore.tokenNeedsRefresh()).toBe(false)

    // When
    vi.advanceTimersByTime(15000)
    // Then
    expect(authStore.tokenNeedsRefresh()).toBe(false)

    // When
    vi.advanceTimersByTime(1000)
    // Then
    expect(authStore.tokenNeedsRefresh()).toBe(true)

    vi.clearAllTimers()
  })
})
