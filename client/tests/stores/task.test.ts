import { create<PERSON><PERSON>, setActive<PERSON><PERSON> } from 'pinia'
import axios from 'axios'
import { fetchStates } from '@js/types'
import { useTaskStore } from '@js/stores/task'
import type { Task } from '@js/model/task'
import type { CheckState } from '@js/model/checkstate'

vi.mock('axios')

const task1 = {
  '@context': 'string',
  '@id': 'task-iri-1',
  '@type': 'Task',
  checkStates: [],
  id: 'taskId',
  assignee: undefined,
  reviews: [],
  status: 'status-iri',
  taskType: 'tam_tax_relevant_restriction',
} satisfies Partial<Task>

const checkState1 = {
  '@context': 'string',
  '@id': 'check-state-id-1',
  '@type': 'string',
  id: 'check-state-uuid-1',
  createdBy: 'check-state-created-by-iri-1',
  check: 'check-state-check-iri-1',
  checked: true,
  createdAt: 'string',
} as CheckState
const checkState2 = {
  '@context': 'string',
  '@id': 'check-state-id-2',
  '@type': 'string',
  id: 'check-state-uuid-2',
  createdBy: 'check-state-created-by-iri-2',
  check: 'check-state-check-iri-2',
  checked: false,
  createdAt: 'string',
} as CheckState

describe('Task Store', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    setActivePinia(createPinia())
  })

  it('has an initial state', () => {
    const taskStore = useTaskStore()
    expect(taskStore.$state).toEqual({
      checkLogsFetchState: fetchStates.idle,
      currentCheckStates: [],
      task: undefined,
    })
  })

  it('fetches task by id', async () => {
    // Given
    vi.mocked(axios.get).mockResolvedValue({ data: task1 })
    const taskStore = useTaskStore()

    // When
    await taskStore.fetchTaskById(task1.id)

    // Then
    expect(taskStore.task).toEqual(task1)
    expect(vi.mocked(axios.get)).toHaveBeenCalledWith('/api/tasks/taskId')
    expect(vi.mocked(axios.get)).toHaveBeenCalledWith('/api/tasks/taskId/check-states', {
      params: { current: true, pagination: false },
    })
    expect(vi.mocked(axios.get)).toHaveBeenCalledWith('status-iri')
  })

  it('fetches current check states of the task', async () => {
    // Given
    vi.mocked(axios.get).mockResolvedValue({ data: { 'hydra:member': [checkState1, checkState2] } })
    const taskStore = useTaskStore()

    // When
    await taskStore.fetchCurrentCheckStates('taskId')

    // Then
    expect(axios.get).toHaveBeenCalledWith('/api/tasks/taskId/check-states', {
      params: {
        current: true,
        pagination: false,
      },
    })
    expect(taskStore.currentCheckStates).toEqual([checkState1, checkState2])
  })

  it('refreshes a task', async () => {
    // Given
    vi.mocked(axios.get).mockResolvedValue({ data: task1 })
    const taskStore = useTaskStore()
    taskStore.$patch({
      task: task1,
    })

    // When
    await taskStore.refresh()

    expect(vi.mocked(axios.get)).toHaveBeenCalledWith('/api/tasks/taskId')
  })

  it('updates check states', async () => {
    // Given
    const newCheckState = { ...checkState1, checked: false }
    vi.mocked(axios.post).mockResolvedValue({ data: { ...newCheckState, updatedAt: 'now' } })
    const taskStore = useTaskStore()
    taskStore.$patch({
      checkLogsFetchState: fetchStates.idle,
      currentCheckStates: [checkState1, checkState2],
      task: task1,
    })

    // When
    await taskStore.changeCheckState(newCheckState)

    // Then
    expect(vi.mocked(axios.post)).toHaveBeenCalledWith('/api/task-check-states', {
      ...checkState1,
      checked: false,
    })
    expect(taskStore.currentCheckStates).toEqual([
      { ...checkState1, checked: false, updatedAt: 'now' },
      checkState2,
    ])
  })
})
