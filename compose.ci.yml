services:
  web:
    image: universalunits/u2-nginx:${GIT_COMMIT_SHORT}
    networks:
      default:
        aliases:
          - test.u2.web
    depends_on:
      - php

  php:
    image: universalunits/u2-php-dev:${GIT_COMMIT_SHORT}
    depends_on:
      - redis
      - db
    environment:
      - APP_ENV
      - AWS_S3_BUCKET=dev-bucket
      - AWS_S3_ENDPOINT=http://minio:9000
      - AWS_S3_KEY=minioadmin
      - AWS_S3_SECRET=minioadmin
      - AWS_S3_USE_PATH_STYLE_ENDPOINT=1
    volumes:
      - type: volume
        source: behat_fixtures
        target: /app/api/tests/behat/fixtures
      - type: volume
        source: logs
        target: /app/api/var/logs

  php_consumer:
    image: universalunits/u2-php-dev:${GIT_COMMIT_SHORT}
    command: supervisord -c /etc/supervisord.conf
    depends_on:
      - redis
      - db
    environment:
      - APP_ENV
      - AWS_S3_BUCKET=dev-bucket
      - AWS_S3_ENDPOINT=http://minio:9000
      - AWS_S3_KEY=minioadmin
      - AWS_S3_SECRET=minioadmin
      - AWS_S3_USE_PATH_STYLE_ENDPOINT=1
    volumes:
      - type: volume
        source: logs
        target: /app/api/var/logs

  db:
    image: mysql:8.0.36
    command: --sql_mode=0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: u2_test
      MYSQL_USER: u2
      MYSQL_PASSWORD: u2

  redis:
    image: redis:6.2

  selenium:
    image: selenium/standalone-chromium:136.0
    #image: selenium/standalone-firefox:latest
    #shm_size: 2gb
    #mem_reservation: 2gb
    volumes:
      - type: volume
        source: behat_fixtures
        target: /app/api/tests/behat/fixtures
        read_only: true
    environment:
      - START_XVFB=false
      - SE_START_XVFB=false

  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    ports:
      - "9000:9000"  # MinIO API
      - "9001:9001"  # MinIO Console
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
      MINIO_DOMAIN: minio
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      start_period: 10s
      start_interval: 1s
      timeout: 20s
      retries: 10
      interval: 1m30s

  minio-init:
    image: minio/mc:latest
    depends_on:
      minio:
        condition: service_healthy
    entrypoint: >
      /bin/sh -c "
      mc alias set minio http://minio:9000 minioadmin minioadmin;
      mc mb minio/dev-bucket --ignore-existing;
      mc anonymous set public minio/dev-bucket;
      exit 0;
      "

volumes:
  behat_fixtures:
  logs:
    name: $LOGS_VOLUME
    external: true
