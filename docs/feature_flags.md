# Feature Flags

Universal Units App uses feature flags to control functionality that is experimental or not publicly 
accessible. We use the [Flagception](https://github.com/bestit/flagception-bundle) bundle to configure 
the flags via the [flagception.yaml](../config/packages/flagception.yaml) file. See their 
[docs](https://github.com/bestit/flagception-bundle) for the various usage and options available. 

## Feature flags in vue

The feature flags used in vue are currently transported via the JWT cookie that is received on login. They get extracted and stored in a cookie named 
`features`. These features are as long valid as the JWT is valid. Enabling a feature flag for the usage in vue after login requires therefore to delete the 
current JWT cookie.

## Autowiring the Feature Flag Manager
The [FeatureFlagTrait](../src/FeatureFlags/FeatureFlagsTrait.php) can be used to inject the feature flag manager 
using autowiring. This allows us to use feature flags in classes without changing the class 
interface. Simply add `use FeatureFlagsTrait;` to your class, and you will be able to get access to feature flags:

```php
<?php

declare(strict_types=1);
namespace U2;

use U2\FeatureFlags\FeatureFlagsTrait;

class MyClass
{
    use FeatureFlagsTrait;

    public function myMethod(): bool
    {
        return $this->features->isActive('my_feature');
    }   
}
```
